{"@@locale": "fr", "gameTitle": "Moteur de jeu vidéo interactif", "settings": "Installer", "allSettings": "Tous les paramètres", "credits": "Liste des crédits", "creditsCreativeTitle": "Créativité et planification", "creditsGameProducer": "Producteur de jeux", "creditsCreativeSource": "Sources d’idées", "creditsSoftwarePlanner": "Planification logicielle", "creditsGameplayPlanner": "Planification du jeu", "creditsProgrammingTitle": "Élaboration de programmes", "creditsSoftwareArchitect": "Architecture logicielle", "creditsCodeWriter": "Rédaction de code", "creditsVisualTitle": "Conception visuelle", "creditsArtDesigner": "Designer artistique", "creditsAnimationDesigner": "Concepteur d’animation", "creditsMarketingTitle": "Publicité du marché", "creditsVideoProducer": "Production vidéo", "creditsCopywriter": "Rédaction", "creditsThanksTitle": "Merci d’avoir joué à mon jeu !", "creditsSpecialThanks": "Un grand merci à vous", "creditsSubtitle": "Voir les équipes et les contributeurs impliqués dans le développement logiciel", "openGameInNewWindow": "<PERSON><PERSON><PERSON><PERSON><PERSON> le projet de jeu dans une nouvelle fenêtre", "openGameInNewWindowDesc": "Lorsqu’il est activé, le projet de jeu s’ouvre dans une nouvelle fenêtre, et lorsqu’il est désactivé, il s’ouvre dans la fenêtre actuelle", "publicMode": "Mode lieu public/Mode Live", "publicModeDesc": "Lorsqu’elle est activée, l’interface de l’atelier impose l’utilisation des balises de tout le monde et n’affiche pas les balises qui ne sont pas liées à l’espace de travail", "verticalLayoutDescription": "Interface de montage vidéo verticale (recommandée pour les écrans haute résolution)", "language": "Paramètres de langue", "fullscreen": "Mode plein écran", "mainWindowFullscreen": "La fenêtre principale est en plein écran", "createNewGame": "<PERSON><PERSON><PERSON> un jeu", "workshop": "Atelier", "openGame": "<PERSON><PERSON><PERSON><PERSON> le jeu", "home": "page d’accueil", "activity": "dynamique", "profile": "<PERSON><PERSON><PERSON>", "downloads": "Télécharger", "friends": "<PERSON><PERSON>", "messages": "Message", "elementParams": "Paramètres d’élément", "backgroundColor": "Couleur d’arrière-plan", "thickness": "épaisseur", "borderColor": "<PERSON><PERSON><PERSON> de la bordure", "elevation": "hauteur", "videoPlayback": "Lecture vidéo", "playbackControl": "Commandes de lecture", "play": "<PERSON><PERSON>", "pause": "Te<PERSON> d<PERSON>", "stop": "<PERSON><PERSON><PERSON><PERSON>", "playbackRate": "<PERSON><PERSON>", "videoCover": "Couverture vidéo", "captureCurrentFrame": "Capture l’image actuelle", "timeEdit": "Édition de l’heure", "startTime": "Commence<PERSON>", "endTime": "<PERSON><PERSON><PERSON>", "setCurrentTime": "<PERSON><PERSON><PERSON> l’heure actuelle", "addVideo": "Ajouter une vidéo", "addDiamond": "Ajouter un diamant", "addRectangle": "Ajouter un rectangle", "addResizableRectangle": "Ajouter un rectangle variable", "addOval": "Ajouter une ellipse", "addParallelogram": "Ajouter un parallélogramme", "addHexagon": "Ajouter un hexagone", "addStorage": "Ajouter de l’espace de stockage", "addImage": "Ajouter une image", "removeAll": "Supp<PERSON>er tout", "saveFlowchart": "Enregistrer l’organigramme", "loadFlowchart": "Lire l’organigramme", "workshopItems": "Projet d’atelier", "loadingFailed": "Echec du chargement", "retryLoading": "Cliquez sur Réessayer", "openGameTitle": "<PERSON><PERSON><PERSON><PERSON> le jeu", "showController": "Afficher la barre de contrôle", "hideController": "Masquer la barre des commandes", "myGames": "Mon jeu", "popularGames": "Jeux populaires", "news": "Nouvelles", "recentlyPlayed": "<PERSON><PERSON>", "recentlyEdited": "Récemment modifié", "achievementDisplay": "Vitrine des résultats", "achievements": "accomplissement", "screenshots": "Capture d’écran", "player": "joueur", "games": "<PERSON><PERSON>", "search": "<PERSON><PERSON><PERSON>", "modifyTimeAndCover": "Modifier l’heure avec la couverture", "delete": "<PERSON><PERSON><PERSON><PERSON>", "removeAllConnections": "<PERSON><PERSON><PERSON> tous les raccords", "toggleConnectable": "La bascule peut être connectée", "toggleResizable": "Basculer pour redimensionner", "segmented": "Sous-niveau", "curved": "courbe", "rectangular": "rectangle", "startPoint": "point de départ", "loadingFailedRetry": "Si le chargement échoue, cliquez sur Réessayer", "flowchartMissingStart": "Il manque un élément de point de départ à l’organigramme", "videoTimeAndCover": "Temps et couverture de la vidéo", "coverLoadFailed": "Echec du chargement de la page couverture", "close": "<PERSON><PERSON><PERSON><PERSON>", "confirm": "Confirmer", "playPause": "Lecture/Pause", "invalidStartTime": "L’heure de début n’est pas valide", "invalidEndTime": "L’heure de fin n’est pas valide", "playbackStatus": "État de <PERSON>", "currentPosition": "Vous ê<PERSON> ici", "volume": "Niveau de volume", "completed": "Que ce soit fait ou pas", "duration": "Durée totale", "rate": "<PERSON><PERSON>", "playbackProgress": "Progression de la lecture", "volumeControl": "Contrôle du volume", "audioTrack": "<PERSON><PERSON>", "subtitle": "légende", "noSubtitle": "Pas de sous-titres", "closeSubtitle": "Désactiver les sous-titres", "modifyChapterTitle": "Modifier le titre de la section", "setBranchParams": "Définir le type de branche et les paramètres", "noAudioTrack": "Pas de pistes audio", "enterProjectName": "Entrez un nom pour le projet", "projectNameHint": "Veuillez entrer un nom de projet", "cancel": "Annuler", "projectCreated": "Le projet a été créé", "fileNotFound": "Le fichier n’existe pas", "upload": "télécharger", "uploadWorkshop": "Télécharger l’atelier", "importImage": "Importer des images", "removeImage": "Supprimer l’image", "previewImageDefault": "faire défaut", "visibility": "Visibilité", "uploadNow": "Téléchargez-le maintenant", "uploading": "Téléchargement", "uploadFailed": "Échec du téléchargement", "uploadSuccess": "Le téléchargement est réussi", "download": "Télécharger", "downloadWorkshop": "Télécharger l’atelier", "downloading": "Téléchargement", "downloadFailed": "Le téléchargement a échoué", "downloadSuccess": "Le téléchargement a été un succès", "invalidFileType": "Type de fichier non valide", "unsupportedFileFormat": "Formats de fichiers non pris en charge, le Workshop n’autorise que les fichiers vidéo, jpg et json", "noValidFilesFound": "Aucun fichier de téléchargement valide n’a été trouvé, assurez-vous que votre projet n’inclut que les types de fichiers pris en charge", "SearchWorkshop": "Cherchez-le dans l’atelier", "style": "Style d’image", "AncientChinese": "Style chinois ancien", "shape": "Formes d’images", "ilpEditor": "<PERSON><PERSON><PERSON> de jeux", "steamLimitedAccount": "Comptes restreints Steam", "sort": "trier", "joinDiscord": "Rejo<PERSON>ez le Discord pour des discussions sur le gameplay", "back": "rendre", "no": "Non, vous ne le faites pas", "resume": "Revenez en arrière et continuez à jouer", "retry": "<PERSON><PERSON><PERSON><PERSON>", "seed": "Graines", "about": "concernant", "exit": "<PERSON><PERSON><PERSON> le jeu", "confirmExitApp": "Êtes-vous sûr de vouloir quitter l’application ?", "challenge": "<PERSON><PERSON><PERSON>", "gallery": "Galerie", "steamChallenge": "Lancez-vous dans le défi", "steamGallery": "Galerie du Steam Workshop", "Subscribe": "s’inscrire", "SubscribeAndDownload": "Abonnez-vous et téléchargez", "clickToSubscribeAndDownload": "Cliquez sur S’abonner pour lancer le téléchargement", "createChallenge": "<PERSON><PERSON><PERSON> un défi", "challengeName": "Le nom du défi", "ilpDesc": "description", "Everyone": "Tous", "NSFW": "Lieux non publics", "public": "Public", "friendsonly": "Visible par les amis", "private": "Priv<PERSON>", "Anime": "Style anime", "Realistic": "Style réaliste", "Pixel": "Style de pixel", "Ancient Chinese": "Style chinois ancien", "Other": "Autres styles", "Landscape": "Rectangle transversal", "Portrait": "Rectangle longitudinal", "Square": "car<PERSON>", "puzzleHint": "Une mauvaise réponse a été réduite", "steamWorkshop": "Atelier vapeur", "steamAuthorOtherFiles": "Autres documents de l’auteur", "publishTime": "<PERSON><PERSON><PERSON><PERSON>", "updateTime": "Actualisé", "ageRating": "Tranches d’âge", "adultAgreementTitle": "Veuillez vous assurer que vous avez au moins 18 ans", "adultAgreementContent": "Le Steam Workshop contient du contenu co-créé par des joueurs du monde entier, qui peut impliquer du contenu qui n’est pas approprié pour être visionné dans des lieux publics.", "Unsubscribed": "Non abonné", "itemNotSubscribed": "Les articles qui ne sont pas abonnés seront automatiquement téléchargés après l’abonnement", "subscribeSuccess": "L’abonnement a réussi, commencez à télécharger...", "subscribeFailed": "Échec de l’abonnement : ", "downloadingPleaseWait": "Téléchargement, s’il vous plaît plus tard", "Subscribed": "Sous<PERSON>rit", "sortBySubscribers": "Par nombre d’abonnements", "sortByVote": "Par le nombre de likes", "sortByFavorites": "Par le nombre de favoris", "sortByPublishDate": "<PERSON><PERSON><PERSON><PERSON>", "sortByUpdateDate": "Actualisé", "sortBy": "Trier par", "toggleSortDirection": "Bascule le sens de tri", "autoFullScreenVideo": "Plein écran automatique lors de la lecture d’une vidéo", "newWindowFullScreen": "La nouvelle fenêtre s’affiche en plein écran", "newWindowFullScreenDescription": "Réglage automatique en mode plein écran lorsqu’une nouvelle fenêtre s’ouvre", "filePreparingPleaseRetry": "Le fichier est en cours de préparation, veuillez réessayer plus tard", "remoteReservedWord": "remote est réservé au programme, veuillez changer le nom du projet", "workshopRules": "Règlement de l’atelier", "workshopRulesDescription": "Avant de soumettre une vidéo interactive au Workshop, assurez-vous qu’elle n’enfreint pas les Conditions d’utilisation de Steam, sinon la vidéo interactive sera supprimée :", "workshopRulesSpecial": "En particulier, les vidéos interactives, les aperçus et les descriptions doivent respecter les règles suivantes :", "workshopRuleNoAdult": "Pas de pornographie ou de nudité photographique ou authentique", "workshopRuleNoOffensive": "Pas d’offensant ou de violent, gore", "workshopRuleNoCopyright": "Pas de violation du droit d’auteur", "workshopRuleNoAds": "Pas de publicité", "workshopRuleNoMisleading": "Il n’y a pas de prévisualisations trompeuses", "archive": "archiver", "flowchart": "Examiner le processus", "startGame": "<PERSON><PERSON> au jeu", "viewFlowchart": "Voir l’organigramme", "nodeDetails": "<PERSON><PERSON><PERSON> du nœud", "elementId": "ID", "elementType": "type", "elementText": "SMS", "startNode": "<PERSON><PERSON><PERSON>", "videoNode": "Nœuds vidéo", "rectangleNode": "Nœuds rectangulaires", "diamondNode": "Nœuds en diamant", "storageNode": "Nœuds de stockage", "ovalNode": "Nœuds elliptiques", "parallelogramNode": "Nœuds de parallélogramme", "hexagonNode": "<PERSON><PERSON><PERSON> hexa<PERSON>", "imageNode": "Nœuds d’image", "unknownNodeType": "Type inconnu", "flowchartFileNotFound": "FLOWCHART.json fichier n’a pas pu être retrouvé", "flowchartLoadError": "Erreur de chargement de l’organigramme : {error}", "createTime": "Heure de création", "lastUpdate": "Dernière mise à jour", "watchedNodesCount": "Le nombre de nœuds surveillés", "startElementIdNotFound": "Impossible d’obtenir l’ID de l’élément de départ du projet", "archiveFileNotFound": "Le fichier d’archive n’existe pas", "loadArchiveError": "Erreur de chargement de l’archive : {error}", "noArchivesFound": "Aucune archive trouvée", "settingsSaved": "Les paramètres sont enregistrés", "saveSettings": "Enregistrer les paramètres", "generalSettings": "Paramètres généraux", "audioSettings": "Paramètres audio", "languageSettings": "Paramètres de langue", "autoSaveProgress": "Enregistrez automatiquement votre progression", "autoSaveDescription": "Sauvegardez automatiquement votre progression dans le jeu", "showVideoControls": "Afficher la barre de contrôle vidéo", "showVideoControlsDescription": "Affichage de la barre des commandes pendant la lecture de la vidéo", "interfaceLanguage": "Langue de l’interface", "version": "Version", "developer": "Développeurs", "openGameError": "Erreur lors de l’ouverture du projet de jeu : {error}", "alreadySubscribed": "Sous<PERSON>rit", "videoNodeLocked": "Ce nœud vidéo n’a pas été déverrouillé, veuillez d’abord regarder la vidéo précédente", "likeSuccess": "Comme le succès", "alreadyLiked": "Il a déjà été aimé", "likeFailed": "N’a pas aimé", "favoriteSuccess": "<PERSON><PERSON><PERSON>", "alreadyFavorited": "<PERSON><PERSON><PERSON><PERSON> mis en signet", "favoriteFailed": "Échec du signet", "unsubscribe": "Résilier votre abonnement", "confirmUnsubscribe": "Êtes-vous sûr de vouloir résilier votre abonnement ?", "unsubscribeSuccess": "L’abonnement a été désabonné avec succès", "unsubscribeFailed": "Échec de l’annulation de l’abonnement", "title": "titre", "required": "Obligatoire", "description": "description", "inputDescription": "Entrez une description", "selectContentRating": "Veuillez sélectionner une classification de contenu", "previewImage": "Prévisualiser l’image", "publish": "publier", "titleCannotBeEmpty": "Le titre ne peut pas être vide", "flowChart": "organigramme", "pleaseSelectProject": "Veuillez d’abord sélectionner le projet", "fileNotFoundTitle": "Le fichier n’existe pas", "openFlowChartFailed": "Echec de l’ouverture de l’organigramme : $e", "invalidTimeFormat": "Le format de l’heure n’est pas valide", "startTimeBeforeEndTime": "L’heure de début doit être antérieure à l’heure de fin", "endTimeNotExceedTotal": "L’heure de fin ne peut pas dépasser la durée totale de la vidéo", "save": "<PERSON><PERSON><PERSON><PERSON>", "playTimeSettings": "Paramètres de début et de fin de la lecture", "startTimeLabel": "<PERSON><PERSON> d<PERSON>", "endTimeLabel": "Heure de fin", "hour": "<PERSON><PERSON>", "minute": "diviser", "second": "deuxième", "showVideoController": "Afficher la barre de contrôle vidéo", "showControllerDuringPlayback": "Affichage de la barre des commandes pendant la lecture de la vidéo", "playbackSpeed": "<PERSON><PERSON><PERSON>", "fileNotFoundWithPath": "Le fichier n’existe pas : $jsonFilePath", "loadArchive": "Lire l’archive", "viewFlowChart": "Voir l’organigramme", "switchHorizontal": "Passer à une disposition horizontale", "switchVertical": "Passer à une disposition verticale", "invalidProjectPath": "Le chemin d’accès au projet n’est pas valide", "gameWindow": "<PERSON><PERSON><PERSON> de <PERSON>eu", "projectLoaded": "Projet chargé : {path}", "projectNotFound": "Élément introuvable : {path}", "newProject": "Créer un nouveau projet", "projectExistsTitle": "Le projet existe déjà", "projectExistsContent": "Le projet existe déjà, va-t-il être dans le projet ?", "confirmUpdateWorkshopItem": "Objets de l’atelier mis à jour", "confirmUpdateWorkshopItemDescription": "V<PERSON> voudrez mettre à jour un objet de l’Atelier existant. Cela remplacera la version actuelle avec vos modifications.", "updating": "Actualisation...", "updateSuccess": "Mise à jour réussie !", "updateWorkshop": "Objets de l’atelier mis à jour", "noWorkshopItems": "Vous n’avez pas d’objets de l’Atelier à mettre à jour", "selectWorkshopItemToUpdate": "Sélectionnez l’élément de l’Atelier que vous souhaitez mettre à jour", "updateWorkshopError": "Erreur de mise à jour de l’atelier : {error}", "autoSaveGame": "Sauvegarde automatique des jeux", "autoSaveInterval": "Intervalles d’enregistrement automatique", "enableFlowchartCheck": "Activer la détection d’organigramme", "flowchartCheckDescription": "Détecte les nœuds non connectés et les connexions d’origine", "disconnectedVideoCount": "Nombre de nœuds avec des vidéos qui ne sont pas connectées : {count}", "startNodeVideoCount": "Le nombre de nœuds vidéo après le point de départ doit être de 1, current : {count}", "exitFullscreen": "<PERSON><PERSON><PERSON> le mode plein écran", "useNewWindowForEditing": "Une nouvelle fenêtre ouvre l’interface Modifier le projet", "useNewWindowForEditingDescription": "Lorsqu’il est activé, l’éditeur de projet s’ouvre dans une nouvelle fenêtre, et lorsqu’il est désactivé, il s’ouvre dans la fenêtre actuelle", "workshopItemUpdated": "Le projet Workshop a été mis à jour", "workshopItemUploaded": "Le projet Workshop a été téléchargé", "uploadFailedWithColon": "Echec du téléchargement : ", "congratsEarnedCoins": "Félicitations! Obtenez une récompense de 10 pièces d’or !", "preparingProjectFiles": "Préparation du dossier de projet...", "preparingConfig": "<PERSON><PERSON><PERSON><PERSON> la <PERSON>", "uploadingPleaseWait": "En cours de téléchargement, veuil<PERSON>z patienter...", "uploadingContent": "Télécharger du contenu", "uploadingPreviewImage": "Télécharger un aperçu", "committingChanges": "Validez vos modifications", "diskSpaceInsufficient": "Espace disque insuffisant, basé sur le mécanisme de téléchargement Steam, vous devez réserver suffisamment d’espace sur le disque où se trouve le logiciel. Libérez l’espace disque et réessayez de télécharger.", "noBranchesToSet": "Le nœud actuel n’a pas plusieurs branches et les paramètres de branche ne peuvent pas être définis", "normalBranch": "Ramification régulière", "timedBranch": "Succursales limitées dans le temps", "qteBranch": "Branche QTE", "timeLimit": "<PERSON><PERSON> de te<PERSON> (secondes)", "autoSelect": "Sélection automatique", "questionDescription": "Description du problème", "buttonOpacity": "Transparence des boutons", "transparent": "transparent", "opaque": "opaque", "buttonText": "Texte du bouton", "branchIndexLabel": "Branche {index} :", "customButtonPosition": "Personnaliser la position du bouton", "screenPreview": "Aperçu de l’écran", "remainingTimeLabel": "Temps restant : {time} secondes", "buttonPositionInfo": "Position horizontale : {horizontalPercent} %, Position verticale : {verticalPercent} %", "titlePositionInfo": "Emplacement : HorizontalPercent} %, Vertical{verticalPercent} %", "saving": "Sauvegarder...", "branchSettingsSaved": "Les paramètres de branche et les organigrammes ont été enregistrés", "saveFlowchartFailed": "Échec de l’enregistrement de l’organigramme", "fullVideoPath": "Chemin d’accès complet à la vidéo : {path}", "videoFileNotExist": "Le fichier vidéo n’existe pas : {path}", "archiveUpdatedForNode": "Mise à jour de l’archive actuelle du nœud : {nodeId}", "nodeMarkedAsWatched": "<PERSON><PERSON><PERSON> marqué comme consulté : {nodeId}", "videoPlaybackError": "Erreur de lecture vidéo : {error}", "addGlobalValue": "Ajouter une valeur globale", "editGlobalValue": "Modifier les valeurs globales", "globalValueName": "Nom de la valeur numérique", "globalValueNameHint": "Par exemple : l’or, la santé, etc", "initialValue": "Valeur initiale", "valueInputHint": "Entrez une valeur initiale", "pleaseEnterValueName": "Entrez un nom numérique", "editGlobalValueTitle": "Modifier la valeur globale : {name}", "valueLabel": "valeur numérique", "globalValues": "Valeurs mondiales", "variableName": "Nom de la variable", "pleaseEnterVariableName": "Entrez un nom de variable", "variableAlreadyExists": "Le nom de la variable existe déjà", "numberType": "chiffre", "textType": "SMS", "booleanType": "Booléen", "variableType": "type", "addToFlowchart": "Ajouter à l’organigramme", "addVariable": "Ajouter des variables", "pleaseEnterValidNumber": "Veuillez saisir un nombre significatif", "pleaseEnterTrueOrFalse": "<PERSON><PERSON><PERSON> true ou false", "pleaseEnterValue": "Entrez une valeur numérique", "failedToGetWorkshopItems": "Impossible d’obtenir des objets de l’atelier", "workshopRecommendedTitle": "Guide pour sauver le monde", "workshopRecommendedDescription": "Écrit par le chef du village moteur lui-même, les villageois ont tous dit que ce manuel évoluera automatiquement avec la mise à jour de la version", "setOptionsAndValueChanges": "Définir les options et modifier les valeurs", "selectBranchToSet": "Sélectionnez la branche que vous souhaitez configurer", "branchWithText": "Branche : {branchText}", "noGlobalValuesFoundAddFirst": "Si vous ne trouvez pas de valeur globale, ajoutez-en d’abord une à la gestion de la valeur globale", "greaterThan": "Plus grand que", "lessThan": "<PERSON><PERSON> de", "equalTo": "quantité", "greaterThanOrEqual": "Su<PERSON><PERSON><PERSON> ou égal à", "lessThanOrEqual": "Inférieur ou égal à", "notEqual": "<PERSON><PERSON>", "range": "gamme", "add": "plus", "subtract": "soustraire", "multiply": "fois", "divide": "Divisant", "setTo": "Se mettre à", "setBranchConditionsAndChanges": "Définissez la condition et le changement numérique de la branche « {branchText} ».", "setAndEnableConditions": "Définit et active les conditions d’affichage de l’option actuelle", "minValue": "minimum", "to": "arriver", "maxValue": "maximum", "setAndEnableValueChanges": "Définir et activer la modification de la valeur après avoir sélectionné une option", "savingBranchSettings": "Enregistrement des paramètres de branche...", "nameBranchSettingsSaved": "Les paramètres de modification de condition et de valeur pour la branche « {branchText} » sont enregistrés", "generalBranchSettingsSaved": "Les paramètres de branche et les organigrammes ont été enregistrés", "saveFailed": "Échec de l’enregistrement, veuillez vérifier les journaux", "apply": "appliquer", "buttonDisplayTime": "bouton pour afficher l’heure", "buttonDisplayTimeDescription": "Combien de secondes avant la fin de la vidéo montre le bouton de branche", "buttonDisplayTimeNote": "Lorsque ce paramètre est réglé sur 0, les branches s’affichent à la fin de la lecture vidéo", "seconds": "deuxième", "buttonTextOnly": "Affiche<PERSON> uniquement le texte (pas d’arrière-plan du bouton)", "qteDuration": "Durée du bouton QTE", "qteDurationDescription": "Les joueurs doivent réagir dans ce délai", "qteSuccessBranch": "QTE Branche réussie", "qteFailBranch": "Branche ayant échoué de QTE", "qteButtonPosition": "Position du bouton QTE", "qteButtonDurationSeconds": "Temps d’affichage du bouton : {duration} secondes", "qteButtonDisplayTime": "QTE\n{durée} secondes", "qteSuccessLabel": "<PERSON><PERSON><PERSON><PERSON>", "qteFailLabel": "<PERSON><PERSON><PERSON>", "qtePositionInfo": "Position du bouton QTE : {horizontalPercent} %, {verticalPercent} %", "enableVideoClickPause": "Appuyez pour mettre la vidéo en pause pendant sa lecture", "enableVideoClickPauseDesc": "Une fois activé, appuyez sur la zone vidéo pour basculer l’état de lecture/pause"}