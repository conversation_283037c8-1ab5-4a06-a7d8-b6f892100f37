PODS:
  - desktop_window (0.0.1):
    - FlutterMacOS
  - FlutterMacOS (1.0.0)
  - media_kit_libs_macos_video (1.0.4):
    - FlutterMacOS
  - media_kit_native_event_loop (1.0.0):
    - FlutterMacOS
  - media_kit_video (0.0.1):
    - FlutterMacOS
  - package_info_plus (0.0.1):
    - FlutterMacOS
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - screen_brightness_macos (0.1.0):
    - FlutterMacOS
  - screen_retriever_macos (0.0.1):
    - FlutterMacOS
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - url_launcher_macos (0.0.1):
    - FlutterMacOS
  - wakelock_plus (0.0.1):
    - FlutterMacOS
  - window_manager (0.2.0):
    - FlutterMacOS
  - window_size (0.0.2):
    - FlutterMacOS

DEPENDENCIES:
  - desktop_window (from `Flutter/ephemeral/.symlinks/plugins/desktop_window/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - media_kit_libs_macos_video (from `Flutter/ephemeral/.symlinks/plugins/media_kit_libs_macos_video/macos`)
  - media_kit_native_event_loop (from `Flutter/ephemeral/.symlinks/plugins/media_kit_native_event_loop/macos`)
  - media_kit_video (from `Flutter/ephemeral/.symlinks/plugins/media_kit_video/macos`)
  - package_info_plus (from `Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - screen_brightness_macos (from `Flutter/ephemeral/.symlinks/plugins/screen_brightness_macos/macos`)
  - screen_retriever_macos (from `Flutter/ephemeral/.symlinks/plugins/screen_retriever_macos/macos`)
  - shared_preferences_foundation (from `Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin`)
  - url_launcher_macos (from `Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos`)
  - wakelock_plus (from `Flutter/ephemeral/.symlinks/plugins/wakelock_plus/macos`)
  - window_manager (from `Flutter/ephemeral/.symlinks/plugins/window_manager/macos`)
  - window_size (from `Flutter/ephemeral/.symlinks/plugins/window_size/macos`)

EXTERNAL SOURCES:
  desktop_window:
    :path: Flutter/ephemeral/.symlinks/plugins/desktop_window/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  media_kit_libs_macos_video:
    :path: Flutter/ephemeral/.symlinks/plugins/media_kit_libs_macos_video/macos
  media_kit_native_event_loop:
    :path: Flutter/ephemeral/.symlinks/plugins/media_kit_native_event_loop/macos
  media_kit_video:
    :path: Flutter/ephemeral/.symlinks/plugins/media_kit_video/macos
  package_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  screen_brightness_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/screen_brightness_macos/macos
  screen_retriever_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/screen_retriever_macos/macos
  shared_preferences_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin
  url_launcher_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos
  wakelock_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/wakelock_plus/macos
  window_manager:
    :path: Flutter/ephemeral/.symlinks/plugins/window_manager/macos
  window_size:
    :path: Flutter/ephemeral/.symlinks/plugins/window_size/macos

SPEC CHECKSUMS:
  desktop_window: fb7c4f12c1129f947ac482296b6f14059d57a3c3
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  media_kit_libs_macos_video: b3e2bbec2eef97c285f2b1baa7963c67c753fb82
  media_kit_native_event_loop: 81fd5b45192b72f8b5b69eaf5b540f45777eb8d5
  media_kit_video: c75b07f14d59706c775778e4dd47dd027de8d1e5
  package_info_plus: 12f1c5c2cfe8727ca46cbd0b26677728972d9a5b
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  screen_brightness_macos: 2d6d3af2165592d9a55ffcd95b7550970e41ebda
  screen_retriever_macos: 776e0fa5d42c6163d2bf772d22478df4b302b161
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  url_launcher_macos: c82c93949963e55b228a30115bd219499a6fe404
  wakelock_plus: 4783562c9a43d209c458cb9b30692134af456269
  window_manager: 3a1844359a6295ab1e47659b1a777e36773cd6e8
  window_size: 339dafa0b27a95a62a843042038fa6c3c48de195

PODFILE CHECKSUM: 236401fc2c932af29a9fcf0e97baeeb2d750d367

COCOAPODS: 1.16.2
