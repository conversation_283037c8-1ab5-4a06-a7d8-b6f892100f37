import 'package:flutter/material.dart';
import 'package:ve/generated/l10n.dart';

class GlobalValueDialog extends StatefulWidget {
  final Function(String name, String initialValue) onAddValue;

  const GlobalValueDialog({
    Key? key,
    required this.onAddValue,
  }) : super(key: key);

  @override
  _GlobalValueDialogState createState() => _GlobalValueDialogState();
}

class _GlobalValueDialogState extends State<GlobalValueDialog> {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _valueController = TextEditingController();
  
  @override
  void dispose() {
    _nameController.dispose();
    _valueController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(S.of(context).addGlobalValue),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _nameController,
              decoration: InputDecoration(
                labelText: S.of(context).globalValueName,
                hintText: S.of(context).globalValueNameHint,
              ),
            ),
            SizedBox(height: 16),
            TextField(
              controller: _valueController,
              decoration: InputDecoration(
                labelText: S.of(context).initialValue,
                hintText: S.of(context).valueInputHint,
              ),
              keyboardType: TextInputType.number,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text(S.of(context).cancel),
        ),
        ElevatedButton(
          onPressed: () {
            if (_nameController.text.isEmpty) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text(S.of(context).pleaseEnterValueName)),
              );
              return;
            }
            
            // 检查数值是否为空
            final value = _valueController.text.isEmpty ? "0" : _valueController.text;
            
            widget.onAddValue(_nameController.text, value);
            Navigator.pop(context);
          },
          child: Text(S.of(context).confirm),
        ),
      ],
    );
  }
}

/// 全局数值管理对话框
class GlobalValueManagerDialog extends StatefulWidget {
  final String valueName;
  final String currentValue;
  final Function(String) onValueChanged;
  final Function() onDelete;

  const GlobalValueManagerDialog({
    required this.valueName,
    required this.currentValue,
    required this.onValueChanged,
    required this.onDelete,
    Key? key,
  }) : super(key: key);

  @override
  _GlobalValueManagerDialogState createState() => _GlobalValueManagerDialogState();
}

class _GlobalValueManagerDialogState extends State<GlobalValueManagerDialog> {
  late TextEditingController _valueController;
  String? _errorText;

  @override
  void initState() {
    super.initState();
    _valueController = TextEditingController(text: widget.currentValue);
  }

  @override
  void dispose() {
    _valueController.dispose();
    super.dispose();
  }

  void _validateAndSave() {
    final value = _valueController.text;
    
    // 尝试确定值的类型并验证
    if (value.isEmpty) {
      setState(() {
        _errorText = "数值不能为空";
      });
      return;
    }

    // 检查是否是数字
    final isNumeric = double.tryParse(value) != null;
    
    // 检查是否是布尔值
    final isBoolean = value == 'true' || value == 'false';
    
    // 如果既不是数字也不是布尔值，则视为文本
    widget.onValueChanged(value);
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(S.of(context).editGlobalValueTitle(widget.valueName)),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _valueController,
              decoration: InputDecoration(
                labelText: S.of(context).valueLabel,
              ),
              keyboardType: TextInputType.number,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => widget.onDelete(),
          style: TextButton.styleFrom(foregroundColor: Colors.red),
          child: Text(S.of(context).delete),
        ),
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text(S.of(context).cancel),
        ),
        ElevatedButton(
          onPressed: () {
            widget.onValueChanged(_valueController.text);
            Navigator.pop(context);
          },
          child: Text(S.of(context).confirm),
        ),
      ],
    );
  }
} 