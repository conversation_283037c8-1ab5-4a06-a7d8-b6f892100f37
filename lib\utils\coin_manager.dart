import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:shared_preferences/shared_preferences.dart';
import 'save_archive.dart';

/// 金币管理类
/// 负责管理用户的金币数量，包括存储、获取和更新金币数量
class CoinManager {
  static const String _coinKey = 'total_coins';
  static const String _completedProjectsKey = 'completed_projects';
  
  /// 单例实例
  static final CoinManager _instance = CoinManager._internal();
  
  /// 金币数量流控制器
  final _coinStreamController = StreamController<int>.broadcast();
  
  /// 金币数量流
  Stream<int> get coinStream => _coinStreamController.stream;
  
  /// 获取单例实例
  factory CoinManager() {
    print('获取CoinManager单例实例');
    return _instance;
  }
  
  /// 内部构造函数
  CoinManager._internal() {
    print('CoinManager._internal()被调用，初始化单例');
    // 初始化时加载金币数量并发送到流中
    _initCoinStream();
  }
  
  /// 初始化金币流
  void _initCoinStream() async {
    final coins = await getTotalCoins();
    _coinStreamController.add(coins);
  }
  
  /// 获取用户总金币数
  Future<int> getTotalCoins() async {
    try {
      // 使用SaveArchive.getFilePath()获取存储路径
      final basePath = await SaveArchive.getFilePath();
      final coinFile = File('$basePath/coins.json');
      
      // 检查文件是否存在
      if (await coinFile.exists()) {
        final jsonContent = await coinFile.readAsString();
        if (jsonContent.isNotEmpty) {
          final jsonData = jsonDecode(jsonContent);
          final coins = jsonData['coins'] ?? 0;
          print('从文件获取总金币: $coins, 路径: ${coinFile.path}');
          return coins;
        }
      }
      
      // 如果文件不存在或为空，尝试从SharedPreferences获取（兼容旧版本）
      final prefs = await SharedPreferences.getInstance();
      final coins = prefs.getInt(_coinKey) ?? 0;
      print('从SharedPreferences获取总金币: $coins');
      
      // 如果从SharedPreferences获取到了金币，立即保存到文件中
      if (coins > 0) {
        await setTotalCoins(coins);
      }
      
      return coins;
    } catch (e) {
      print('获取总金币时出错: $e');
      return 0;
    }
  }
  
  /// 设置用户总金币数
  Future<bool> setTotalCoins(int coins) async {
    try {
      print('设置总金币: $coins');
      
      // 使用SaveArchive.getFilePath()获取存储路径
      final basePath = await SaveArchive.getFilePath();
      final coinFile = File('$basePath/coins.json');
      
      // 确保目录存在
      final dir = Directory(basePath);
      if (!await dir.exists()) {
        await dir.create(recursive: true);
      }
      
      // 将金币数据保存到文件
      final jsonData = {'coins': coins, 'updateTime': DateTime.now().toIso8601String()};
      final jsonString = jsonEncode(jsonData);
      await coinFile.writeAsString(jsonString);
      
      print('成功保存金币数据到文件: ${coinFile.path}, coins=$coins');
      
      // 同时更新SharedPreferences（兼容旧版本）
      final prefs = await SharedPreferences.getInstance();
      final prefResult = await prefs.setInt(_coinKey, coins);
      print('同步金币数据到SharedPreferences: coins=$coins, success=$prefResult');
      
      // 通知金币数量变化
      _coinStreamController.add(coins);
      
      return true;
    } catch (e) {
      print('设置总金币时出错: $e');
      return false;
    }
  }
  
  /// 增加金币
  Future<int> addCoins(int amount) async {
    final currentCoins = await getTotalCoins();
    final newTotal = currentCoins + amount;
    final success = await setTotalCoins(newTotal);
    print('增加金币: 当前金币=$currentCoins, 增加金币=$amount, 新总金币=$newTotal, 保存成功=$success');
    
    // 验证金币是否真的被保存
    final verifiedCoins = await getTotalCoins();
    print('验证金币保存: 期望金币=$newTotal, 实际金币=$verifiedCoins, 是否一致=${verifiedCoins == newTotal}');
    
    return newTotal;
  }
  
  /// 获取已完成项目列表
  Future<List<String>> _getCompletedProjects() async {
    try {
      // 使用SaveArchive.getFilePath()获取存储路径
      final basePath = await SaveArchive.getFilePath();
      final projectsFile = File('$basePath/completed_projects.json');
      
      // 检查文件是否存在
      if (await projectsFile.exists()) {
        final jsonContent = await projectsFile.readAsString();
        if (jsonContent.isNotEmpty) {
          final jsonData = jsonDecode(jsonContent);
          final projects = (jsonData['projects'] as List<dynamic>).cast<String>();
          print('从文件获取已完成项目列表: $projects, 路径: ${projectsFile.path}');
          return projects;
        }
      }
      
      // 如果文件不存在或为空，尝试从SharedPreferences获取（兼容旧版本）
      final prefs = await SharedPreferences.getInstance();
      final projects = prefs.getStringList(_completedProjectsKey) ?? [];
      print('从SharedPreferences获取已完成项目列表: $projects');
      
      // 如果从SharedPreferences获取到了项目列表，立即保存到文件中
      if (projects.isNotEmpty) {
        await _saveCompletedProjects(projects);
      }
      
      return projects;
    } catch (e) {
      print('获取已完成项目列表时出错: $e');
      return [];
    }
  }
  
  /// 保存已完成项目列表
  Future<bool> _saveCompletedProjects(List<String> projects) async {
    try {
      // 使用SaveArchive.getFilePath()获取存储路径
      final basePath = await SaveArchive.getFilePath();
      final projectsFile = File('$basePath/completed_projects.json');
      
      // 确保目录存在
      final dir = Directory(basePath);
      if (!await dir.exists()) {
        await dir.create(recursive: true);
      }
      
      // 将项目列表保存到文件
      final jsonData = {
        'projects': projects,
        'updateTime': DateTime.now().toIso8601String()
      };
      final jsonString = jsonEncode(jsonData);
      await projectsFile.writeAsString(jsonString);
      
      print('成功保存已完成项目列表到文件: ${projectsFile.path}, projects=$projects');
      
      // 同时更新SharedPreferences（兼容旧版本）
      final prefs = await SharedPreferences.getInstance();
      final prefResult = await prefs.setStringList(_completedProjectsKey, projects);
      print('同步已完成项目列表到SharedPreferences: projects=$projects, success=$prefResult');
      
      return true;
    } catch (e) {
      print('保存已完成项目列表时出错: $e');
      return false;
    }
  }
  
  /// 检查项目是否已完成（已获得金币）
  Future<bool> isProjectCompleted(String projectId) async {
    final completedProjects = await _getCompletedProjects();
    return completedProjects.contains(projectId);
  }
  
  /// 标记项目为已完成
  Future<bool> markProjectAsCompleted(String projectId) async {
    final completedProjects = await _getCompletedProjects();
    
    print('当前已完成项目列表: $completedProjects');
    
    if (completedProjects.contains(projectId)) {
      print('项目已经标记为完成: projectId=$projectId');
      return true; // 项目已经标记为完成
    }
    
    completedProjects.add(projectId);
    final success = await _saveCompletedProjects(completedProjects);
    print('添加项目到已完成列表: projectId=$projectId, 新列表=${completedProjects}, 保存成功=$success');
    
    return success;
  }
  
  /// 为完成项目奖励金币
  /// 返回是否成功奖励（如果项目已完成过则返回false）
  Future<bool> rewardCoinsForProject(String projectId, {int amount = 10}) async {
    print('尝试为项目奖励金币: projectId=$projectId, amount=$amount');
    
    if (projectId.isEmpty) {
      print('错误: projectId为空，无法奖励金币');
      return false;
    }
    
    // 检查项目是否已完成
    final isCompleted = await isProjectCompleted(projectId);
    print('项目完成状态检查: projectId=$projectId, isCompleted=$isCompleted');
    
    if (isCompleted) {
      print('项目已完成，不再奖励金币: projectId=$projectId');
      return false; // 项目已完成，不再奖励
    }
    
    // 增加金币
    final beforeCoins = await getTotalCoins();
    final newTotal = await addCoins(amount);
    print('成功增加金币: 之前金币=$beforeCoins, projectId=$projectId, amount=$amount, 新总金币=$newTotal');
    
    // 标记项目为已完成
    final marked = await markProjectAsCompleted(projectId);
    print('标记项目为已完成: projectId=$projectId, success=$marked');
    
    // 最终验证
    final finalCoins = await getTotalCoins();
    final finalIsCompleted = await isProjectCompleted(projectId);
    print('最终验证: 金币数量=$finalCoins, 项目是否已完成=$finalIsCompleted');
    
    return marked && (finalCoins > beforeCoins);
  }
}