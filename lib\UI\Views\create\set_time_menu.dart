import 'package:flutter/material.dart';
import 'package:flutter_flow_chart/flutter_flow_chart.dart';
import 'package:star_menu/star_menu.dart';
import 'package:ve/generated/l10n.dart';

class SetTimeMenu extends StatefulWidget {
  const SetTimeMenu({
    required this.element,
    super.key,
  });

  final FlowElement element;

  @override
  State<SetTimeMenu> createState() => _SetTimeMenuState();
}
class _SetTimeMenuState extends State<SetTimeMenu> {
  late TextEditingController _startHourController;
  late TextEditingController _startMinuteController;
  late TextEditingController _startSecondController;
  late TextEditingController _endHourController;
  late TextEditingController _endMinuteController;
  late TextEditingController _endSecondController;
  late int allTime; // 视频总时长(秒)
  
  @override
  void initState() {
    super.initState();
    allTime = _timeStringToSeconds(widget.element.allTime);
    _initializeControllers();
  }

  void _initializeControllers() {
    final startParts = widget.element.startTime.split(':');
    final endParts = widget.element.endTime.split(':');
    
    _startHourController = TextEditingController(text: startParts[0]);
    _startMinuteController = TextEditingController(text: startParts[1]);
    _startSecondController = TextEditingController(text: startParts[2]);
    _endHourController = TextEditingController(text: endParts[0]);
    _endMinuteController = TextEditingController(text: endParts[1]);
    _endSecondController = TextEditingController(text: endParts[2]);
  }

  @override
  void dispose() {
    _startHourController.dispose();
    _startMinuteController.dispose();
    _startSecondController.dispose();
    _endHourController.dispose();
    _endMinuteController.dispose();
    _endSecondController.dispose();
    super.dispose();
  }

  int _timeStringToSeconds(String time) {
    List<String> parts = time.split(':');
    int hours = int.parse(parts[0]);
    int minutes = int.parse(parts[1]); 
    
    // 处理秒和毫秒部分
    String secondsPart = parts[2];
    int seconds = 0;
    
    if (secondsPart.contains('.')) {
      List<String> secondsAndMillis = secondsPart.split('.');
      seconds = int.parse(secondsAndMillis[0]);
      // 毫秒在此函数中不需要特别处理，因为我们以秒为单位计算总时长
    } else {
      seconds = int.parse(secondsPart);
    }
    
    return hours * 3600 + minutes * 60 + seconds;
  }

  bool _validateTime() {
    String startTime = '${_startHourController.text}:${_startMinuteController.text}:${_startSecondController.text}';
    String endTime = '${_endHourController.text}:${_endMinuteController.text}:${_endSecondController.text}';

    // 验证时间格式
    if (!_isValidTimeFormat(startTime) || !_isValidTimeFormat(endTime)) {
      _showErrorMessage(S.of(context).invalidTimeFormat);
      return false;
    }

    // 验证是否超过总时长
    int startSeconds = _timeStringToSeconds(startTime);
    int endSeconds = _timeStringToSeconds(endTime);
    
    if (startSeconds >= endSeconds) {
      _showErrorMessage(S.of(context).startTimeBeforeEndTime);
      return false;
    }

    if (endSeconds > allTime) {
      _showErrorMessage(S.of(context).endTimeNotExceedTotal);
      return false;
    }

    return true;
  }

  bool _isValidTimeFormat(String time) {
    final parts = time.split(':');
    if (parts.length != 3) return false;
    
    try {
      final hours = int.parse(parts[0]);
      final minutes = int.parse(parts[1]);
      
      // 处理秒部分，可能包含毫秒
      String secondsPart = parts[2];
      int seconds;
      
      if (secondsPart.contains('.')) {
        // 如果包含小数点，分别验证秒和毫秒部分
        List<String> secondsAndMillis = secondsPart.split('.');
        seconds = int.parse(secondsAndMillis[0]);
        
        // 毫秒部分无需额外验证，任何整数都可以接受
      } else {
        seconds = int.parse(secondsPart);
      }
      
      return hours >= 0 && minutes >= 0 && minutes < 60 && seconds >= 0 && seconds < 60;
    } catch (e) {
      return false;
    }
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {},
      child: StarMenu(
        params: StarMenuParameters.panel(context, columns: 1)
            .copyWith(openDurationMs: 60, onHoverScale: 1),
        items: [
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildTimeRow(S.of(context).startTimeLabel, _startHourController, _startMinuteController, _startSecondController),
                const SizedBox(height: 16),
                _buildTimeRow(S.of(context).endTimeLabel, _endHourController, _endMinuteController, _endSecondController),
                const SizedBox(height: 16),
                Container(
                  width: 200, // 设置固定宽度
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center, // 居中对齐
                    mainAxisSize: MainAxisSize.min, // 最小宽度
                    children: [
                      ElevatedButton(
                        onPressed: () {
                          if (_validateTime()) {
                            widget.element.startTime = '${_startHourController.text}:${_startMinuteController.text}:${_startSecondController.text}';
                            widget.element.endTime = '${_endHourController.text}:${_endMinuteController.text}:${_endSecondController.text}';
                          }
                        },
                        child: Text(S.of(context).save),
                      ),
                      const SizedBox(width: 28), // 按钮之间的间距
                      ElevatedButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        child: Text(S.of(context).close),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
        child: Text(S.of(context).playTimeSettings),
      ),
    );
  }
  Widget _buildTimeRow(
    String label,
    TextEditingController hourController,
    TextEditingController minuteController,
    TextEditingController secondController,
  ) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text('${S.of(context).startTimeLabel == label ? S.of(context).startTimeLabel : S.of(context).endTimeLabel}: '),
        _buildTimeTextField(hourController, S.of(context).hour, 40),
        const Text(':'),
        _buildTimeTextField(minuteController, S.of(context).minute, 40),
        const Text(':'),
        _buildTimeTextField(secondController, S.of(context).second, 40),
      ],
    );
  }

  Widget _buildTimeTextField(TextEditingController controller, String hint, double width) {
    String localizedHint;
    if (hint == '时') {
      localizedHint = S.of(context).hour;
    } else if (hint == '分') {
      localizedHint = S.of(context).minute;
    } else {
      localizedHint = S.of(context).second;
    }
    return Container(
      width: width,
      margin: const EdgeInsets.symmetric(horizontal: 4),
      child: TextField(
        controller: controller,
        decoration: InputDecoration(
          hintText: localizedHint,
          contentPadding: const EdgeInsets.symmetric(horizontal: 8),
          border: const OutlineInputBorder(),
        ),
        keyboardType: TextInputType.number,
        textAlign: TextAlign.center,
      ),
    );
  }
}