// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a af locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'af';

  static String m0(nodeId) => "Huidige nodusargief opgedateer: ${nodeId}";

  static String m1(index) => "Tak ${index}:";

  static String m2(branchText) => "Tak: ${branchText}";

  static String m3(horizontalPercent, verticalPercent) =>
      "Horisontale posisie: ${horizontalPercent}%, Vertikale posisie: ${verticalPercent}%";

  static String m4(count) =>
      "Aantal nodusse met video\'s wat nie gekoppel is nie: ${count}";

  static String m5(name) => "Redigeer globale waarde: ${name}";

  static String m6(error) => "Fout met laai van vloeidiagram: ${error}";

  static String m7(path) => "Volledige videopad: ${path}";

  static String m8(error) => "Fout met laai van argief: ${error}";

  static String m9(branchText) =>
      "Die voorwaarde- en waardeveranderingsinstellings vir tak \"${branchText}\" word gestoor";

  static String m10(nodeId) => "Gemerkte knoop soos besigtig: ${nodeId}";

  static String m11(error) => "Fout met oopmaak van speletjieprojek: ${error}";

  static String m12(path) => "Projek gelaai: ${path}";

  static String m13(path) => "Item nie gevind nie: ${path}";

  static String m14(duration) => "QTE\n${duration} sekondes";

  static String m15(duration) => "Knoppie vertoon tyd: ${duration} sekondes";

  static String m16(horizontalPercent, verticalPercent) =>
      "QTE-knoppie posisie: ${horizontalPercent}%, ${verticalPercent}%";

  static String m17(time) => "Oorblywende tyd: ${time} sekondes";

  static String m18(branchText) =>
      "Stel die voorwaarde en numeriese verandering van tak \"${branchText}\".";

  static String m19(count) =>
      "Die aantal videonodusse na die beginpunt moet 1 wees, huidig: ${count}";

  static String m20(horizontalPercent, verticalPercent) =>
      "Ligging: ${horizontalPercent}%, Vertikaal${verticalPercent}%";

  static String m21(error) => "Dateer Werkswinkelfout op: ${error}";

  static String m22(path) => "Die videolêer bestaan nie: ${path}";

  static String m23(error) => "Video speel fout: ${error}";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "AncientChinese": MessageLookupByLibrary.simpleMessage(
      "Antieke Chinese styl",
    ),
    "Anime": MessageLookupByLibrary.simpleMessage("Anime styl"),
    "Everyone": MessageLookupByLibrary.simpleMessage("Almal van hulle"),
    "Landscape": MessageLookupByLibrary.simpleMessage("Dwarsreghoek"),
    "NSFW": MessageLookupByLibrary.simpleMessage("Nie-openbare plekke"),
    "Other": MessageLookupByLibrary.simpleMessage("Ander style"),
    "Pixel": MessageLookupByLibrary.simpleMessage("Pixel styl"),
    "Portrait": MessageLookupByLibrary.simpleMessage("Longitudinale reghoek"),
    "Realistic": MessageLookupByLibrary.simpleMessage("Realistiese styl"),
    "SearchWorkshop": MessageLookupByLibrary.simpleMessage(
      "Soek dit in die werkswinkel",
    ),
    "Square": MessageLookupByLibrary.simpleMessage("Vierkante"),
    "Subscribe": MessageLookupByLibrary.simpleMessage("Inteken"),
    "SubscribeAndDownload": MessageLookupByLibrary.simpleMessage(
      "Teken in en laai af",
    ),
    "Subscribed": MessageLookupByLibrary.simpleMessage("Ingeteken"),
    "Unsubscribed": MessageLookupByLibrary.simpleMessage("Nie ingeteken nie"),
    "about": MessageLookupByLibrary.simpleMessage("Rakende"),
    "achievementDisplay": MessageLookupByLibrary.simpleMessage(
      "Vertoonvenster van resultate",
    ),
    "achievements": MessageLookupByLibrary.simpleMessage("Vervulling"),
    "activity": MessageLookupByLibrary.simpleMessage("Dinamiese"),
    "add": MessageLookupByLibrary.simpleMessage("Plus"),
    "addDiamond": MessageLookupByLibrary.simpleMessage("Voeg \'n diamant by"),
    "addGlobalValue": MessageLookupByLibrary.simpleMessage(
      "Voeg \'n globale waarde by",
    ),
    "addHexagon": MessageLookupByLibrary.simpleMessage("Voeg \'n seshoek by"),
    "addImage": MessageLookupByLibrary.simpleMessage("Voeg \'n prent by"),
    "addOval": MessageLookupByLibrary.simpleMessage("Voeg \'n ellips by"),
    "addParallelogram": MessageLookupByLibrary.simpleMessage(
      "Voeg \'n parallelogram by",
    ),
    "addRectangle": MessageLookupByLibrary.simpleMessage("Voeg \'n reghoek by"),
    "addResizableRectangle": MessageLookupByLibrary.simpleMessage(
      "Voeg \'n veranderlike reghoek by",
    ),
    "addStorage": MessageLookupByLibrary.simpleMessage("Voeg berging by"),
    "addToFlowchart": MessageLookupByLibrary.simpleMessage(
      "Voeg by vloeidiagram",
    ),
    "addVariable": MessageLookupByLibrary.simpleMessage(
      "Voeg veranderlikes by",
    ),
    "addVideo": MessageLookupByLibrary.simpleMessage("Voeg \'n video by"),
    "adultAgreementContent": MessageLookupByLibrary.simpleMessage(
      "Die Steam Workshop bevat mede-geskepte inhoud van spelers regoor die wêreld, wat inhoud kan behels wat nie geskik is om op openbare plekke te kyk nie.",
    ),
    "adultAgreementTitle": MessageLookupByLibrary.simpleMessage(
      "Maak asseblief seker jy is ten minste 18 jaar oud",
    ),
    "ageRating": MessageLookupByLibrary.simpleMessage("Ouderdomsgraderings"),
    "allSettings": MessageLookupByLibrary.simpleMessage("Alle instellings"),
    "alreadyFavorited": MessageLookupByLibrary.simpleMessage(
      "Reeds geboekmerk",
    ),
    "alreadyLiked": MessageLookupByLibrary.simpleMessage(
      "Daar is reeds van gehou",
    ),
    "alreadySubscribed": MessageLookupByLibrary.simpleMessage("Ingeteken"),
    "apply": MessageLookupByLibrary.simpleMessage("Toepassing"),
    "archive": MessageLookupByLibrary.simpleMessage("Argief"),
    "archiveFileNotFound": MessageLookupByLibrary.simpleMessage(
      "Die argieflêer bestaan nie",
    ),
    "archiveUpdatedForNode": m0,
    "audioSettings": MessageLookupByLibrary.simpleMessage("Oudio-instellings"),
    "audioTrack": MessageLookupByLibrary.simpleMessage("Track"),
    "autoFullScreenVideo": MessageLookupByLibrary.simpleMessage(
      "Outomaties volskerm wanneer \'n video gespeel word",
    ),
    "autoSaveDescription": MessageLookupByLibrary.simpleMessage(
      "Stoor jou spelvordering outomaties",
    ),
    "autoSaveGame": MessageLookupByLibrary.simpleMessage(
      "Outomatiese stoor speletjies",
    ),
    "autoSaveInterval": MessageLookupByLibrary.simpleMessage(
      "Outostoor intervalle",
    ),
    "autoSaveProgress": MessageLookupByLibrary.simpleMessage(
      "Stoor jou vordering outomaties",
    ),
    "autoSelect": MessageLookupByLibrary.simpleMessage("Outomatiese seleksie"),
    "back": MessageLookupByLibrary.simpleMessage("Terugkeer"),
    "backgroundColor": MessageLookupByLibrary.simpleMessage("Agtergrond kleur"),
    "booleanType": MessageLookupByLibrary.simpleMessage("Boole-waarde"),
    "borderColor": MessageLookupByLibrary.simpleMessage("Grens kleur"),
    "branchIndexLabel": m1,
    "branchSettingsSaved": MessageLookupByLibrary.simpleMessage(
      "Takinstellings en vloeidiagramme is gestoor",
    ),
    "branchWithText": m2,
    "buttonDisplayTime": MessageLookupByLibrary.simpleMessage(
      "knoppie om die tyd te vertoon",
    ),
    "buttonDisplayTimeDescription": MessageLookupByLibrary.simpleMessage(
      "Hoeveel sekondes voor die einde van die video wys die takknoppie",
    ),
    "buttonDisplayTimeNote": MessageLookupByLibrary.simpleMessage(
      "Wanneer hierdie parameter op 0 gestel is, word takke aan die einde van video-afspeel vertoon",
    ),
    "buttonOpacity": MessageLookupByLibrary.simpleMessage(
      "Knoppie deursigtigheid",
    ),
    "buttonPositionInfo": m3,
    "buttonText": MessageLookupByLibrary.simpleMessage("Knoppie teks"),
    "buttonTextOnly": MessageLookupByLibrary.simpleMessage(
      "Wys slegs teks (geen knoppie agtergrond nie)",
    ),
    "cancel": MessageLookupByLibrary.simpleMessage("Kanselleer"),
    "captureCurrentFrame": MessageLookupByLibrary.simpleMessage(
      "Vang die huidige raam vas",
    ),
    "challenge": MessageLookupByLibrary.simpleMessage("Uitdaging"),
    "challengeName": MessageLookupByLibrary.simpleMessage(
      "Die naam van die uitdaging",
    ),
    "clickToSubscribeAndDownload": MessageLookupByLibrary.simpleMessage(
      "Klik Teken in om te begin aflaai",
    ),
    "close": MessageLookupByLibrary.simpleMessage("Maak toe"),
    "closeSubtitle": MessageLookupByLibrary.simpleMessage(
      "Skakel onderskrifte af",
    ),
    "committingChanges": MessageLookupByLibrary.simpleMessage(
      "Pleeg jou veranderinge",
    ),
    "completed": MessageLookupByLibrary.simpleMessage(
      "Of dit gedoen word of nie",
    ),
    "confirm": MessageLookupByLibrary.simpleMessage("Bevestig"),
    "confirmExitApp": MessageLookupByLibrary.simpleMessage(
      "Is jy seker jy wil die toepassing verlaat?",
    ),
    "confirmUnsubscribe": MessageLookupByLibrary.simpleMessage(
      "Is jy seker jy wil jou intekening kanselleer?",
    ),
    "confirmUpdateWorkshopItem": MessageLookupByLibrary.simpleMessage(
      "Opgedateerde werkswinkelitems",
    ),
    "confirmUpdateWorkshopItemDescription": MessageLookupByLibrary.simpleMessage(
      "Jy sal \'n bestaande werkswinkelitem wil opdateer. Dit sal die huidige weergawe met jou veranderinge vervang.",
    ),
    "congratsEarnedCoins": MessageLookupByLibrary.simpleMessage(
      "Geluk! Kry \'n beloning van 10 goud!",
    ),
    "coverLoadFailed": MessageLookupByLibrary.simpleMessage(
      "Laai van voorblad het misluk",
    ),
    "createChallenge": MessageLookupByLibrary.simpleMessage(
      "Skep \'n uitdaging",
    ),
    "createNewGame": MessageLookupByLibrary.simpleMessage("Skep \'n speletjie"),
    "createTime": MessageLookupByLibrary.simpleMessage("Skeppingstyd"),
    "credits": MessageLookupByLibrary.simpleMessage("Lys van krediete"),
    "creditsAnimationDesigner": MessageLookupByLibrary.simpleMessage(
      "Animasie Ontwerper",
    ),
    "creditsArtDesigner": MessageLookupByLibrary.simpleMessage("Kunsontwerper"),
    "creditsCodeWriter": MessageLookupByLibrary.simpleMessage("Kode skryf"),
    "creditsCopywriter": MessageLookupByLibrary.simpleMessage("Kopieskryf"),
    "creditsCreativeSource": MessageLookupByLibrary.simpleMessage(
      "Bronne van idees",
    ),
    "creditsCreativeTitle": MessageLookupByLibrary.simpleMessage(
      "Kreatiwiteit en beplanning",
    ),
    "creditsGameProducer": MessageLookupByLibrary.simpleMessage(
      "Speletjie Vervaardiger",
    ),
    "creditsGameplayPlanner": MessageLookupByLibrary.simpleMessage(
      "Spel beplanning",
    ),
    "creditsMarketingTitle": MessageLookupByLibrary.simpleMessage(
      "Mark publisiteit",
    ),
    "creditsProgrammingTitle": MessageLookupByLibrary.simpleMessage(
      "Program ontwikkeling",
    ),
    "creditsSoftwareArchitect": MessageLookupByLibrary.simpleMessage(
      "Sagteware argitektuur",
    ),
    "creditsSoftwarePlanner": MessageLookupByLibrary.simpleMessage(
      "Sagteware beplanning",
    ),
    "creditsSpecialThanks": MessageLookupByLibrary.simpleMessage(
      "Spesiale dank aan jou",
    ),
    "creditsSubtitle": MessageLookupByLibrary.simpleMessage(
      "Sien die spanne en bydraers wat betrokke is by sagteware-ontwikkeling",
    ),
    "creditsThanksTitle": MessageLookupByLibrary.simpleMessage(
      "Dankie dat jy my speletjie gespeel het!",
    ),
    "creditsVideoProducer": MessageLookupByLibrary.simpleMessage(
      "Video produksie",
    ),
    "creditsVisualTitle": MessageLookupByLibrary.simpleMessage(
      "Visuele ontwerp",
    ),
    "currentPosition": MessageLookupByLibrary.simpleMessage("Jy is hier"),
    "curved": MessageLookupByLibrary.simpleMessage("Kurwe"),
    "customButtonPosition": MessageLookupByLibrary.simpleMessage(
      "Pas die knoppieposisie aan",
    ),
    "delete": MessageLookupByLibrary.simpleMessage("Skrap"),
    "description": MessageLookupByLibrary.simpleMessage("Beskrywing"),
    "developer": MessageLookupByLibrary.simpleMessage("Ontwikkelaars"),
    "diamondNode": MessageLookupByLibrary.simpleMessage("Diamant nodusse"),
    "disconnectedVideoCount": m4,
    "diskSpaceInsufficient": MessageLookupByLibrary.simpleMessage(
      "Onvoldoende skyfspasie, gebaseer op die Steam-oplaaimeganisme, moet u genoeg ruimte bespreek op die skyf waar die sagteware geleë is. Maak die skyfspasie skoon en probeer weer oplaai.",
    ),
    "divide": MessageLookupByLibrary.simpleMessage("Verdeel"),
    "download": MessageLookupByLibrary.simpleMessage("Aflaai"),
    "downloadFailed": MessageLookupByLibrary.simpleMessage(
      "Die aflaai het misluk",
    ),
    "downloadSuccess": MessageLookupByLibrary.simpleMessage(
      "Die aflaai was suksesvol",
    ),
    "downloadWorkshop": MessageLookupByLibrary.simpleMessage(
      "Laai die werkswinkel af",
    ),
    "downloading": MessageLookupByLibrary.simpleMessage("Laai tans"),
    "downloadingPleaseWait": MessageLookupByLibrary.simpleMessage(
      "Laai af, asseblief later",
    ),
    "downloads": MessageLookupByLibrary.simpleMessage("Aflaai"),
    "duration": MessageLookupByLibrary.simpleMessage("Totale duur"),
    "editGlobalValue": MessageLookupByLibrary.simpleMessage(
      "Redigeer die globale waardes",
    ),
    "editGlobalValueTitle": m5,
    "elementId": MessageLookupByLibrary.simpleMessage("ID"),
    "elementParams": MessageLookupByLibrary.simpleMessage("element parameters"),
    "elementText": MessageLookupByLibrary.simpleMessage("Teks"),
    "elementType": MessageLookupByLibrary.simpleMessage("Tik"),
    "elevation": MessageLookupByLibrary.simpleMessage("Hoogte"),
    "enableFlowchartCheck": MessageLookupByLibrary.simpleMessage(
      "Aktiveer vloeidiagramopsporing",
    ),
    "enableVideoClickPause": MessageLookupByLibrary.simpleMessage(
      "Tik om die video te onderbreek terwyl dit speel",
    ),
    "enableVideoClickPauseDesc": MessageLookupByLibrary.simpleMessage(
      "Sodra dit aan is, tik op die video-area om die speel-/pouse-toestand te wissel",
    ),
    "endTime": MessageLookupByLibrary.simpleMessage("Om te eindig\nTyd"),
    "endTimeLabel": MessageLookupByLibrary.simpleMessage("Eindtyd"),
    "endTimeNotExceedTotal": MessageLookupByLibrary.simpleMessage(
      "Die eindtyd kan nie die totale lengte van die video oorskry nie",
    ),
    "enterProjectName": MessageLookupByLibrary.simpleMessage(
      "Invoer \'n naam vir die projek",
    ),
    "equalTo": MessageLookupByLibrary.simpleMessage("Bedrag"),
    "exit": MessageLookupByLibrary.simpleMessage("Verlaat die speletjie"),
    "exitFullscreen": MessageLookupByLibrary.simpleMessage("Sluit volskerm af"),
    "failedToGetWorkshopItems": MessageLookupByLibrary.simpleMessage(
      "Kon nie werkswinkelitems kry nie",
    ),
    "favoriteFailed": MessageLookupByLibrary.simpleMessage(
      "Boekmerk het misluk",
    ),
    "favoriteSuccess": MessageLookupByLibrary.simpleMessage(
      "Suksesvol versamel",
    ),
    "fileNotFound": MessageLookupByLibrary.simpleMessage(
      "Die lêer bestaan nie",
    ),
    "fileNotFoundTitle": MessageLookupByLibrary.simpleMessage(
      "Die lêer bestaan nie",
    ),
    "fileNotFoundWithPath": MessageLookupByLibrary.simpleMessage(
      "Lêer bestaan nie: \$jsonFilePath",
    ),
    "filePreparingPleaseRetry": MessageLookupByLibrary.simpleMessage(
      "Die lêer word voorberei, probeer asseblief weer later",
    ),
    "flowChart": MessageLookupByLibrary.simpleMessage("vloei grafiek"),
    "flowchart": MessageLookupByLibrary.simpleMessage("Hersien die proses"),
    "flowchartCheckDescription": MessageLookupByLibrary.simpleMessage(
      "Bespeur ongekoppelde nodusse en oorspronklike verbindings",
    ),
    "flowchartFileNotFound": MessageLookupByLibrary.simpleMessage(
      "FLOWCHART.json lêer kon nie gevind word nie",
    ),
    "flowchartLoadError": m6,
    "flowchartMissingStart": MessageLookupByLibrary.simpleMessage(
      "Die vloeidiagram ontbreek \'n beginpuntelement",
    ),
    "friends": MessageLookupByLibrary.simpleMessage("Vriende"),
    "friendsonly": MessageLookupByLibrary.simpleMessage("Sigbaar vir vriende"),
    "fullVideoPath": m7,
    "fullscreen": MessageLookupByLibrary.simpleMessage("Volskerm modus"),
    "gallery": MessageLookupByLibrary.simpleMessage("Galery"),
    "gameTitle": MessageLookupByLibrary.simpleMessage(
      "Interaktiewe videospeletjie-enjin",
    ),
    "gameWindow": MessageLookupByLibrary.simpleMessage("Speletjie venster"),
    "games": MessageLookupByLibrary.simpleMessage("Speletjie"),
    "generalBranchSettingsSaved": MessageLookupByLibrary.simpleMessage(
      "Takinstellings en vloeidiagramme is gestoor",
    ),
    "generalSettings": MessageLookupByLibrary.simpleMessage(
      "Algemene instellings",
    ),
    "globalValueName": MessageLookupByLibrary.simpleMessage(
      "Die naam van die numeriese waarde",
    ),
    "globalValueNameHint": MessageLookupByLibrary.simpleMessage(
      "Byvoorbeeld: goud, gesondheid, ens",
    ),
    "globalValues": MessageLookupByLibrary.simpleMessage("Globale waardes"),
    "greaterThan": MessageLookupByLibrary.simpleMessage("Groter as"),
    "greaterThanOrEqual": MessageLookupByLibrary.simpleMessage(
      "Groter as of gelyk aan",
    ),
    "hexagonNode": MessageLookupByLibrary.simpleMessage("Seshoekige nodusse"),
    "hideController": MessageLookupByLibrary.simpleMessage(
      "Versteek die beheerbalk",
    ),
    "home": MessageLookupByLibrary.simpleMessage("tuisblad"),
    "hour": MessageLookupByLibrary.simpleMessage("Tyd"),
    "ilpDesc": MessageLookupByLibrary.simpleMessage("Beskrywing"),
    "ilpEditor": MessageLookupByLibrary.simpleMessage("Speletjie redigeerder"),
    "imageNode": MessageLookupByLibrary.simpleMessage("Beeld nodusse"),
    "importImage": MessageLookupByLibrary.simpleMessage("Voer beelde in"),
    "initialValue": MessageLookupByLibrary.simpleMessage("Aanvanklike waarde"),
    "inputDescription": MessageLookupByLibrary.simpleMessage(
      "Voer asb. \'n beskrywing in",
    ),
    "interfaceLanguage": MessageLookupByLibrary.simpleMessage(
      "Koppelvlak taal",
    ),
    "invalidEndTime": MessageLookupByLibrary.simpleMessage(
      "Die eindtyd is ongeldig",
    ),
    "invalidFileType": MessageLookupByLibrary.simpleMessage(
      "Ongeldige lêertipe",
    ),
    "invalidProjectPath": MessageLookupByLibrary.simpleMessage(
      "Die projekpad is ongeldig",
    ),
    "invalidStartTime": MessageLookupByLibrary.simpleMessage(
      "Die begintyd is ongeldig",
    ),
    "invalidTimeFormat": MessageLookupByLibrary.simpleMessage(
      "Die tydformaat is ongeldig",
    ),
    "itemNotSubscribed": MessageLookupByLibrary.simpleMessage(
      "Items waarop nie ingeteken is nie, sal outomaties afgelaai word nadat jy ingeteken het",
    ),
    "joinDiscord": MessageLookupByLibrary.simpleMessage(
      "Sluit aan by die Discord vir spelbesprekings",
    ),
    "language": MessageLookupByLibrary.simpleMessage("Taalinstellings"),
    "languageSettings": MessageLookupByLibrary.simpleMessage("Taalinstellings"),
    "lastUpdate": MessageLookupByLibrary.simpleMessage("Laas opgedateer"),
    "lessThan": MessageLookupByLibrary.simpleMessage("Minder as"),
    "lessThanOrEqual": MessageLookupByLibrary.simpleMessage(
      "Minder as of gelyk aan",
    ),
    "likeFailed": MessageLookupByLibrary.simpleMessage(
      "Kon nie daarvan hou nie",
    ),
    "likeSuccess": MessageLookupByLibrary.simpleMessage("Soos sukses"),
    "loadArchive": MessageLookupByLibrary.simpleMessage("Lees die argief"),
    "loadArchiveError": m8,
    "loadFlowchart": MessageLookupByLibrary.simpleMessage(
      "Lees die vloeidiagram",
    ),
    "loadingFailed": MessageLookupByLibrary.simpleMessage("Kon nie laai nie"),
    "loadingFailedRetry": MessageLookupByLibrary.simpleMessage(
      "As die laai misluk, klik Probeer weer",
    ),
    "mainWindowFullscreen": MessageLookupByLibrary.simpleMessage(
      "Die hoofvenster is volskerm",
    ),
    "maxValue": MessageLookupByLibrary.simpleMessage("Maksimum"),
    "messages": MessageLookupByLibrary.simpleMessage("Boodskap"),
    "minValue": MessageLookupByLibrary.simpleMessage("Minimum"),
    "minute": MessageLookupByLibrary.simpleMessage("Verdeel"),
    "modifyChapterTitle": MessageLookupByLibrary.simpleMessage(
      "Verander die afdelingtitel",
    ),
    "modifyTimeAndCover": MessageLookupByLibrary.simpleMessage(
      "Verander die tyd met die omslag",
    ),
    "multiply": MessageLookupByLibrary.simpleMessage("Keer"),
    "myGames": MessageLookupByLibrary.simpleMessage("My speletjie"),
    "nameBranchSettingsSaved": m9,
    "newProject": MessageLookupByLibrary.simpleMessage("Skep \'n nuwe projek"),
    "newWindowFullScreen": MessageLookupByLibrary.simpleMessage(
      "Die nuwe venster is volskerm",
    ),
    "newWindowFullScreenDescription": MessageLookupByLibrary.simpleMessage(
      "Stel outomaties na volskermmodus wanneer \'n nuwe venster oopmaak",
    ),
    "news": MessageLookupByLibrary.simpleMessage("Nuus"),
    "no": MessageLookupByLibrary.simpleMessage("Nee, jy doen nie"),
    "noArchivesFound": MessageLookupByLibrary.simpleMessage(
      "Geen argief gevind nie",
    ),
    "noAudioTrack": MessageLookupByLibrary.simpleMessage(
      "Geen oudiosnitte nie",
    ),
    "noBranchesToSet": MessageLookupByLibrary.simpleMessage(
      "Die huidige nodus het nie veelvuldige takke nie, en takparameters kan nie gestel word nie",
    ),
    "noGlobalValuesFoundAddFirst": MessageLookupByLibrary.simpleMessage(
      "As jy nie \'n globale waarde kan vind nie, voeg eers \'n waarde by die globale waardebestuur",
    ),
    "noSubtitle": MessageLookupByLibrary.simpleMessage("Geen onderskrifte nie"),
    "noValidFilesFound": MessageLookupByLibrary.simpleMessage(
      "Geen geldige oplaailêers is gevind nie, maak seker dat jou projek slegs ondersteunde lêertipes insluit",
    ),
    "noWorkshopItems": MessageLookupByLibrary.simpleMessage(
      "Jy het nie werkswinkelitems om op te dateer nie",
    ),
    "nodeDetails": MessageLookupByLibrary.simpleMessage("Nodus besonderhede"),
    "nodeMarkedAsWatched": m10,
    "normalBranch": MessageLookupByLibrary.simpleMessage("Gereelde vertakking"),
    "notEqual": MessageLookupByLibrary.simpleMessage("Nie gelyk nie"),
    "numberType": MessageLookupByLibrary.simpleMessage("Syfer"),
    "opaque": MessageLookupByLibrary.simpleMessage("ondeursigtig"),
    "openFlowChartFailed": MessageLookupByLibrary.simpleMessage(
      "Kon nie vloeidiagram oopmaak nie: \$e",
    ),
    "openGame": MessageLookupByLibrary.simpleMessage("Open die speletjie"),
    "openGameError": m11,
    "openGameInNewWindow": MessageLookupByLibrary.simpleMessage(
      "Open die speletjieprojek in \'n nuwe venster",
    ),
    "openGameInNewWindowDesc": MessageLookupByLibrary.simpleMessage(
      "Wanneer dit geaktiveer is, maak die speletjieprojek in \'n nuwe venster oop, en wanneer dit gedeaktiveer is, word dit in die huidige venster oopgemaak",
    ),
    "openGameTitle": MessageLookupByLibrary.simpleMessage("Open die speletjie"),
    "ovalNode": MessageLookupByLibrary.simpleMessage("Ellips nodusse"),
    "parallelogramNode": MessageLookupByLibrary.simpleMessage(
      "Parallelogram nodusse",
    ),
    "pause": MessageLookupByLibrary.simpleMessage("Tyd uit"),
    "play": MessageLookupByLibrary.simpleMessage("Speel"),
    "playPause": MessageLookupByLibrary.simpleMessage("Speel/Pouse"),
    "playTimeSettings": MessageLookupByLibrary.simpleMessage(
      "Begin- en eindtydinstellings vir afspeel",
    ),
    "playbackControl": MessageLookupByLibrary.simpleMessage(
      "Afspeel kontroles",
    ),
    "playbackProgress": MessageLookupByLibrary.simpleMessage("Speel vordering"),
    "playbackRate": MessageLookupByLibrary.simpleMessage("Afspeeltempo"),
    "playbackSpeed": MessageLookupByLibrary.simpleMessage("Speel spoed"),
    "playbackStatus": MessageLookupByLibrary.simpleMessage("Speel status"),
    "player": MessageLookupByLibrary.simpleMessage("speler"),
    "pleaseEnterTrueOrFalse": MessageLookupByLibrary.simpleMessage(
      "Tik asseblief waar of onwaar in",
    ),
    "pleaseEnterValidNumber": MessageLookupByLibrary.simpleMessage(
      "Voer asb. \'n beduidende getal in",
    ),
    "pleaseEnterValue": MessageLookupByLibrary.simpleMessage(
      "Tik asb. \'n numeriese waarde in",
    ),
    "pleaseEnterValueName": MessageLookupByLibrary.simpleMessage(
      "Tik asb. \'n numeriese naam in",
    ),
    "pleaseEnterVariableName": MessageLookupByLibrary.simpleMessage(
      "Tik asb. \'n veranderlike naam in",
    ),
    "pleaseSelectProject": MessageLookupByLibrary.simpleMessage(
      "Kies asb. eers die projek",
    ),
    "popularGames": MessageLookupByLibrary.simpleMessage("Gewilde speletjies"),
    "preparingConfig": MessageLookupByLibrary.simpleMessage(
      "Berei die konfigurasie voor",
    ),
    "preparingProjectFiles": MessageLookupByLibrary.simpleMessage(
      "Berei tans die projeklêer voor...",
    ),
    "previewImage": MessageLookupByLibrary.simpleMessage(
      "Voorskou van die beeld",
    ),
    "previewImageDefault": MessageLookupByLibrary.simpleMessage("Verstek"),
    "private": MessageLookupByLibrary.simpleMessage("Private"),
    "profile": MessageLookupByLibrary.simpleMessage("Persoonlike data"),
    "projectCreated": MessageLookupByLibrary.simpleMessage(
      "Die projek is geskep",
    ),
    "projectExistsContent": MessageLookupByLibrary.simpleMessage(
      "Die projek bestaan reeds, gaan dit in die projek wees?",
    ),
    "projectExistsTitle": MessageLookupByLibrary.simpleMessage(
      "Die projek bestaan reeds",
    ),
    "projectLoaded": m12,
    "projectNameHint": MessageLookupByLibrary.simpleMessage(
      "Tik asb. \'n projeknaam in",
    ),
    "projectNotFound": m13,
    "public": MessageLookupByLibrary.simpleMessage("Openbare"),
    "publicMode": MessageLookupByLibrary.simpleMessage(
      "Openbare lokaalmodus/regstreekse modus",
    ),
    "publicModeDesc": MessageLookupByLibrary.simpleMessage(
      "Wanneer dit geaktiveer is, sal die Werkswinkel-koppelvlak die gebruik van almal se etikette afdwing en sal dit nie nie-werkpleketikette vertoon nie",
    ),
    "publish": MessageLookupByLibrary.simpleMessage("Publiseer"),
    "publishTime": MessageLookupByLibrary.simpleMessage("Vrygestel"),
    "puzzleHint": MessageLookupByLibrary.simpleMessage(
      "Een verkeerde antwoord is verminder",
    ),
    "qteBranch": MessageLookupByLibrary.simpleMessage("QTE-tak"),
    "qteButtonDisplayTime": m14,
    "qteButtonDurationSeconds": m15,
    "qteButtonPosition": MessageLookupByLibrary.simpleMessage(
      "QTE-knoppie posisie",
    ),
    "qteDuration": MessageLookupByLibrary.simpleMessage("QTE-knoppie duur"),
    "qteDurationDescription": MessageLookupByLibrary.simpleMessage(
      "Spelers moet binne hierdie tyd reageer",
    ),
    "qteFailBranch": MessageLookupByLibrary.simpleMessage("QTE mislukte tak"),
    "qteFailLabel": MessageLookupByLibrary.simpleMessage("Misluk"),
    "qtePositionInfo": m16,
    "qteSuccessBranch": MessageLookupByLibrary.simpleMessage(
      "QTE Suksesvolle Tak",
    ),
    "qteSuccessLabel": MessageLookupByLibrary.simpleMessage("Slaag"),
    "questionDescription": MessageLookupByLibrary.simpleMessage(
      "Probleem beskrywing",
    ),
    "range": MessageLookupByLibrary.simpleMessage("Reeks"),
    "rate": MessageLookupByLibrary.simpleMessage("Afspeeltempo"),
    "recentlyEdited": MessageLookupByLibrary.simpleMessage(
      "Onlangs geredigeer",
    ),
    "recentlyPlayed": MessageLookupByLibrary.simpleMessage("Onlangs gespeel"),
    "rectangleNode": MessageLookupByLibrary.simpleMessage("Reghoekige nodusse"),
    "rectangular": MessageLookupByLibrary.simpleMessage("Reghoek"),
    "remainingTimeLabel": m17,
    "remoteReservedWord": MessageLookupByLibrary.simpleMessage(
      "Remote is gereserveer vir die program, verander asseblief die projeknaam",
    ),
    "removeAll": MessageLookupByLibrary.simpleMessage("Verwyder almal"),
    "removeAllConnections": MessageLookupByLibrary.simpleMessage(
      "Verwyder alle verbindings",
    ),
    "removeImage": MessageLookupByLibrary.simpleMessage("Verwyder die beeld"),
    "required": MessageLookupByLibrary.simpleMessage("Vereis"),
    "resume": MessageLookupByLibrary.simpleMessage(
      "Gaan terug en speel verder",
    ),
    "retry": MessageLookupByLibrary.simpleMessage("Probeer"),
    "retryLoading": MessageLookupByLibrary.simpleMessage(
      "Klik op Probeer weer",
    ),
    "save": MessageLookupByLibrary.simpleMessage("Stoor"),
    "saveFailed": MessageLookupByLibrary.simpleMessage(
      "Stoor het misluk, gaan asseblief die logboeke na",
    ),
    "saveFlowchart": MessageLookupByLibrary.simpleMessage(
      "Stoor die vloeidiagram",
    ),
    "saveFlowchartFailed": MessageLookupByLibrary.simpleMessage(
      "Kon nie vloeidiagram stoor nie",
    ),
    "saveSettings": MessageLookupByLibrary.simpleMessage(
      "Stoor die instellings",
    ),
    "saving": MessageLookupByLibrary.simpleMessage("Stoor..."),
    "savingBranchSettings": MessageLookupByLibrary.simpleMessage(
      "Stoor tans takinstellings...",
    ),
    "screenPreview": MessageLookupByLibrary.simpleMessage("Skerm voorskou"),
    "screenshots": MessageLookupByLibrary.simpleMessage("Skermskoot"),
    "search": MessageLookupByLibrary.simpleMessage("Soek"),
    "second": MessageLookupByLibrary.simpleMessage("Tweede"),
    "seconds": MessageLookupByLibrary.simpleMessage("Tweede"),
    "seed": MessageLookupByLibrary.simpleMessage("Saad"),
    "segmented": MessageLookupByLibrary.simpleMessage("subvlak"),
    "selectBranchToSet": MessageLookupByLibrary.simpleMessage(
      "Kies die tak wat jy wil opstel",
    ),
    "selectContentRating": MessageLookupByLibrary.simpleMessage(
      "Kies asb. \'n inhoudgradering",
    ),
    "selectWorkshopItemToUpdate": MessageLookupByLibrary.simpleMessage(
      "Kies die Werkswinkelitem wat jy wil opdateer",
    ),
    "setAndEnableConditions": MessageLookupByLibrary.simpleMessage(
      "Stel en aktiveer die voorwaardes waaronder die huidige opsie verskyn",
    ),
    "setAndEnableValueChanges": MessageLookupByLibrary.simpleMessage(
      "Stel en aktiveer die verandering in waarde nadat \'n opsie gekies is",
    ),
    "setBranchConditionsAndChanges": m18,
    "setBranchParams": MessageLookupByLibrary.simpleMessage(
      "Stel die taktipe en parameters in",
    ),
    "setCurrentTime": MessageLookupByLibrary.simpleMessage(
      "Stel die huidige tyd in",
    ),
    "setOptionsAndValueChanges": MessageLookupByLibrary.simpleMessage(
      "Stel die opsies in en verander die waardes",
    ),
    "setTo": MessageLookupByLibrary.simpleMessage("Stel op"),
    "settings": MessageLookupByLibrary.simpleMessage("Stel"),
    "settingsSaved": MessageLookupByLibrary.simpleMessage(
      "Die instellings word gestoor",
    ),
    "shape": MessageLookupByLibrary.simpleMessage("Prent vorms"),
    "showController": MessageLookupByLibrary.simpleMessage(
      "Vertoon die beheerbalk",
    ),
    "showControllerDuringPlayback": MessageLookupByLibrary.simpleMessage(
      "Vertoon die beheerbalk terwyl die video speel",
    ),
    "showVideoController": MessageLookupByLibrary.simpleMessage(
      "Vertoon die videobeheerbalk",
    ),
    "showVideoControls": MessageLookupByLibrary.simpleMessage(
      "Vertoon die videobeheerbalk",
    ),
    "showVideoControlsDescription": MessageLookupByLibrary.simpleMessage(
      "Vertoon die beheerbalk terwyl die video speel",
    ),
    "sort": MessageLookupByLibrary.simpleMessage("Rangskik"),
    "sortBy": MessageLookupByLibrary.simpleMessage("Sorteer volgens"),
    "sortByFavorites": MessageLookupByLibrary.simpleMessage(
      "Volgens die aantal gunstelinge",
    ),
    "sortByPublishDate": MessageLookupByLibrary.simpleMessage("Vrygestel"),
    "sortBySubscribers": MessageLookupByLibrary.simpleMessage(
      "Volgens aantal intekeninge",
    ),
    "sortByUpdateDate": MessageLookupByLibrary.simpleMessage("Bygewerk"),
    "sortByVote": MessageLookupByLibrary.simpleMessage(
      "Volgens die aantal likes",
    ),
    "startElementIdNotFound": MessageLookupByLibrary.simpleMessage(
      "Kan nie die beginelement-ID van die projek kry nie",
    ),
    "startGame": MessageLookupByLibrary.simpleMessage("Speel die speletjie"),
    "startNode": MessageLookupByLibrary.simpleMessage("Begin nodus"),
    "startNodeVideoCount": m19,
    "startPoint": MessageLookupByLibrary.simpleMessage("Beginpunt"),
    "startTime": MessageLookupByLibrary.simpleMessage("Begin\nTyd"),
    "startTimeBeforeEndTime": MessageLookupByLibrary.simpleMessage(
      "Die begintyd moet vroeër as die eindtyd wees",
    ),
    "startTimeLabel": MessageLookupByLibrary.simpleMessage("Begin tyd"),
    "steamAuthorOtherFiles": MessageLookupByLibrary.simpleMessage(
      "Ander dokumente van die skrywer",
    ),
    "steamChallenge": MessageLookupByLibrary.simpleMessage(
      "Begin die uitdaging",
    ),
    "steamGallery": MessageLookupByLibrary.simpleMessage(
      "Stoom Werkswinkel Galery",
    ),
    "steamLimitedAccount": MessageLookupByLibrary.simpleMessage(
      "Steam-beperkte rekeninge",
    ),
    "steamWorkshop": MessageLookupByLibrary.simpleMessage("Stoom werkswinkel"),
    "stop": MessageLookupByLibrary.simpleMessage("Stop dit"),
    "storageNode": MessageLookupByLibrary.simpleMessage("Berging nodusse"),
    "style": MessageLookupByLibrary.simpleMessage("Prent styl"),
    "subscribeFailed": MessageLookupByLibrary.simpleMessage(
      "Inskrywing het misluk: ",
    ),
    "subscribeSuccess": MessageLookupByLibrary.simpleMessage(
      "Die intekening is suksesvol, begin aflaai ...",
    ),
    "subtitle": MessageLookupByLibrary.simpleMessage("Byskrif"),
    "subtract": MessageLookupByLibrary.simpleMessage("Trek"),
    "switchHorizontal": MessageLookupByLibrary.simpleMessage(
      "Skakel oor na \'n horisontale uitleg",
    ),
    "switchVertical": MessageLookupByLibrary.simpleMessage(
      "Skakel oor na \'n vertikale uitleg",
    ),
    "textType": MessageLookupByLibrary.simpleMessage("Teks"),
    "thickness": MessageLookupByLibrary.simpleMessage("Dikte"),
    "timeEdit": MessageLookupByLibrary.simpleMessage("Tyd redigering"),
    "timeLimit": MessageLookupByLibrary.simpleMessage(
      "Tydsbeperking (sekondes)",
    ),
    "timedBranch": MessageLookupByLibrary.simpleMessage("Tydsbeperkte takke"),
    "title": MessageLookupByLibrary.simpleMessage("Titel"),
    "titleCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
      "Die titel kan nie leeg wees nie",
    ),
    "titlePositionInfo": m20,
    "to": MessageLookupByLibrary.simpleMessage("Kom"),
    "toggleConnectable": MessageLookupByLibrary.simpleMessage(
      "Wissel kan gekoppel word",
    ),
    "toggleResizable": MessageLookupByLibrary.simpleMessage(
      "Wissel om die grootte te verander",
    ),
    "toggleSortDirection": MessageLookupByLibrary.simpleMessage(
      "Wissel die sorteerrigting",
    ),
    "transparent": MessageLookupByLibrary.simpleMessage("Deursigtige"),
    "unknownNodeType": MessageLookupByLibrary.simpleMessage("Onbekende tipe"),
    "unsubscribe": MessageLookupByLibrary.simpleMessage(
      "Kanselleer jou intekening",
    ),
    "unsubscribeFailed": MessageLookupByLibrary.simpleMessage(
      "Kon nie die intekening kanselleer nie",
    ),
    "unsubscribeSuccess": MessageLookupByLibrary.simpleMessage(
      "Die intekening is suksesvol uitgeteken",
    ),
    "unsupportedFileFormat": MessageLookupByLibrary.simpleMessage(
      "Nie-ondersteunde lêerformate, laat die werkswinkel slegs video-, jpg- en json-lêers toe",
    ),
    "updateSuccess": MessageLookupByLibrary.simpleMessage("Opdatering sukses!"),
    "updateTime": MessageLookupByLibrary.simpleMessage("Bygewerk"),
    "updateWorkshop": MessageLookupByLibrary.simpleMessage(
      "Opgedateerde werkswinkelitems",
    ),
    "updateWorkshopError": m21,
    "updating": MessageLookupByLibrary.simpleMessage("Bywerking..."),
    "upload": MessageLookupByLibrary.simpleMessage("Oplaai"),
    "uploadFailed": MessageLookupByLibrary.simpleMessage("Oplaai het misluk"),
    "uploadFailedWithColon": MessageLookupByLibrary.simpleMessage(
      "Oplaai het misluk: ",
    ),
    "uploadNow": MessageLookupByLibrary.simpleMessage("Laai dit nou op"),
    "uploadSuccess": MessageLookupByLibrary.simpleMessage(
      "Die oplaai is suksesvol",
    ),
    "uploadWorkshop": MessageLookupByLibrary.simpleMessage(
      "Laai die werkswinkel op",
    ),
    "uploading": MessageLookupByLibrary.simpleMessage("Oplaai"),
    "uploadingContent": MessageLookupByLibrary.simpleMessage("Laai inhoud op"),
    "uploadingPleaseWait": MessageLookupByLibrary.simpleMessage(
      "Laai op, wag asseblief...",
    ),
    "uploadingPreviewImage": MessageLookupByLibrary.simpleMessage(
      "Laai \'n voorskou op",
    ),
    "useNewWindowForEditing": MessageLookupByLibrary.simpleMessage(
      "\'n Nuwe venster maak die Redigeer projek-koppelvlak oop",
    ),
    "useNewWindowForEditingDescription": MessageLookupByLibrary.simpleMessage(
      "Wanneer dit geaktiveer is, word die projekredigeerder in \'n nuwe venster oopgemaak, en wanneer dit gedeaktiveer is, word dit in die huidige venster oopgemaak",
    ),
    "valueInputHint": MessageLookupByLibrary.simpleMessage(
      "Tik asb. \'n aanvanklike waarde in",
    ),
    "valueLabel": MessageLookupByLibrary.simpleMessage("numeriese waarde"),
    "variableAlreadyExists": MessageLookupByLibrary.simpleMessage(
      "Die veranderlike naam bestaan reeds",
    ),
    "variableName": MessageLookupByLibrary.simpleMessage("Veranderlike naam"),
    "variableType": MessageLookupByLibrary.simpleMessage("Tik"),
    "version": MessageLookupByLibrary.simpleMessage("Weergawe"),
    "verticalLayoutDescription": MessageLookupByLibrary.simpleMessage(
      "Vertikale videobewerkingskoppelvlak (aanbeveel vir hoë-resolusie-skerms)",
    ),
    "videoCover": MessageLookupByLibrary.simpleMessage("Video-omslag"),
    "videoFileNotExist": m22,
    "videoNode": MessageLookupByLibrary.simpleMessage("Video nodusse"),
    "videoNodeLocked": MessageLookupByLibrary.simpleMessage(
      "Hierdie videoknoop is nie ontsluit nie, kyk asseblief eers na die vorige video",
    ),
    "videoPlayback": MessageLookupByLibrary.simpleMessage("Video-afspeel"),
    "videoPlaybackError": m23,
    "videoTimeAndCover": MessageLookupByLibrary.simpleMessage(
      "Videotyd en omslag",
    ),
    "viewFlowChart": MessageLookupByLibrary.simpleMessage(
      "Bekyk die vloeidiagram",
    ),
    "viewFlowchart": MessageLookupByLibrary.simpleMessage(
      "Bekyk die vloeidiagram",
    ),
    "visibility": MessageLookupByLibrary.simpleMessage("Sigbaarheid"),
    "volume": MessageLookupByLibrary.simpleMessage("Volume vlak"),
    "volumeControl": MessageLookupByLibrary.simpleMessage("Volume beheer"),
    "watchedNodesCount": MessageLookupByLibrary.simpleMessage(
      "Die aantal nodusse wat gemonitor is",
    ),
    "workshop": MessageLookupByLibrary.simpleMessage("Werkswinkel"),
    "workshopItemUpdated": MessageLookupByLibrary.simpleMessage(
      "Die Werkswinkelprojek is opgedateer",
    ),
    "workshopItemUploaded": MessageLookupByLibrary.simpleMessage(
      "Die Werkswinkelprojek is opgelaai",
    ),
    "workshopItems": MessageLookupByLibrary.simpleMessage("Werkswinkel projek"),
    "workshopRecommendedDescription": MessageLookupByLibrary.simpleMessage(
      "Geskryf deur die hoof van die enjindorp self, het die dorpenaars almal gesê dat hierdie handleiding outomaties sal ontwikkel met die weergawe-opdatering",
    ),
    "workshopRecommendedTitle": MessageLookupByLibrary.simpleMessage(
      "Gids om die wêreld te red",
    ),
    "workshopRuleNoAds": MessageLookupByLibrary.simpleMessage(
      "Geen advertensies nie",
    ),
    "workshopRuleNoAdult": MessageLookupByLibrary.simpleMessage(
      "Geen fotografiese of outentieke pornografie of naaktheid nie",
    ),
    "workshopRuleNoCopyright": MessageLookupByLibrary.simpleMessage(
      "Geen kopieregskending nie",
    ),
    "workshopRuleNoMisleading": MessageLookupByLibrary.simpleMessage(
      "Daar is geen misleidende voorskoue nie",
    ),
    "workshopRuleNoOffensive": MessageLookupByLibrary.simpleMessage(
      "Geen aanstootlike of gewelddadige bloed nie",
    ),
    "workshopRules": MessageLookupByLibrary.simpleMessage("Werkswinkel reëls"),
    "workshopRulesDescription": MessageLookupByLibrary.simpleMessage(
      "Voordat u \'n interaktiewe video by die werkswinkel indien, moet u seker maak dat dit nie Steam se diensbepalings oortree nie, anders sal die interaktiewe video verwyder word:",
    ),
    "workshopRulesSpecial": MessageLookupByLibrary.simpleMessage(
      "In die besonder moet interaktiewe video\'s, voorskoue en beskrywings aan die volgende reëls voldoen:",
    ),
  };
}
