import 'dart:async';
import 'dart:convert';
import 'dart:ffi';
import 'dart:io';
import 'dart:typed_data';

import 'package:file_picker/file_picker.dart';

import 'package:ffi/ffi.dart';
import 'package:steamworks/steamworks.dart';
import 'package:url_launcher/url_launcher_string.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'steam_file_ex.dart';
import 'steam_file.dart';
import 'steam_globals.dart';
import 'steam_tags.dart';
import 'package:ve/UI/Views/create/create_game_view.dart';
import 'package:flutter/material.dart';
import 'package:ve/generated/l10n.dart';
import 'package:ve/UI/shared/coin_animation.dart';
import 'coin_manager.dart';

/// 允许的视频格式列表
final List<String> allowedVideoFormats = [
  '3do', 'str', '4xm', 'anm', 'asf', 'avi', 'avs', 'bmv', 'cdxl', 'cine', 
  'dav', 'dnxhd', 'dv', 'flv', 'gxf', 'h264', 'hevc', 'ivf', 'kux', 'm2ts', 
  'mkv', 'webm', 'mlv', 'mov', 'mp4', '3gp', 'mpg', 'mpeg', 'mts', 'mxf', 
  'nut', 'ogv', 'rm', 'roq', 'swf', 'thp', 'vob', 'wmv', 'yuv'
];

/// 允许的字幕格式列表
final List<String> allowedSubtitleFormats = [
  'ass', 'ssa', 'srt', 'sub', 'vtt', 'sup'
];

enum ApiLanguage {
  english,
  schinese,
}

enum SteamUGCSort {
  publishTime,
  updateTime,
  vote,
}

extension ArrayCharExtensions on Array<Char> {
  String toDartString() {
    var bytesBuilder = BytesBuilder();
    int index = 0;
    while (this[index] != 0) {
      bytesBuilder.addByte(this[index]);
      ++index;
    }

    var bytes = bytesBuilder.takeBytes();
    return utf8.decode(bytes);
  }
}

class SteamDownloadListener {
  SteamDownloadListener._();

  static bool _init = false;
  static final Map<int, List<void Function()>> _events = {};
  static final Map<int, double> _downloadProgress = {};

  static void init() {
    if (_init) return;
    _init = true;
    SteamClient.instance.registerCallback<DownloadItemResult>(
      cb: (result) {
        if (result.appId != SteamGlobals.instance.appId) return;
        _events[result.publishedFileId]
            ?.toList()
            .forEach((element) => element());
      },
    );
  }

  static void add(int fileId, void Function() cb, {bool once = false}) {
    if (once) {
      late void Function() newCB;
      newCB = () {
        cb();
        remove(fileId, newCB);
      };
      _events.putIfAbsent(fileId, () => []).add(newCB);
    } else {
      _events.putIfAbsent(fileId, () => []).add(cb);
    }
  }

  static void remove(int fileId, void Function() cb) {
    _events[fileId]?.remove(cb);
  }

  static void removeAll([int? fileId]) {
    if (fileId == null) {
      _events.clear();
    } else {
      _events.remove(fileId);
    }
  }
  
  static bool isDownloading(int itemId) {
    final steamFile = SteamSimpleFile(id: itemId);
    return steamFile.isDownLoading;
  }
  
  static double getProgress(int itemId) {
    final steamFile = SteamSimpleFile(id: itemId);
    steamFile.updateDownloadBytes();
    return steamFile.totalBytes > 0 
      ? steamFile.downloadedBytes / steamFile.totalBytes 
      : 0.0;
  }
}

class SubmitResult {
  final EResult result;
  final int publishedFileId;
  final bool userNeedsToAcceptWorkshopLegalAgreement;

  SubmitResult({
    required this.result,
    required this.publishedFileId,
    required this.userNeedsToAcceptWorkshopLegalAgreement,
  });

  @override
  String toString() =>
      'SubmitResult{result: $result, publishedFileId: $publishedFileId, userNeedsToAcceptWorkshopLegalAgreement: $userNeedsToAcceptWorkshopLegalAgreement}';
}

extension SteamClientEx on SteamClient {
  int get userId => SteamGlobals.instance.accountId;

  int get appId => SteamGlobals.instance.appId;

  /// return steam ugc item file id
  Future<int> createItemReturnId() async {
    final completer = Completer<int>();
    registerCallResult<CreateItemResult>(
      asyncCallId: steamUgc.createItem(
        appId,
        EWorkshopFileType.community,
      ),
      cb: (result, hasFailed) {
        print('steamUgc.createItem\n'
            'publishedFileId: ${result.publishedFileId}');
        if (result.result == EResult.eResultOK) {
          completer.complete(result.publishedFileId);
        } else {
          completer.completeError(Exception('create item failed'));
        }
      },
    );
    return completer.future;
  }

  Future removeItem(int itemId) async {
    assert(itemId > 0);
    final Completer completer = Completer();
    registerCallResult<DeleteItemResult>(
      asyncCallId: steamUgc.deleteItem(itemId),
      cb: (result, hasFailed) {
        print('删除创意工坊 $itemId ${result.result}');
        completer.complete(result.result);
      },
    );
    return completer.future;
  }

  Future<bool> subscribe(int id) async {
    final complete = Completer<EResult>();
    registerCallResult<RemoteStorageSubscribePublishedFileResult>(
      asyncCallId: steamUgc.subscribeItem(id),
      cb: (r, f) {
        print('Subscribe $id result: ${r.result}');
        complete.complete(r.result);
      },
    );
    return (await complete.future) == EResult.eResultOK;
  }

  Future<bool> unsubscribe(int id) async {
    final complete = Completer<EResult>();
    registerCallResult<RemoteStorageUnsubscribePublishedFileResult>(
      asyncCallId: steamUgc.unsubscribeItem(id),
      cb: (r, f) {
        print('Unsubscribe $id result: ${r.result}');
        complete.complete(r.result);
      },
    );
    return (await complete.future) == EResult.eResultOK;
  }
  
  Future<bool> voteUp(int id) async {
    final complete = Completer<EResult>();
    registerCallResult<SetUserItemVoteResult>(
      asyncCallId: steamUgc.setUserItemVote(id, true),
      cb: (r, f) {
        print('Vote up $id result: ${r.result}');
        complete.complete(r.result);
      },
    );
    return (await complete.future) == EResult.eResultOK;
  }
  
  Future<bool> addToFavorites(int id) async {
    final complete = Completer<EResult>();
    registerCallResult<UserFavoriteItemsListChanged>(
      asyncCallId: steamUgc.addItemToFavorites(appId, id),
      cb: (r, f) {
        print('Add to favorites $id result: ${r.result}');
        complete.complete(r.result);
      },
    );
    return (await complete.future) == EResult.eResultOK;
  }
  
  Future<bool> removeFromFavorites(int id) async {
    final complete = Completer<EResult>();
    registerCallResult<UserFavoriteItemsListChanged>(
      asyncCallId: steamUgc.removeItemFromFavorites(appId, id),
      cb: (r, f) {
        print('Remove from favorites $id result: ${r.result}');
        complete.complete(r.result);
      },
    );
    return (await complete.future) == EResult.eResultOK;
  }
  
  Future<bool> hasVotedUp(int id) async {
    final complete = Completer<bool>();
    registerCallResult<GetUserItemVoteResult>(
      asyncCallId: steamUgc.getUserItemVote(id),
      cb: (r, f) {
        print('Has voted up $id result: ${r.result}, voted up: ${r.votedUp}');
        if (r.result == EResult.eResultOK) {
          complete.complete(r.votedUp);
        } else {
          complete.complete(false);
        }
      },
    );
    return complete.future;
  }
  
  Future<bool> voteDown(int id) async {
    final complete = Completer<EResult>();
    registerCallResult<SetUserItemVoteResult>(
      asyncCallId: steamUgc.setUserItemVote(id, false),
      cb: (r, f) {
        print('Vote down $id result: ${r.result}');
        complete.complete(r.result);
      },
    );
    return (await complete.future) == EResult.eResultOK;
  }
  
  Future<bool> isFavorited(int id) async {
    // 获取物品状态，检查是否已收藏
    final itemState = steamUgc.getItemState(id);
    // 根据Steam API文档，位掩码中的特定位表示是否已收藏
    const int favoriteFlag = 4; // 假设第3位表示收藏状态，需要根据实际API确认
    return (itemState & favoriteFlag) != 0;
  }

  Future<bool> isSubscribed(int id) async {
    // 获取物品状态，检查是否已订阅
    final itemState = steamUgc.getItemState(id);
    // 根据Steam API文档，位掩码中的特定位表示是否已订阅
    const int subscribedFlag = 1; // 第1位表示订阅状态
    final isSubscribed = (itemState & subscribedFlag) != 0;
    print('isSubscribed $id result: $isSubscribed (state: $itemState)');
    return Future.value(isSubscribed);
  }

  openUrl(String url, [mode = EActivateGameOverlayToWebPageMode.default_]) {
    if (url.startsWith('https://')) url = 'steam://openurl/$url';
    launchUrlString(url);

    /// bug
    // steamFriends.activateGameOverlayToWebPage(
    //   url.toNativeUtf8(),
    //   mode,
    // );
  }

  startPlaytimeTracking(int itemId) {
    final id = calloc<UnsignedLongLong>()..value = itemId;
    final SteamApiCall res = steamUgc.startPlaytimeTracking(id, 1);
    print('追踪游戏时间，结果:$res');
  }

  stopPlaytimeTracking() {
    steamUgc.stopPlaytimeTrackingForAllItems();
  }

  Future<void> downloadUGCItem(int id, {bool highPriority = false}) async {
    final completer = Completer<void>();
    SteamDownloadListener.add(id, () {
      completer.complete();
    }, once: true);
    steamUgc.downloadItem(id, highPriority);
    return completer.future;
  }

  // 获取创意工坊项目更新进度
  Future<(EItemUpdateStatus, double)> getItemUpdateProgress(int handle) async {
    final completer = Completer<(EItemUpdateStatus, double)>();
    using((arena) {
      final bytesProcessed = arena<UnsignedLongLong>();
      final bytesTotal = arena<UnsignedLongLong>();
      final status = steamUgc.getItemUpdateProgress(
        handle,
        bytesProcessed,
        bytesTotal,
      );
      final progress = bytesTotal.value > 0 
          ? bytesProcessed.value / bytesTotal.value 
          : 0.0;
      completer.complete((status, progress));
    });
    return completer.future;
  }
  
  Future<SubmitResult?> uploadToSteam(BuildContext context, String projectName, {SteamFile? steamFile}) async {
    File? selectedImageFile;
    
    // 在最开始就保存ScaffoldMessengerState的引用，解决重复声明问题
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    
    // 预先缓存所有需要的本地化文本和上下文信息，避免后续使用已销毁的context
    final updateSuccessText = steamFile != null ? S.of(context).workshopItemUpdated : S.of(context).workshopItemUploaded;
    final uploadFailedText = S.of(context).uploadFailedWithColon;
    final fileNotFoundText = S.of(context).fileNotFound;
    final uploadingText = steamFile != null ? S.of(context).updating : S.of(context).uploading;
    final congratsText = S.of(context).congratsEarnedCoins;
    final deviceWidth = MediaQuery.of(context).size.width;
    
    // 预先缓存所有上传状态的本地化文本，避免后续使用已销毁的context
    final preparingConfigText = S.of(context).preparingConfig;
    final uploadingPleaseWaitText = S.of(context).uploadingPleaseWait;
    final uploadingContentText = S.of(context).uploadingContent;
    final uploadingPreviewImageText = S.of(context).uploadingPreviewImage;
    final committingChangesText = S.of(context).committingChanges;
    final preparingProjectFilesText = S.of(context).preparingProjectFiles;
    
    // 检查是否是首次上传，如果是则显示警告
    final prefs = await SharedPreferences.getInstance();
    final hasShownWorkshopWarning = prefs.getBool('has_shown_workshop_warning') ?? false;
    
    // 如果是更新现有物品，询问用户确认
    if (steamFile != null) {
      // 确认是否更新现有物品
      final bool? confirmed = await showDialog<bool>(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            backgroundColor: Colors.black87,
            title: Text(S.of(context).confirmUpdateWorkshopItem, style: TextStyle(color: Colors.white)),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(S.of(context).confirmUpdateWorkshopItemDescription, style: TextStyle(color: Colors.white)),
                  SizedBox(height: 20),
                  ListTile(
                    contentPadding: EdgeInsets.zero,
                    leading: SizedBox(
                      width: 80,
                      height: 45,
                      child: steamFile.cover.isNotEmpty 
                          ? Image.network(steamFile.cover, fit: BoxFit.cover)
                          : Container(color: Colors.grey.shade800),
                    ),
                    title: Text(steamFile.name, style: TextStyle(color: Colors.white)),
                    subtitle: Text('ID: ${steamFile.id}', style: TextStyle(color: Colors.white70)),
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                child: Text(S.of(context).cancel, style: TextStyle(color: Colors.white)),
                onPressed: () {
                  Navigator.of(context).pop(false);
                },
              ),
              ElevatedButton(
                child: Text(S.of(context).confirm),
                onPressed: () {
                  Navigator.of(context).pop(true);
                },
              ),
            ],
          );
        },
      );
      
      if (confirmed != true) {
        return null;
      }
    }
    // 如果是新创建物品且未显示过警告，显示警告
    else if (!hasShownWorkshopWarning) {
      // 显示警告对话框
      final bool? confirmed = await showDialog<bool>(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return AlertDialog(
            backgroundColor: Colors.black87,
            title: Text(S.of(context).workshopRules, style: TextStyle(color: Colors.white)),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    S.of(context).workshopRulesDescription,
                    style: TextStyle(color: Colors.white),
                  ),
                  InkWell(
                    onTap: () => openEulaUrl(),
                    child: Text(
                      'http://store.steampowered.com/subscriber_agreement/',
                      style: TextStyle(color: Colors.blue),
                    ),
                  ),
                  SizedBox(height: 20),
                  Text(
                    S.of(context).workshopRulesSpecial,
                    style: TextStyle(color: Colors.white),
                  ),
                  SizedBox(height: 10),
                  _buildRuleItem(S.of(context).workshopRuleNoAdult),
                  _buildRuleItem(S.of(context).workshopRuleNoOffensive),
                  _buildRuleItem(S.of(context).workshopRuleNoCopyright),
                  _buildRuleItem(S.of(context).workshopRuleNoAds),
                  _buildRuleItem(S.of(context).workshopRuleNoMisleading),
                ],
              ),
            ),
            actions: [
              TextButton(
                child: Text(S.of(context).confirm, style: TextStyle(color: Colors.white)),
                onPressed: () {
                  Navigator.of(context).pop(true);
                },
              ),
            ],
          );
        },
      );
      
      // 如果用户取消了上传，则返回
      if (confirmed != true) {
        return null;
      }
      
      // 记录已经显示过警告
      await prefs.setBool('has_shown_workshop_warning', true);
    }
    
    // 显示创意工坊物品信息表单
    // 如果是更新现有物品，使用现有物品的信息作为默认值
    String title = steamFile?.name ?? projectName;
    String description = steamFile?.description ?? '';
    String visibility = 'public';
    String? ageRating = steamFile?.ageRating?.value;
    
    // 显示物品信息表单对话框
    final bool? formConfirmed = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              backgroundColor: Colors.black87,
              title: Text(S.of(context).uploadWorkshop, style: TextStyle(color: Colors.white)),
              content: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 两列布局
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 左列
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // 标题
                              Text('${S.of(context).title} *${S.of(context).required}', style: TextStyle(color: Colors.white70)),
                              SizedBox(
                                width: double.infinity,
                                height: 40,
                                child: TextField(
                                  style: TextStyle(color: Colors.white),
                                  decoration: InputDecoration(
                                    hintText: '输入标题',
                                    hintStyle: TextStyle(color: Colors.white38),
                                    enabledBorder: UnderlineInputBorder(
                                      borderSide: BorderSide(color: Colors.white54),
                                    ),
                                    focusedBorder: UnderlineInputBorder(
                                      borderSide: BorderSide(color: Colors.blue),
                                    ),
                                    contentPadding: EdgeInsets.symmetric(vertical: 8.0),
                                  ),
                                  onChanged: (value) {
                                    title = value;
                                  },
                                  controller: TextEditingController(text: title),
                                ),
                              ),
                              SizedBox(height: 20),
                              
                              // 描述
                              Text(S.of(context).description, style: TextStyle(color: Colors.white70)),
                              SizedBox(
                                width: double.infinity,
                                height: 80,
                                child: TextField(
                                  style: TextStyle(color: Colors.white),
                                  maxLines: 3,
                                  decoration: InputDecoration(
                                    hintText: S.of(context).inputDescription,
                                    hintStyle: TextStyle(color: Colors.white38),
                                    enabledBorder: OutlineInputBorder(
                                      borderSide: BorderSide(color: Colors.white54),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderSide: BorderSide(color: Colors.blue),
                                    ),
                                    contentPadding: EdgeInsets.symmetric(vertical: 8.0, horizontal: 10.0),
                                  ),
                                  onChanged: (value) {
                                    description = value;
                                  },
                                ),
                              ),
                              SizedBox(height: 20),
                              
                              // 年龄分级 - 必选项
                              Row(
                                children: [
                                  Text(S.of(context).ageRating, style: TextStyle(color: Colors.white70)),
                                  Text(' *${S.of(context).required}', style: TextStyle(color: Colors.red)),
                                ],
                              ),
                              DropdownButton<String>(
                                dropdownColor: Colors.black87,
                                value: ageRating,
                                hint: Text(S.of(context).selectContentRating, style: TextStyle(color: Colors.white54)),
                                style: TextStyle(color: Colors.white),
                                underline: Container(height: 1, color: ageRating == null ? Colors.red : Colors.white54),
                                onChanged: (String? newValue) {
                                  if (newValue != null) {
                                    setState(() {
                                      ageRating = newValue;
                                    });
                                  }
                                },
                                items: TagAgeRating.values
                                    .map<DropdownMenuItem<String>>((TagAgeRating rating) {
                                  return DropdownMenuItem<String>(
                                    value: rating.value,
                                    child: Text(rating.value),
                                  );
                                }).toList(),
                              ),
                              SizedBox(height: 20),
                              
                              // 可见性
                              Text(S.of(context).visibility, style: TextStyle(color: Colors.white70)),
                              DropdownButton<String>(
                                dropdownColor: Colors.black87,
                                value: visibility,
                                style: TextStyle(color: Colors.white),
                                underline: Container(height: 1, color: Colors.white54),
                                onChanged: (String? newValue) {
                                  if (newValue != null) {
                                    setState(() {
                                      visibility = newValue;
                                    });
                                  }
                                },
                                items: <String>['public', 'friendsonly', 'private']
                                    .map<DropdownMenuItem<String>>((String value) {
                                  return DropdownMenuItem<String>(
                                    value: value,
                                    child: Text(value),
                                  );
                                }).toList(),
                              ),
                            ],
                          ),
                        ),
                        
                        SizedBox(width: 20), // 列之间的间距
                        
                        // 右列
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              
                              // 预览图片
                              Text('${S.of(context).previewImage} *${S.of(context).required}', style: TextStyle(color: Colors.white70)),
                              SizedBox(height: 10),
                              StatefulBuilder(
                                builder: (context, setPreviewState) {
                                  return Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                        width: 640, // 固定宽度
                                        height: 360, // 固定高度，保持16:9比例
                                        decoration: BoxDecoration(
                                          border: Border.all(color: Colors.white54),
                                          color: Colors.black45,
                                        ),
                                        child: selectedImageFile != null
                                          ? Image.file(
                                              selectedImageFile!,
                                              fit: BoxFit.cover,
                                            )
                                          : Center(child: Text(S.of(context).previewImageDefault, style: TextStyle(color: Colors.white))),
                                      ),
                                      SizedBox(height: 10),
                                      Row(
                                        children: [
                                          ElevatedButton(
                                            onPressed: () async {
                                              final result = await FilePicker.platform.pickFiles(
                                                type: FileType.image,
                                                allowMultiple: false,
                                              );
                                              
                                              if (result != null && result.files.isNotEmpty) {
                                                final file = File(result.files.first.path!);
                                                setPreviewState(() {
                                                  selectedImageFile = file;
                                                });
                                              }
                                            },
                                            child: Text(S.of(context).importImage),
                                          ),
                                          SizedBox(width: 10),
                                          if (selectedImageFile != null)
                                            ElevatedButton(
                                              onPressed: () {
                                                setPreviewState(() {
                                                  selectedImageFile = null;
                                                });
                                              },
                                              style: ElevatedButton.styleFrom(
                                                backgroundColor: Colors.red.shade800,
                                              ),
                                              child: Text(S.of(context).removeImage),
                                            ),
                                        ],
                                      ),
                                    ],
                                  );
                                },
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  child: Text(S.of(context).cancel, style: TextStyle(color: Colors.white)),
                  onPressed: () {
                    Navigator.of(context).pop(false);
                  },
                ),
                TextButton(
                  child: Text(S.of(context).publish, style: TextStyle(color: Colors.white)),
                  onPressed: () {
                    // 验证标题不能为空
                    if (title.trim().isEmpty) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text(S.of(context).titleCannotBeEmpty)),
                      );
                      return;
                    }
                    
                    // 验证年龄分级必须选择
                    if (ageRating == null) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text(S.of(context).selectContentRating)),
                      );
                      return;
                    }
                    
                    Navigator.of(context).pop(true);
                  },
                ),
              ],
            );
          },
        );
      },
    );
    
    // 如果用户取消了表单，则返回
    if (formConfirmed != true) {
      return null;
    }
    
    // 使用从steamFile获取的itemId
    int? itemId = steamFile?.id;
    final contentFolder = CreateGameView.getSavePath(projectName);
    final dir = Directory(contentFolder);
    
    // 确保文件夹存在
    if (!dir.existsSync()) {
      scaffoldMessenger.showSnackBar(
        SnackBar(content: Text('${S.of(context).fileNotFound}: $contentFolder')),
      );
      return null;
    }

    // 使用绝对路径创建临时目录
    final currentDir = Directory.current;
    final timestamp = DateFormat('yyyyMMdd_HHmmss').format(DateTime.now());
    final tempDir = Directory(path.join(currentDir.path, '../../temp/steamworkshopload_$timestamp'));
    if (!tempDir.existsSync()) {
      tempDir.createSync(recursive: true);
    }

    // 对于准备文件时的SnackBar使用预先缓存的本地化文本
    scaffoldMessenger.hideCurrentSnackBar();
    scaffoldMessenger.showSnackBar(
      SnackBar(
        content: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(preparingProjectFilesText),
            SizedBox(
              width: 20, 
              height: 20, 
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.deepPurple.shade700,
        duration: const Duration(days: 1), // 设置一个非常长的时间，确保它不会自动消失
      ),
    );

    // 复制所有文件到临时目录，不再更新内部的SnackBar
    List<File> filesToCopy = dir.listSync().whereType<File>().toList();
    int totalFiles = filesToCopy.length;
    int copiedFiles = 0;
    
    for (var file in filesToCopy) {
      await file.copy(path.join(tempDir.path, path.basename(file.path)));
      copiedFiles++;
      // 不再在此处更新SnackBar
    }

    // 文件复制完成，不再手动隐藏准备提示或添加延迟
    // 下一个 showSnackBar 调用会自动替换当前的

    File jsonFile = File('${tempDir.path}/flowchart.json');
    if (!jsonFile.existsSync()) {
      scaffoldMessenger.showSnackBar(
        SnackBar(content: Text('${S.of(context).fileNotFound}: $jsonFile')),
      );
      return null;
    }

    // 处理json文件中的path字段
    final jsonContent = jsonDecode(jsonFile.readAsStringSync());
    final paths = <String>[];    
    if (jsonContent is Map) {
      _extractPaths(jsonContent, paths);
    }

    // 去重和过滤空值
    final uniquePaths = paths.where((path) => path != null && path.isNotEmpty).toSet().toList();

    // 检查视频文件并添加其对应的字幕文件
    final completePathList = <String>[];
    for (final filePath in uniquePaths) {
      completePathList.add(filePath);
      
      // 检查是否是视频文件
      final extension = path.extension(filePath).toLowerCase();
      if (extension.isNotEmpty) {
        final ext = extension.substring(1);
        if (allowedVideoFormats.contains(ext)) {
          // 如果是视频文件，查找同路径同名的字幕文件
          final directory = path.dirname(filePath);
          final baseName = path.basenameWithoutExtension(filePath);
          
          // 检查每一种字幕格式
          for (final subtitleExt in allowedSubtitleFormats) {
            final subtitlePath = path.join(directory, '$baseName.$subtitleExt');
            final subtitleFile = File(subtitlePath);
            if (subtitleFile.existsSync()) {
              // 找到对应字幕文件，添加到列表
              completePathList.add(subtitlePath);
              print('添加视频对应字幕: $subtitlePath');
            }
          }
        }
      }
    }

    // 检查文件类型和存在性
    final validPaths = <String>[];
    for (final filePath in completePathList) {
      final file = File(filePath);
      if (!file.existsSync()) {
        scaffoldMessenger.showSnackBar(
          SnackBar(content: Text('${S.of(context).fileNotFound}: $filePath')),
        );
        break;
      }
      // 检查文件格式是否有效
      if (!_isValidFileFormat(filePath)) {
        scaffoldMessenger.showSnackBar(
          SnackBar(content: Text('${S.of(context).unsupportedFileFormat}: $filePath')),
        );
        continue;
      }
      validPaths.add(filePath);
    }
    
    // 如果没有有效路径，则返回
    if (validPaths.isEmpty) {
      scaffoldMessenger.showSnackBar(
        SnackBar(content: Text(S.of(context).noValidFilesFound)),
      );
      return null;
    }

    // 处理封面图片
    File previewFile = File('${tempDir.path}/preview.jpg');
    
    // 如果用户选择了图片，使用选择的图片作为封面
    if (selectedImageFile != null && selectedImageFile!.existsSync()) {
      // 复制选择的图片到临时目录
      await selectedImageFile!.copy(previewFile.path);
    } 
    // 如果没有选择图片，尝试使用默认图片
    else {
      // 如果没有默认图片，使用文件夹中的第一个jpg文件
      if (!previewFile.existsSync()) {
        for (var file in tempDir.listSync().whereType<File>()) {
          if (file.path.toLowerCase().endsWith('.jpg')) {
            previewFile = file;
            break;
          }
        }
      }

      // 如果仍然没有找到图片，使用应用程序的logo作为默认图片
      if (!previewFile.existsSync()) {
        final defaultImage = File('assets/images/logo.png');
        if (defaultImage.existsSync()) {
          await defaultImage.copy(previewFile.path);
        }
      }
    }

    // 创建文件名映射，处理同名文件
    final Map<String, String> fileNameMap = {};
    for (final filePath in validPaths) {
      final file = File(filePath);
      if (file.existsSync()) {
        String baseName = path.basename(filePath);
        String extension = path.extension(filePath);
        String nameWithoutExt = path.basenameWithoutExtension(filePath);
        String destFileName = baseName;
        int counter = 1;
        
        // 如果文件名已存在，添加数字后缀
        while (fileNameMap.containsValue(destFileName)) {
          destFileName = '$nameWithoutExt($counter)$extension';
          counter++;
        }
        
        final destPath = path.join(tempDir.path, destFileName);
        try {
          await file.copy(destPath);
        } catch (e) {
          // 检查是否是磁盘空间不足错误
          bool isDiskSpaceError = false;
          
          if (e is FileSystemException) {
            // 检查各种可能的磁盘空间不足错误表现形式
            isDiskSpaceError = e.toString().contains('磁盘空间不足') || 
                              e.toString().contains('disk space') || 
                              e.toString().contains('no space') ||
                              e.toString().contains('space left') ||
                              (e.osError?.errorCode == 112) || // Windows错误码
                              (e.osError?.errorCode == 28);   // Linux/Unix错误码
                              
            // 记录详细错误信息
            print('文件复制错误: ${e.message}');
            print('源路径: ${file.path}');
            print('目标路径: $destPath');
            if (e.osError != null) {
              print('系统错误码: ${e.osError?.errorCode}, 错误信息: ${e.osError?.message}');
            }
          }
          
          if (isDiskSpaceError) {
            // 显示对话框
            await showDialog(
              context: context,
              barrierDismissible: false,
              builder: (BuildContext dialogContext) {
                return AlertDialog(
                  backgroundColor: Colors.black87,
                  title: Text(S.of(dialogContext).uploadFailed, style: TextStyle(color: Colors.white)),
                  content: Text(
                    S.of(dialogContext).diskSpaceInsufficient,
                    style: TextStyle(color: Colors.white),
                  ),
                  actions: [
                    TextButton(
                      child: Text(S.of(dialogContext).confirm, style: TextStyle(color: Colors.white)),
                      onPressed: () {
                        Navigator.of(dialogContext).pop();
                      },
                    ),
                  ],
                );
              },
            );
            
            // 清理临时文件夹
            if (tempDir.existsSync()) {
              try {
                tempDir.deleteSync(recursive: true);
              } catch (cleanupError) {
                print('清理临时文件夹错误: $cleanupError');
              }
            }
            
            // 隐藏任何显示的SnackBar
            scaffoldMessenger.hideCurrentSnackBar();
            
            // 取消上传过程
            return null;
          }
          
          // 其他类型错误重新抛出
          rethrow;
        }
        fileNameMap[filePath] = destFileName;
      }
    }
    
    // 更新JSON文件中的路径引用
    if (jsonContent is Map) {
      // 递归更新JSON中的路径
      void _updateJsonPaths(dynamic json, Map<String, String> pathMap) {
        if (json is Map) {
          if (json.containsKey('path') && json['path'] is String) {
            final oldPath = json['path'];
            if (pathMap.containsKey(oldPath)) {
              json['path'] = pathMap[oldPath];
            }
          }
          for (final value in json.values) {
            _updateJsonPaths(value, pathMap);
          }
        } else if (json is List) {
          for (final item in json) {
            _updateJsonPaths(item, pathMap);
          }
        }
      }
      
      _updateJsonPaths(jsonContent, fileNameMap);
      // 使用JsonEncoder.withIndent保持原始格式化
      final encoder = JsonEncoder.withIndent('  ');
      await jsonFile.writeAsString(encoder.convert(jsonContent));
    }

    // 根据选择的可见性设置转换为Steam API的可见性枚举值
    ERemoteStoragePublishedFileVisibility visibilityEnum;
    switch (visibility) {
      case 'public':
        visibilityEnum = ERemoteStoragePublishedFileVisibility.public;
        break;
      case 'friendsonly':
        visibilityEnum = ERemoteStoragePublishedFileVisibility.friendsOnly;
        break;
      case 'private':
        visibilityEnum = ERemoteStoragePublishedFileVisibility.private;
        break;
      default:
        visibilityEnum = ERemoteStoragePublishedFileVisibility.public;
    }
    
    // Prepare tags for the workshop item
    final List<String> tags = [];
    
    // Add age rating as a tag (should never be null at this point due to validation)
    if (ageRating != null) {
      tags.add(ageRating!); // Using non-null assertion since we've checked it's not null
    }
    
    // 准备开始上传，显示上传进度条的初始状态
    // 不需要重复声明scaffoldMessenger，使用前面的引用
    scaffoldMessenger.hideCurrentSnackBar(); // 确保之前的提示已隐藏
    
    // 使用StreamController实时更新进度
    final uploadProgressController = StreamController<(EItemUpdateStatus, double)>.broadcast();
    
    // 创建固定的SnackBar，通过Stream更新内容
    final uploadSnackBar = SnackBar(
      content: StreamBuilder<(EItemUpdateStatus, double)>(
        stream: uploadProgressController.stream,
        initialData: (EItemUpdateStatus.preparingConfig, 0.0), // 设置初始状态为准备配置0%
        builder: (builderContext, snapshot) {
          // 获取当前状态和进度
          final status = snapshot.data?.$1 ?? EItemUpdateStatus.invalid;
          final progress = snapshot.data?.$2 ?? 0.0;
          
          // 根据状态更新文本内容，使用预先缓存的本地化文本
          String statusText = uploadingText;
          switch (status) {
            case EItemUpdateStatus.preparingConfig:
              statusText = preparingConfigText;
              break;
            case EItemUpdateStatus.preparingContent:
              statusText = uploadingPleaseWaitText;
              break;
            case EItemUpdateStatus.uploadingContent:
              statusText = uploadingContentText;
              break;
            case EItemUpdateStatus.uploadingPreviewFile:
              statusText = uploadingPreviewImageText;
              break;
            case EItemUpdateStatus.committingChanges:
              statusText = committingChangesText;
              break;
            default:
              break;
          }
          
          // 创建更美观的进度指示器
          return Stack(
            children: [
              // 背景进度条 - 使用渐变填充
              ClipRRect(
                borderRadius: BorderRadius.circular(8.0),
                child: Stack(
                  children: [
                    // 背景层 - 更浅的颜色
                    Container(
                      height: 40,
                      width: double.infinity,
                      color: Colors.blue.shade100, // 非常浅的蓝色作为底色
                    ),
                    // 进度层 - 使用预先获取的设备宽度而不是MediaQuery
                    Container(
                      height: 40,
                      width: deviceWidth * progress, // 使用预先记录的设备宽度
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Colors.blue.shade700,
                            Colors.lightBlue.shade500,
                          ],
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              
              // 文本内容，状态和百分比并排显示
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 10.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // 状态文本
                    Text(
                      statusText,
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        shadows: [
                          Shadow(
                            color: Colors.black54,
                            blurRadius: 2,
                            offset: Offset(1, 1),
                          ),
                        ],
                      ),
                    ),
                    // 百分比
                    Text(
                      '${(progress * 100).toStringAsFixed(1)}%',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        shadows: [
                          Shadow(
                            color: Colors.black54,
                            blurRadius: 2,
                            offset: Offset(1, 1),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          );
        }
      ),
      duration: Duration(days: 1), // 设置一个很长的时间，直到手动关闭
      backgroundColor: Colors.transparent, // 透明背景，让Stack内容可见
      padding: EdgeInsets.zero, // 移除默认内边距
    );
    
    // 显示上传进度SnackBar
    scaffoldMessenger.showSnackBar(uploadSnackBar);
    
    // 创建上传进度更新计时器
    Timer? progressTimer;
    
    // 打印状态日志用于调试
    void logUpdateStatus(int handle, EItemUpdateStatus status, double progress) {
      print('上传状态: $status, 进度: ${(progress * 100).toStringAsFixed(2)}%, Handle: $handle');
    }
    
    // 弱引用保存context
    bool isDisposed = false;
    
    // Capture context.mounted value for use in async callbacks
    bool wasContextMounted = context.mounted;
    
    // Create a local function for reward handling to pass the pre-captured text
    Future<void> handleRewardCoins() async {
      if (steamFile == null) {
        try {
          await _rewardCoinsForUpload(
            context: wasContextMounted ? context : null, 
            projectName: projectName,
            scaffoldMessenger: scaffoldMessenger,
            congratsText: congratsText
          );
        } catch (e) {
          print('奖励金币时出错: $e');
        }
      }
    }
    
    // Convert tags list to SteamParamStringArray
    final tagsArray = calloc<SteamParamStringArray>();
    final tagsPointers = calloc<Pointer<Utf8>>(tags.length);
    
    for (var i = 0; i < tags.length; i++) {
      tagsPointers[i] = tags[i].toNativeUtf8();
    }
    
    tagsArray.ref.strings = tagsPointers;
    tagsArray.ref.numStrings = tags.length;
    
    // 创建一个全局状态来跟踪上传进度
    // 这样即使编辑窗口关闭，主窗口仍能显示进度
    int? globalUploadHandle;
    
    // 在上传完成后清理进度条
    void cleanup() {
      // 标记为已释放
      isDisposed = true;
      
      // 取消进度更新计时器
      progressTimer?.cancel();
      progressTimer = null;
      
      // 关闭流
      if (!uploadProgressController.isClosed) {
        uploadProgressController.close();
      }
    }
    
    // 更改进度更新的方式，使用顶级定时器
    void startProgressMonitoring(int handle) {
      globalUploadHandle = handle;
      
      // 标记是否已经显示了完成消息
      bool hasShownCompletionMessage = false;
      
      // 创建一个顶级的定时器，不依赖于特定的widget context
      progressTimer = Timer.periodic(Duration(milliseconds: 500), (timer) async {
        // 如果已销毁且上传已完成，则停止监控
        if (isDisposed) {
          // 特别检查上传是否已完成
          final (status, progress) = await getItemUpdateProgress(handle);
          if (progress >= 1.0 || status == EItemUpdateStatus.invalid) {
            timer.cancel();
            return;
          }
          // 即使UI已销毁，也继续监控直到上传完成
        }
        
        try {
          // 使用正确的句柄获取上传进度
          final (status, progress) = await getItemUpdateProgress(handle);
          
          // 记录日志用于调试
          logUpdateStatus(handle, status, progress);
          
          // 通过Stream发送状态和进度更新，避免使用context
          if (!uploadProgressController.isClosed) {
            uploadProgressController.add((status, progress));
          }
          
          // 如果上传已完成，停止监控
          if (progress >= 1.0 || status == EItemUpdateStatus.invalid) {
            timer.cancel();
            
            // 如果UI仍然存在且尚未显示过完成消息，显示成功消息
            if (!isDisposed && !uploadProgressController.isClosed && !hasShownCompletionMessage) {
              hasShownCompletionMessage = true; // 标记已显示完成消息
              cleanup();
              try {
                scaffoldMessenger.hideCurrentSnackBar();
                scaffoldMessenger.showSnackBar(
                  SnackBar(
                    content: Text(updateSuccessText),
                    backgroundColor: Colors.green.shade700,
                    duration: Duration(seconds: 3),
                  ),
                );
              } catch (e) {
                print('显示上传成功提示时出错: $e');
              }
            }
          }
        } catch (e) {
          print('获取上传进度时出错: $e');
          // 只有在严重错误时才取消定时器
          if (e.toString().contains('invalid handle') || e.toString().contains('not found')) {
            timer.cancel();
          }
        }
      });
    }
    
    try {
      // 标记上传过程是否完成
      bool isUploadCompleted = false;
      
      final result = await SteamClient.instance.createItem(
        itemId: itemId,
        language: ApiLanguage.english,
        title: title,
        description: description,
        contentFolder: tempDir.path,
        previewImagePath: previewFile.path,
        visibility: visibilityEnum,
        metaData: jsonEncode(
          {
            'version': '1.0',
            'projectName': projectName,
            'includedFiles': validPaths,
            'ageRating': ageRating,
          },
        ),
        onUpdate: (handle) {
          // 获取实际的上传句柄
          globalUploadHandle = handle;
          
          // Set tags for the item
          steamUgc.setItemTags(handle, tagsArray, false);
          
          // 启动独立的进度监控
          startProgressMonitoring(handle);
        },
      );
      
      // 标记上传已完成
      isUploadCompleted = true;
      
      // 上传完成但进度监控可能仍在运行
      // 等待一小段时间让进度条达到100%
      await Future.delayed(Duration(seconds: 1));
      
      // 清理资源
      cleanup();
      
      // 处理金币奖励，使用预捕获的上下文信息
      await handleRewardCoins();

      // Free allocated memory
      for (var i = 0; i < tags.length; i++) {
        calloc.free(tagsPointers[i]);
      }
      calloc.free(tagsPointers);
      calloc.free(tagsArray);

      // 删除临时文件夹
      if (tempDir.existsSync()) {
        tempDir.deleteSync(recursive: true);
      }

      return result;
    } catch (e) {
      // 错误处理
      print('上传创意工坊时出错: $e');
      
      // 清理资源
      cleanup();
      
      // 尝试显示错误消息
      try {
        scaffoldMessenger.hideCurrentSnackBar();
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text(uploadFailedText + e.toString()),
            backgroundColor: Colors.red.shade700,
          ),
        );
      } catch (snackBarError) {
        print('显示错误消息时出错: $snackBarError');
      }
      
      // Free allocated memory
      for (var i = 0; i < tags.length; i++) {
        calloc.free(tagsPointers[i]);
      }
      calloc.free(tagsPointers);
      calloc.free(tagsArray);
      
      rethrow;
    }
  }

  /// 为上传创意工坊奖励金币
  Future<void> _rewardCoinsForUpload({
    BuildContext? context,
    required String projectName,
    required ScaffoldMessengerState scaffoldMessenger,
    required String congratsText
  }) async {
    try {
      // 使用项目名称作为唯一标识
      final projectId = 'workshop_upload_$projectName';
      
      // 检查是否已经奖励过金币
      final coinManager = CoinManager();
      final rewarded = await coinManager.rewardCoinsForProject(projectId);
      
      // 如果成功奖励（即首次上传），显示金币动画
      if (rewarded && context != null && context.mounted) {
        // 在Scaffold上显示金币动画
        final overlay = Overlay.of(context);
        late final OverlayEntry overlayEntry;
        
        // 播放音效（如果有的话）
        // 可以在这里添加金币音效播放代码
        
        overlayEntry = OverlayEntry(
          builder: (overlayContext) => Positioned.fill(
            child: Material(
              color: Colors.transparent,
              child: CoinAnimation(
                coinAmount: 10,
                onAnimationComplete: () {
                  // 动画完成后移除
                  Future.delayed(Duration(milliseconds: 500), () {
                    if (overlay.mounted) {
                      overlay.setState(() {
                        overlayEntry.remove();
                      });
                    }
                  });
                },
              ),
            ),
          ),
        );
        
        // 添加到Overlay
        overlay.insert(overlayEntry);
        
        // 显示提示消息 - 使用外部传入的ScaffoldMessengerState和文本
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text(congratsText),
            backgroundColor: Colors.amber.shade700,
            duration: Duration(seconds: 2),
          ),
        );
        
        print('成功为上传创意工坊奖励金币: projectId=$projectId');
      }
    } catch (e) {
      print('为上传创意工坊奖励金币时出错: $e');
    }
  }
  
  void _extractPaths(dynamic json, List<String> paths) {
    if (json is Map) {
      if (json.containsKey('path') && json['path'] is String) {
        paths.add(json['path']);
      }
      for (final value in json.values) {
        _extractPaths(value, paths);
      }
    } else if (json is List) {
      for (final item in json) {
        _extractPaths(item, paths);
      }
    }
  }

  /// 检查文件是否为允许的格式
  /// 允许的格式包括：视频格式、字幕格式、jpg和json
  bool _isValidFileFormat(String filePath) {
    // 获取文件扩展名并转换为小写
    final extension = path.extension(filePath).toLowerCase();
    if (extension.isEmpty) return false;
    
    // 移除扩展名开头的点
    final ext = extension.substring(1);
    
    // 允许的视频格式列表已移至全局变量
    
    // 允许的字幕格式列表已移至全局变量
    
    // 允许的图像格式
    final allowedImageFormats = ['jpg', 'jpeg'];
    
    // 允许的其他格式
    final allowedOtherFormats = ['json'];
    
    // 判断是否是允许的格式
    return allowedVideoFormats.contains(ext) || 
           allowedSubtitleFormats.contains(ext) ||
           allowedImageFormats.contains(ext) || 
           allowedOtherFormats.contains(ext);
  }

  openEulaUrl() {
    openUrl(
      'https://steamcommunity.com/sharedfiles/workshoplegalagreement?appid=${SteamClient.instance.appId}',
    );
  }

  openUGCItemUrl(int publishedFileId) {
    openUrl('steam://url/CommunityFilePage/$publishedFileId');
  }
  
  // 创建规则项目的UI组件
  Widget _buildRuleItem(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 6.0, right: 8.0),
            child: Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                color: Colors.blue,
                shape: BoxShape.circle,
              ),
            ),
          ),
          Expanded(
            child: Text(text, style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }
}

List<String> getSteamItemTags(Arena arena, int handle, int index) {
  final list = <String>[];
  final tags = SteamClient.instance.steamUgc.getQueryUgcNumTags(handle, index);
  for (var i = 0; i < tags; i++) {
    final tag = arena<Uint8>(255).cast<Utf8>();
    SteamClient.instance.steamUgc.getQueryUgcTag(handle, index, i, tag, 255);
    list.add(tag.toDartString());
  }
  return list;
}
