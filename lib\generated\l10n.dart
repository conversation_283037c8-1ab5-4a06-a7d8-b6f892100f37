// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class S {
  S();

  static S? _current;

  static S get current {
    assert(
      _current != null,
      'No instance of S was loaded. Try to initialize the S delegate before accessing S.current.',
    );
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<S> load(Locale locale) {
    final name =
        (locale.countryCode?.isEmpty ?? false)
            ? locale.languageCode
            : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = S();
      S._current = instance;

      return instance;
    });
  }

  static S of(BuildContext context) {
    final instance = S.maybeOf(context);
    assert(
      instance != null,
      'No instance of S present in the widget tree. Did you add S.delegate in localizationsDelegates?',
    );
    return instance!;
  }

  static S? maybeOf(BuildContext context) {
    return Localizations.of<S>(context, S);
  }

  /// `Interactive Video Engine`
  String get gameTitle {
    return Intl.message(
      'Interactive Video Engine',
      name: 'gameTitle',
      desc: '',
      args: [],
    );
  }

  /// `Settings`
  String get settings {
    return Intl.message('Settings', name: 'settings', desc: '', args: []);
  }

  /// `All Settings`
  String get allSettings {
    return Intl.message(
      'All Settings',
      name: 'allSettings',
      desc: '',
      args: [],
    );
  }

  /// `Credits`
  String get credits {
    return Intl.message('Credits', name: 'credits', desc: '', args: []);
  }

  /// `Creative & Planning`
  String get creditsCreativeTitle {
    return Intl.message(
      'Creative & Planning',
      name: 'creditsCreativeTitle',
      desc: '',
      args: [],
    );
  }

  /// `Game Producer`
  String get creditsGameProducer {
    return Intl.message(
      'Game Producer',
      name: 'creditsGameProducer',
      desc: '',
      args: [],
    );
  }

  /// `Creative Source`
  String get creditsCreativeSource {
    return Intl.message(
      'Creative Source',
      name: 'creditsCreativeSource',
      desc: '',
      args: [],
    );
  }

  /// `Software Planner`
  String get creditsSoftwarePlanner {
    return Intl.message(
      'Software Planner',
      name: 'creditsSoftwarePlanner',
      desc: '',
      args: [],
    );
  }

  /// `Gameplay Planner`
  String get creditsGameplayPlanner {
    return Intl.message(
      'Gameplay Planner',
      name: 'creditsGameplayPlanner',
      desc: '',
      args: [],
    );
  }

  /// `Programming`
  String get creditsProgrammingTitle {
    return Intl.message(
      'Programming',
      name: 'creditsProgrammingTitle',
      desc: '',
      args: [],
    );
  }

  /// `Software Architect`
  String get creditsSoftwareArchitect {
    return Intl.message(
      'Software Architect',
      name: 'creditsSoftwareArchitect',
      desc: '',
      args: [],
    );
  }

  /// `Code Writer`
  String get creditsCodeWriter {
    return Intl.message(
      'Code Writer',
      name: 'creditsCodeWriter',
      desc: '',
      args: [],
    );
  }

  /// `Visual Design`
  String get creditsVisualTitle {
    return Intl.message(
      'Visual Design',
      name: 'creditsVisualTitle',
      desc: '',
      args: [],
    );
  }

  /// `Art Designer`
  String get creditsArtDesigner {
    return Intl.message(
      'Art Designer',
      name: 'creditsArtDesigner',
      desc: '',
      args: [],
    );
  }

  /// `Animation Designer`
  String get creditsAnimationDesigner {
    return Intl.message(
      'Animation Designer',
      name: 'creditsAnimationDesigner',
      desc: '',
      args: [],
    );
  }

  /// `Marketing`
  String get creditsMarketingTitle {
    return Intl.message(
      'Marketing',
      name: 'creditsMarketingTitle',
      desc: '',
      args: [],
    );
  }

  /// `Video Producer`
  String get creditsVideoProducer {
    return Intl.message(
      'Video Producer',
      name: 'creditsVideoProducer',
      desc: '',
      args: [],
    );
  }

  /// `Copywriter`
  String get creditsCopywriter {
    return Intl.message(
      'Copywriter',
      name: 'creditsCopywriter',
      desc: '',
      args: [],
    );
  }

  /// `Thanks for playing my game!`
  String get creditsThanksTitle {
    return Intl.message(
      'Thanks for playing my game!',
      name: 'creditsThanksTitle',
      desc: '',
      args: [],
    );
  }

  /// `Special Thanks`
  String get creditsSpecialThanks {
    return Intl.message(
      'Special Thanks',
      name: 'creditsSpecialThanks',
      desc: '',
      args: [],
    );
  }

  /// `View teams and contributors involved in software development`
  String get creditsSubtitle {
    return Intl.message(
      'View teams and contributors involved in software development',
      name: 'creditsSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Open Game in New Window`
  String get openGameInNewWindow {
    return Intl.message(
      'Open Game in New Window',
      name: 'openGameInNewWindow',
      desc: '',
      args: [],
    );
  }

  /// `When enabled, open game projects in a new window, otherwise open them in the current window`
  String get openGameInNewWindowDesc {
    return Intl.message(
      'When enabled, open game projects in a new window, otherwise open them in the current window',
      name: 'openGameInNewWindowDesc',
      desc: '',
      args: [],
    );
  }

  /// `Public Mode/Live Streaming Mode`
  String get publicMode {
    return Intl.message(
      'Public Mode/Live Streaming Mode',
      name: 'publicMode',
      desc: '',
      args: [],
    );
  }

  /// `When enabled, the workshop interface will force the use of 'Everyone' content rating and not show NSFW tags`
  String get publicModeDesc {
    return Intl.message(
      'When enabled, the workshop interface will force the use of \'Everyone\' content rating and not show NSFW tags',
      name: 'publicModeDesc',
      desc: '',
      args: [],
    );
  }

  /// `Vertical video editing interface (recommended for high resolution screens)`
  String get verticalLayoutDescription {
    return Intl.message(
      'Vertical video editing interface (recommended for high resolution screens)',
      name: 'verticalLayoutDescription',
      desc: '',
      args: [],
    );
  }

  /// `Language`
  String get language {
    return Intl.message('Language', name: 'language', desc: '', args: []);
  }

  /// `Fullscreen Mode`
  String get fullscreen {
    return Intl.message(
      'Fullscreen Mode',
      name: 'fullscreen',
      desc: '',
      args: [],
    );
  }

  /// `Main Window Fullscreen`
  String get mainWindowFullscreen {
    return Intl.message(
      'Main Window Fullscreen',
      name: 'mainWindowFullscreen',
      desc: '',
      args: [],
    );
  }

  /// `Create Game`
  String get createNewGame {
    return Intl.message(
      'Create Game',
      name: 'createNewGame',
      desc: '',
      args: [],
    );
  }

  /// `Workshop`
  String get workshop {
    return Intl.message('Workshop', name: 'workshop', desc: '', args: []);
  }

  /// `Open Game`
  String get openGame {
    return Intl.message('Open Game', name: 'openGame', desc: '', args: []);
  }

  /// `Home`
  String get home {
    return Intl.message('Home', name: 'home', desc: '', args: []);
  }

  /// `Activity`
  String get activity {
    return Intl.message('Activity', name: 'activity', desc: '', args: []);
  }

  /// `Profile`
  String get profile {
    return Intl.message('Profile', name: 'profile', desc: '', args: []);
  }

  /// `Downloads`
  String get downloads {
    return Intl.message('Downloads', name: 'downloads', desc: '', args: []);
  }

  /// `Friends`
  String get friends {
    return Intl.message('Friends', name: 'friends', desc: '', args: []);
  }

  /// `Messages`
  String get messages {
    return Intl.message('Messages', name: 'messages', desc: '', args: []);
  }

  /// `Element Parameters`
  String get elementParams {
    return Intl.message(
      'Element Parameters',
      name: 'elementParams',
      desc: '',
      args: [],
    );
  }

  /// `Background Color`
  String get backgroundColor {
    return Intl.message(
      'Background Color',
      name: 'backgroundColor',
      desc: '',
      args: [],
    );
  }

  /// `Thickness`
  String get thickness {
    return Intl.message('Thickness', name: 'thickness', desc: '', args: []);
  }

  /// `Border Color`
  String get borderColor {
    return Intl.message(
      'Border Color',
      name: 'borderColor',
      desc: '',
      args: [],
    );
  }

  /// `Elevation`
  String get elevation {
    return Intl.message('Elevation', name: 'elevation', desc: '', args: []);
  }

  /// `Video Playback`
  String get videoPlayback {
    return Intl.message(
      'Video Playback',
      name: 'videoPlayback',
      desc: '',
      args: [],
    );
  }

  /// `Playback Control`
  String get playbackControl {
    return Intl.message(
      'Playback Control',
      name: 'playbackControl',
      desc: '',
      args: [],
    );
  }

  /// `Play`
  String get play {
    return Intl.message('Play', name: 'play', desc: '', args: []);
  }

  /// `Pause`
  String get pause {
    return Intl.message('Pause', name: 'pause', desc: '', args: []);
  }

  /// `Stop`
  String get stop {
    return Intl.message('Stop', name: 'stop', desc: '', args: []);
  }

  /// `Playback Rate`
  String get playbackRate {
    return Intl.message(
      'Playback Rate',
      name: 'playbackRate',
      desc: '',
      args: [],
    );
  }

  /// `Video Cover`
  String get videoCover {
    return Intl.message('Video Cover', name: 'videoCover', desc: '', args: []);
  }

  /// `Capture Current Frame`
  String get captureCurrentFrame {
    return Intl.message(
      'Capture Current Frame',
      name: 'captureCurrentFrame',
      desc: '',
      args: [],
    );
  }

  /// `Time Edit`
  String get timeEdit {
    return Intl.message('Time Edit', name: 'timeEdit', desc: '', args: []);
  }

  /// `Start Time`
  String get startTime {
    return Intl.message('Start Time', name: 'startTime', desc: '', args: []);
  }

  /// `End Time`
  String get endTime {
    return Intl.message('End Time', name: 'endTime', desc: '', args: []);
  }

  /// `Set Current Time`
  String get setCurrentTime {
    return Intl.message(
      'Set Current Time',
      name: 'setCurrentTime',
      desc: '',
      args: [],
    );
  }

  /// `Add Video`
  String get addVideo {
    return Intl.message('Add Video', name: 'addVideo', desc: '', args: []);
  }

  /// `Add Diamond`
  String get addDiamond {
    return Intl.message('Add Diamond', name: 'addDiamond', desc: '', args: []);
  }

  /// `Add Rectangle`
  String get addRectangle {
    return Intl.message(
      'Add Rectangle',
      name: 'addRectangle',
      desc: '',
      args: [],
    );
  }

  /// `Add Resizable Rectangle`
  String get addResizableRectangle {
    return Intl.message(
      'Add Resizable Rectangle',
      name: 'addResizableRectangle',
      desc: '',
      args: [],
    );
  }

  /// `Add Oval`
  String get addOval {
    return Intl.message('Add Oval', name: 'addOval', desc: '', args: []);
  }

  /// `Add Parallelogram`
  String get addParallelogram {
    return Intl.message(
      'Add Parallelogram',
      name: 'addParallelogram',
      desc: '',
      args: [],
    );
  }

  /// `Add Hexagon`
  String get addHexagon {
    return Intl.message('Add Hexagon', name: 'addHexagon', desc: '', args: []);
  }

  /// `Add Storage`
  String get addStorage {
    return Intl.message('Add Storage', name: 'addStorage', desc: '', args: []);
  }

  /// `Add Image`
  String get addImage {
    return Intl.message('Add Image', name: 'addImage', desc: '', args: []);
  }

  /// `Remove All`
  String get removeAll {
    return Intl.message('Remove All', name: 'removeAll', desc: '', args: []);
  }

  /// `Save Flowchart`
  String get saveFlowchart {
    return Intl.message(
      'Save Flowchart',
      name: 'saveFlowchart',
      desc: '',
      args: [],
    );
  }

  /// `Load Flowchart`
  String get loadFlowchart {
    return Intl.message(
      'Load Flowchart',
      name: 'loadFlowchart',
      desc: '',
      args: [],
    );
  }

  /// `Workshop Items`
  String get workshopItems {
    return Intl.message(
      'Workshop Items',
      name: 'workshopItems',
      desc: '',
      args: [],
    );
  }

  /// `Loading Failed`
  String get loadingFailed {
    return Intl.message(
      'Loading Failed',
      name: 'loadingFailed',
      desc: '',
      args: [],
    );
  }

  /// `Click to Retry`
  String get retryLoading {
    return Intl.message(
      'Click to Retry',
      name: 'retryLoading',
      desc: '',
      args: [],
    );
  }

  /// `Open Game`
  String get openGameTitle {
    return Intl.message('Open Game', name: 'openGameTitle', desc: '', args: []);
  }

  /// `Show Controller`
  String get showController {
    return Intl.message(
      'Show Controller',
      name: 'showController',
      desc: '',
      args: [],
    );
  }

  /// `Hide Controller`
  String get hideController {
    return Intl.message(
      'Hide Controller',
      name: 'hideController',
      desc: '',
      args: [],
    );
  }

  /// `My Games`
  String get myGames {
    return Intl.message('My Games', name: 'myGames', desc: '', args: []);
  }

  /// `Popular Games`
  String get popularGames {
    return Intl.message(
      'Popular Games',
      name: 'popularGames',
      desc: '',
      args: [],
    );
  }

  /// `News`
  String get news {
    return Intl.message('News', name: 'news', desc: '', args: []);
  }

  /// `Recently Played`
  String get recentlyPlayed {
    return Intl.message(
      'Recently Played',
      name: 'recentlyPlayed',
      desc: '',
      args: [],
    );
  }

  /// `Recently Edited`
  String get recentlyEdited {
    return Intl.message(
      'Recently Edited',
      name: 'recentlyEdited',
      desc: '',
      args: [],
    );
  }

  /// `Achievement Display`
  String get achievementDisplay {
    return Intl.message(
      'Achievement Display',
      name: 'achievementDisplay',
      desc: '',
      args: [],
    );
  }

  /// `Achievements`
  String get achievements {
    return Intl.message(
      'Achievements',
      name: 'achievements',
      desc: '',
      args: [],
    );
  }

  /// `Screenshots`
  String get screenshots {
    return Intl.message('Screenshots', name: 'screenshots', desc: '', args: []);
  }

  /// `Player`
  String get player {
    return Intl.message('Player', name: 'player', desc: '', args: []);
  }

  /// `Games`
  String get games {
    return Intl.message('Games', name: 'games', desc: '', args: []);
  }

  /// `Search`
  String get search {
    return Intl.message('Search', name: 'search', desc: '', args: []);
  }

  /// `Modify Time and Cover`
  String get modifyTimeAndCover {
    return Intl.message(
      'Modify Time and Cover',
      name: 'modifyTimeAndCover',
      desc: '',
      args: [],
    );
  }

  /// `Delete`
  String get delete {
    return Intl.message('Delete', name: 'delete', desc: '', args: []);
  }

  /// `Remove All Connections`
  String get removeAllConnections {
    return Intl.message(
      'Remove All Connections',
      name: 'removeAllConnections',
      desc: '',
      args: [],
    );
  }

  /// `Toggle Connectable`
  String get toggleConnectable {
    return Intl.message(
      'Toggle Connectable',
      name: 'toggleConnectable',
      desc: '',
      args: [],
    );
  }

  /// `Toggle Resizable`
  String get toggleResizable {
    return Intl.message(
      'Toggle Resizable',
      name: 'toggleResizable',
      desc: '',
      args: [],
    );
  }

  /// `Segmented`
  String get segmented {
    return Intl.message('Segmented', name: 'segmented', desc: '', args: []);
  }

  /// `Curved`
  String get curved {
    return Intl.message('Curved', name: 'curved', desc: '', args: []);
  }

  /// `Rectangular`
  String get rectangular {
    return Intl.message('Rectangular', name: 'rectangular', desc: '', args: []);
  }

  /// `Start`
  String get startPoint {
    return Intl.message('Start', name: 'startPoint', desc: '', args: []);
  }

  /// `Loading Failed, Click to Retry`
  String get loadingFailedRetry {
    return Intl.message(
      'Loading Failed, Click to Retry',
      name: 'loadingFailedRetry',
      desc: '',
      args: [],
    );
  }

  /// `Flowchart missing start element`
  String get flowchartMissingStart {
    return Intl.message(
      'Flowchart missing start element',
      name: 'flowchartMissingStart',
      desc: '',
      args: [],
    );
  }

  /// `Video Time and Cover`
  String get videoTimeAndCover {
    return Intl.message(
      'Video Time and Cover',
      name: 'videoTimeAndCover',
      desc: '',
      args: [],
    );
  }

  /// `Cover Load Failed`
  String get coverLoadFailed {
    return Intl.message(
      'Cover Load Failed',
      name: 'coverLoadFailed',
      desc: '',
      args: [],
    );
  }

  /// `Close`
  String get close {
    return Intl.message('Close', name: 'close', desc: '', args: []);
  }

  /// `Confirm`
  String get confirm {
    return Intl.message('Confirm', name: 'confirm', desc: '', args: []);
  }

  /// `Play/Pause`
  String get playPause {
    return Intl.message('Play/Pause', name: 'playPause', desc: '', args: []);
  }

  /// `Invalid Start Time`
  String get invalidStartTime {
    return Intl.message(
      'Invalid Start Time',
      name: 'invalidStartTime',
      desc: '',
      args: [],
    );
  }

  /// `Invalid End Time`
  String get invalidEndTime {
    return Intl.message(
      'Invalid End Time',
      name: 'invalidEndTime',
      desc: '',
      args: [],
    );
  }

  /// `Playback Status`
  String get playbackStatus {
    return Intl.message(
      'Playback Status',
      name: 'playbackStatus',
      desc: '',
      args: [],
    );
  }

  /// `Current Position`
  String get currentPosition {
    return Intl.message(
      'Current Position',
      name: 'currentPosition',
      desc: '',
      args: [],
    );
  }

  /// `Volume Level`
  String get volume {
    return Intl.message('Volume Level', name: 'volume', desc: '', args: []);
  }

  /// `Completion Status`
  String get completed {
    return Intl.message(
      'Completion Status',
      name: 'completed',
      desc: '',
      args: [],
    );
  }

  /// `Total Duration`
  String get duration {
    return Intl.message('Total Duration', name: 'duration', desc: '', args: []);
  }

  /// `Playback Rate`
  String get rate {
    return Intl.message('Playback Rate', name: 'rate', desc: '', args: []);
  }

  /// `Playback Progress`
  String get playbackProgress {
    return Intl.message(
      'Playback Progress',
      name: 'playbackProgress',
      desc: '',
      args: [],
    );
  }

  /// `Volume Control`
  String get volumeControl {
    return Intl.message(
      'Volume Control',
      name: 'volumeControl',
      desc: '',
      args: [],
    );
  }

  /// `Audio Track`
  String get audioTrack {
    return Intl.message('Audio Track', name: 'audioTrack', desc: '', args: []);
  }

  /// `Subtitle`
  String get subtitle {
    return Intl.message('Subtitle', name: 'subtitle', desc: '', args: []);
  }

  /// `No Subtitle`
  String get noSubtitle {
    return Intl.message('No Subtitle', name: 'noSubtitle', desc: '', args: []);
  }

  /// `Close Subtitle`
  String get closeSubtitle {
    return Intl.message(
      'Close Subtitle',
      name: 'closeSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Modify Chapter Title`
  String get modifyChapterTitle {
    return Intl.message(
      'Modify Chapter Title',
      name: 'modifyChapterTitle',
      desc: '',
      args: [],
    );
  }

  /// `Set Branch Type and Parameters`
  String get setBranchParams {
    return Intl.message(
      'Set Branch Type and Parameters',
      name: 'setBranchParams',
      desc: '',
      args: [],
    );
  }

  /// `No AudioTrack`
  String get noAudioTrack {
    return Intl.message(
      'No AudioTrack',
      name: 'noAudioTrack',
      desc: '',
      args: [],
    );
  }

  /// `Enter Project Name`
  String get enterProjectName {
    return Intl.message(
      'Enter Project Name',
      name: 'enterProjectName',
      desc: '',
      args: [],
    );
  }

  /// `Project Name`
  String get projectNameHint {
    return Intl.message(
      'Project Name',
      name: 'projectNameHint',
      desc: '',
      args: [],
    );
  }

  /// `Cancel`
  String get cancel {
    return Intl.message('Cancel', name: 'cancel', desc: '', args: []);
  }

  /// `Project Created Successfully`
  String get projectCreated {
    return Intl.message(
      'Project Created Successfully',
      name: 'projectCreated',
      desc: '',
      args: [],
    );
  }

  /// `File Not Found`
  String get fileNotFound {
    return Intl.message(
      'File Not Found',
      name: 'fileNotFound',
      desc: '',
      args: [],
    );
  }

  /// `Upload`
  String get upload {
    return Intl.message('Upload', name: 'upload', desc: '', args: []);
  }

  /// `Upload to Workshop`
  String get uploadWorkshop {
    return Intl.message(
      'Upload to Workshop',
      name: 'uploadWorkshop',
      desc: '',
      args: [],
    );
  }

  /// `Import Image`
  String get importImage {
    return Intl.message(
      'Import Image',
      name: 'importImage',
      desc: '',
      args: [],
    );
  }

  /// `Remove Image`
  String get removeImage {
    return Intl.message(
      'Remove Image',
      name: 'removeImage',
      desc: '',
      args: [],
    );
  }

  /// `Default`
  String get previewImageDefault {
    return Intl.message(
      'Default',
      name: 'previewImageDefault',
      desc: '',
      args: [],
    );
  }

  /// `Age Rating`
  String get ageRating {
    return Intl.message('Age Rating', name: 'ageRating', desc: '', args: []);
  }

  /// `Visibility`
  String get visibility {
    return Intl.message('Visibility', name: 'visibility', desc: '', args: []);
  }

  /// `Upload Now`
  String get uploadNow {
    return Intl.message('Upload Now', name: 'uploadNow', desc: '', args: []);
  }

  /// `Uploading`
  String get uploading {
    return Intl.message('Uploading', name: 'uploading', desc: '', args: []);
  }

  /// `Upload Failed`
  String get uploadFailed {
    return Intl.message(
      'Upload Failed',
      name: 'uploadFailed',
      desc: '',
      args: [],
    );
  }

  /// `Upload Success`
  String get uploadSuccess {
    return Intl.message(
      'Upload Success',
      name: 'uploadSuccess',
      desc: '',
      args: [],
    );
  }

  /// `Download`
  String get download {
    return Intl.message('Download', name: 'download', desc: '', args: []);
  }

  /// `Download from Workshop`
  String get downloadWorkshop {
    return Intl.message(
      'Download from Workshop',
      name: 'downloadWorkshop',
      desc: '',
      args: [],
    );
  }

  /// `Downloading`
  String get downloading {
    return Intl.message('Downloading', name: 'downloading', desc: '', args: []);
  }

  /// `Download Failed`
  String get downloadFailed {
    return Intl.message(
      'Download Failed',
      name: 'downloadFailed',
      desc: '',
      args: [],
    );
  }

  /// `Download Success`
  String get downloadSuccess {
    return Intl.message(
      'Download Success',
      name: 'downloadSuccess',
      desc: '',
      args: [],
    );
  }

  /// `Invalid File Type`
  String get invalidFileType {
    return Intl.message(
      'Invalid File Type',
      name: 'invalidFileType',
      desc: '',
      args: [],
    );
  }

  /// `Unsupported file format, workshop only allows video, jpg and json files`
  String get unsupportedFileFormat {
    return Intl.message(
      'Unsupported file format, workshop only allows video, jpg and json files',
      name: 'unsupportedFileFormat',
      desc: '',
      args: [],
    );
  }

  /// `No valid files found for upload, please ensure your project includes supported files only`
  String get noValidFilesFound {
    return Intl.message(
      'No valid files found for upload, please ensure your project includes supported files only',
      name: 'noValidFilesFound',
      desc: '',
      args: [],
    );
  }

  /// `Search in the workshop`
  String get SearchWorkshop {
    return Intl.message(
      'Search in the workshop',
      name: 'SearchWorkshop',
      desc: '',
      args: [],
    );
  }

  /// `Style`
  String get style {
    return Intl.message('Style', name: 'style', desc: '', args: []);
  }

  /// `Ancient Chinese`
  String get AncientChinese {
    return Intl.message(
      'Ancient Chinese',
      name: 'AncientChinese',
      desc: '',
      args: [],
    );
  }

  /// `Shape`
  String get shape {
    return Intl.message('Shape', name: 'shape', desc: '', args: []);
  }

  /// `Game Editor`
  String get ilpEditor {
    return Intl.message('Game Editor', name: 'ilpEditor', desc: '', args: []);
  }

  /// `Steam Limited Account`
  String get steamLimitedAccount {
    return Intl.message(
      'Steam Limited Account',
      name: 'steamLimitedAccount',
      desc: '',
      args: [],
    );
  }

  /// `Sort`
  String get sort {
    return Intl.message('Sort', name: 'sort', desc: '', args: []);
  }

  /// `Join Discord to discussion`
  String get joinDiscord {
    return Intl.message(
      'Join Discord to discussion',
      name: 'joinDiscord',
      desc: '',
      args: [],
    );
  }

  /// `Back`
  String get back {
    return Intl.message('Back', name: 'back', desc: '', args: []);
  }

  /// `No`
  String get no {
    return Intl.message('No', name: 'no', desc: '', args: []);
  }

  /// `Resume`
  String get resume {
    return Intl.message('Resume', name: 'resume', desc: '', args: []);
  }

  /// `Retry`
  String get retry {
    return Intl.message('Retry', name: 'retry', desc: '', args: []);
  }

  /// `Seed`
  String get seed {
    return Intl.message('Seed', name: 'seed', desc: '', args: []);
  }

  /// `About`
  String get about {
    return Intl.message('About', name: 'about', desc: '', args: []);
  }

  /// `Exit`
  String get exit {
    return Intl.message('Exit', name: 'exit', desc: '', args: []);
  }

  /// `Are you sure you want to exit the application?`
  String get confirmExitApp {
    return Intl.message(
      'Are you sure you want to exit the application?',
      name: 'confirmExitApp',
      desc: '',
      args: [],
    );
  }

  /// `Challenges`
  String get challenge {
    return Intl.message('Challenges', name: 'challenge', desc: '', args: []);
  }

  /// `Gallery`
  String get gallery {
    return Intl.message('Gallery', name: 'gallery', desc: '', args: []);
  }

  /// `Start challenge`
  String get steamChallenge {
    return Intl.message(
      'Start challenge',
      name: 'steamChallenge',
      desc: '',
      args: [],
    );
  }

  /// `Steam workshop gallery`
  String get steamGallery {
    return Intl.message(
      'Steam workshop gallery',
      name: 'steamGallery',
      desc: '',
      args: [],
    );
  }

  /// `Subscribe`
  String get Subscribe {
    return Intl.message('Subscribe', name: 'Subscribe', desc: '', args: []);
  }

  /// `Subscribe & download`
  String get SubscribeAndDownload {
    return Intl.message(
      'Subscribe & download',
      name: 'SubscribeAndDownload',
      desc: '',
      args: [],
    );
  }

  /// `Click to subscribe and download`
  String get clickToSubscribeAndDownload {
    return Intl.message(
      'Click to subscribe and download',
      name: 'clickToSubscribeAndDownload',
      desc: '',
      args: [],
    );
  }

  /// `Create challenge`
  String get createChallenge {
    return Intl.message(
      'Create challenge',
      name: 'createChallenge',
      desc: '',
      args: [],
    );
  }

  /// `Challenge name`
  String get challengeName {
    return Intl.message(
      'Challenge name',
      name: 'challengeName',
      desc: '',
      args: [],
    );
  }

  /// `Description`
  String get ilpDesc {
    return Intl.message('Description', name: 'ilpDesc', desc: '', args: []);
  }

  /// `Everyone`
  String get Everyone {
    return Intl.message('Everyone', name: 'Everyone', desc: '', args: []);
  }

  /// `NSFW`
  String get NSFW {
    return Intl.message('NSFW', name: 'NSFW', desc: '', args: []);
  }

  /// `Public`
  String get public {
    return Intl.message('Public', name: 'public', desc: '', args: []);
  }

  /// `Friends Only`
  String get friendsonly {
    return Intl.message(
      'Friends Only',
      name: 'friendsonly',
      desc: '',
      args: [],
    );
  }

  /// `Private`
  String get private {
    return Intl.message('Private', name: 'private', desc: '', args: []);
  }

  /// `Anime`
  String get Anime {
    return Intl.message('Anime', name: 'Anime', desc: '', args: []);
  }

  /// `Realistic`
  String get Realistic {
    return Intl.message('Realistic', name: 'Realistic', desc: '', args: []);
  }

  /// `Pixel`
  String get Pixel {
    return Intl.message('Pixel', name: 'Pixel', desc: '', args: []);
  }

  // skipped getter for the 'Ancient Chinese' key

  /// `Other`
  String get Other {
    return Intl.message('Other', name: 'Other', desc: '', args: []);
  }

  /// `Landscape`
  String get Landscape {
    return Intl.message('Landscape', name: 'Landscape', desc: '', args: []);
  }

  /// `Portrait`
  String get Portrait {
    return Intl.message('Portrait', name: 'Portrait', desc: '', args: []);
  }

  /// `Square`
  String get Square {
    return Intl.message('Square', name: 'Square', desc: '', args: []);
  }

  /// `Already reduced one wrong answer`
  String get puzzleHint {
    return Intl.message(
      'Already reduced one wrong answer',
      name: 'puzzleHint',
      desc: '',
      args: [],
    );
  }

  /// `Steam Workshop`
  String get steamWorkshop {
    return Intl.message(
      'Steam Workshop',
      name: 'steamWorkshop',
      desc: '',
      args: [],
    );
  }

  /// `Author's Other Files`
  String get steamAuthorOtherFiles {
    return Intl.message(
      'Author\'s Other Files',
      name: 'steamAuthorOtherFiles',
      desc: '',
      args: [],
    );
  }

  /// `Publish Time`
  String get publishTime {
    return Intl.message(
      'Publish Time',
      name: 'publishTime',
      desc: '',
      args: [],
    );
  }

  /// `Update Time`
  String get updateTime {
    return Intl.message('Update Time', name: 'updateTime', desc: '', args: []);
  }

  /// `Please make sure you are over 18 years old`
  String get adultAgreementTitle {
    return Intl.message(
      'Please make sure you are over 18 years old',
      name: 'adultAgreementTitle',
      desc: '',
      args: [],
    );
  }

  /// `The Steam Workshop contains co created content from players worldwide, which may involve content that is not suitable for viewing in public places.`
  String get adultAgreementContent {
    return Intl.message(
      'The Steam Workshop contains co created content from players worldwide, which may involve content that is not suitable for viewing in public places.',
      name: 'adultAgreementContent',
      desc: '',
      args: [],
    );
  }

  /// `Unsubscribed`
  String get Unsubscribed {
    return Intl.message(
      'Unsubscribed',
      name: 'Unsubscribed',
      desc: '',
      args: [],
    );
  }

  /// `Item not subscribed, it will start downloading automatically after subscription`
  String get itemNotSubscribed {
    return Intl.message(
      'Item not subscribed, it will start downloading automatically after subscription',
      name: 'itemNotSubscribed',
      desc: '',
      args: [],
    );
  }

  /// `Subscribe success, downloading...`
  String get subscribeSuccess {
    return Intl.message(
      'Subscribe success, downloading...',
      name: 'subscribeSuccess',
      desc: '',
      args: [],
    );
  }

  /// `Subscribe failed: `
  String get subscribeFailed {
    return Intl.message(
      'Subscribe failed: ',
      name: 'subscribeFailed',
      desc: '',
      args: [],
    );
  }

  /// `Downloading, please wait`
  String get downloadingPleaseWait {
    return Intl.message(
      'Downloading, please wait',
      name: 'downloadingPleaseWait',
      desc: '',
      args: [],
    );
  }

  /// `Subscribed`
  String get Subscribed {
    return Intl.message('Subscribed', name: 'Subscribed', desc: '', args: []);
  }

  /// `By Subscribers`
  String get sortBySubscribers {
    return Intl.message(
      'By Subscribers',
      name: 'sortBySubscribers',
      desc: '',
      args: [],
    );
  }

  /// `By Votes`
  String get sortByVote {
    return Intl.message('By Votes', name: 'sortByVote', desc: '', args: []);
  }

  /// `By Favorites`
  String get sortByFavorites {
    return Intl.message(
      'By Favorites',
      name: 'sortByFavorites',
      desc: '',
      args: [],
    );
  }

  /// `By Publish Date`
  String get sortByPublishDate {
    return Intl.message(
      'By Publish Date',
      name: 'sortByPublishDate',
      desc: '',
      args: [],
    );
  }

  /// `By Update Date`
  String get sortByUpdateDate {
    return Intl.message(
      'By Update Date',
      name: 'sortByUpdateDate',
      desc: '',
      args: [],
    );
  }

  /// `Sort By`
  String get sortBy {
    return Intl.message('Sort By', name: 'sortBy', desc: '', args: []);
  }

  /// `Toggle Sort Direction`
  String get toggleSortDirection {
    return Intl.message(
      'Toggle Sort Direction',
      name: 'toggleSortDirection',
      desc: '',
      args: [],
    );
  }

  /// `Auto Fullscreen Video`
  String get autoFullScreenVideo {
    return Intl.message(
      'Auto Fullscreen Video',
      name: 'autoFullScreenVideo',
      desc: '',
      args: [],
    );
  }

  /// `New Window Fullscreen`
  String get newWindowFullScreen {
    return Intl.message(
      'New Window Fullscreen',
      name: 'newWindowFullScreen',
      desc: '',
      args: [],
    );
  }

  /// `When opening a new window, make it fullscreen automatically`
  String get newWindowFullScreenDescription {
    return Intl.message(
      'When opening a new window, make it fullscreen automatically',
      name: 'newWindowFullScreenDescription',
      desc: '',
      args: [],
    );
  }

  /// `File preparing, please try again later`
  String get filePreparingPleaseRetry {
    return Intl.message(
      'File preparing, please try again later',
      name: 'filePreparingPleaseRetry',
      desc: '',
      args: [],
    );
  }

  /// `'remote' is a reserved word, please use a different project name`
  String get remoteReservedWord {
    return Intl.message(
      '\'remote\' is a reserved word, please use a different project name',
      name: 'remoteReservedWord',
      desc: '',
      args: [],
    );
  }

  /// `Workshop Rules`
  String get workshopRules {
    return Intl.message(
      'Workshop Rules',
      name: 'workshopRules',
      desc: '',
      args: [],
    );
  }

  /// `Before submitting interactive videos to the creative workshop, please ensure that they do not violate Steam's terms of service, otherwise the interactive videos will be removed:`
  String get workshopRulesDescription {
    return Intl.message(
      'Before submitting interactive videos to the creative workshop, please ensure that they do not violate Steam\'s terms of service, otherwise the interactive videos will be removed:',
      name: 'workshopRulesDescription',
      desc: '',
      args: [],
    );
  }

  /// `Especially interactive videos, preview images and descriptions should follow these rules:`
  String get workshopRulesSpecial {
    return Intl.message(
      'Especially interactive videos, preview images and descriptions should follow these rules:',
      name: 'workshopRulesSpecial',
      desc: '',
      args: [],
    );
  }

  /// `No photographic or realistic pornographic or nude content`
  String get workshopRuleNoAdult {
    return Intl.message(
      'No photographic or realistic pornographic or nude content',
      name: 'workshopRuleNoAdult',
      desc: '',
      args: [],
    );
  }

  /// `No offensive content or violent, bloody content`
  String get workshopRuleNoOffensive {
    return Intl.message(
      'No offensive content or violent, bloody content',
      name: 'workshopRuleNoOffensive',
      desc: '',
      args: [],
    );
  }

  /// `No copyright infringement`
  String get workshopRuleNoCopyright {
    return Intl.message(
      'No copyright infringement',
      name: 'workshopRuleNoCopyright',
      desc: '',
      args: [],
    );
  }

  /// `No advertisements`
  String get workshopRuleNoAds {
    return Intl.message(
      'No advertisements',
      name: 'workshopRuleNoAds',
      desc: '',
      args: [],
    );
  }

  /// `No misleading preview images`
  String get workshopRuleNoMisleading {
    return Intl.message(
      'No misleading preview images',
      name: 'workshopRuleNoMisleading',
      desc: '',
      args: [],
    );
  }

  /// `Archive`
  String get archive {
    return Intl.message('Archive', name: 'archive', desc: '', args: []);
  }

  /// `Flowchart`
  String get flowchart {
    return Intl.message('Flowchart', name: 'flowchart', desc: '', args: []);
  }

  /// `START GAME`
  String get startGame {
    return Intl.message('START GAME', name: 'startGame', desc: '', args: []);
  }

  /// `View Flowchart`
  String get viewFlowchart {
    return Intl.message(
      'View Flowchart',
      name: 'viewFlowchart',
      desc: '',
      args: [],
    );
  }

  /// `Node Details`
  String get nodeDetails {
    return Intl.message(
      'Node Details',
      name: 'nodeDetails',
      desc: '',
      args: [],
    );
  }

  /// `ID`
  String get elementId {
    return Intl.message('ID', name: 'elementId', desc: '', args: []);
  }

  /// `Type`
  String get elementType {
    return Intl.message('Type', name: 'elementType', desc: '', args: []);
  }

  /// `Text`
  String get elementText {
    return Intl.message('Text', name: 'elementText', desc: '', args: []);
  }

  /// `Start Node`
  String get startNode {
    return Intl.message('Start Node', name: 'startNode', desc: '', args: []);
  }

  /// `Video Node`
  String get videoNode {
    return Intl.message('Video Node', name: 'videoNode', desc: '', args: []);
  }

  /// `Rectangle Node`
  String get rectangleNode {
    return Intl.message(
      'Rectangle Node',
      name: 'rectangleNode',
      desc: '',
      args: [],
    );
  }

  /// `Diamond Node`
  String get diamondNode {
    return Intl.message(
      'Diamond Node',
      name: 'diamondNode',
      desc: '',
      args: [],
    );
  }

  /// `Storage Node`
  String get storageNode {
    return Intl.message(
      'Storage Node',
      name: 'storageNode',
      desc: '',
      args: [],
    );
  }

  /// `Oval Node`
  String get ovalNode {
    return Intl.message('Oval Node', name: 'ovalNode', desc: '', args: []);
  }

  /// `Parallelogram Node`
  String get parallelogramNode {
    return Intl.message(
      'Parallelogram Node',
      name: 'parallelogramNode',
      desc: '',
      args: [],
    );
  }

  /// `Hexagon Node`
  String get hexagonNode {
    return Intl.message(
      'Hexagon Node',
      name: 'hexagonNode',
      desc: '',
      args: [],
    );
  }

  /// `Image Node`
  String get imageNode {
    return Intl.message('Image Node', name: 'imageNode', desc: '', args: []);
  }

  /// `Unknown Type`
  String get unknownNodeType {
    return Intl.message(
      'Unknown Type',
      name: 'unknownNodeType',
      desc: '',
      args: [],
    );
  }

  /// `FLOWCHART.json file not found`
  String get flowchartFileNotFound {
    return Intl.message(
      'FLOWCHART.json file not found',
      name: 'flowchartFileNotFound',
      desc: '',
      args: [],
    );
  }

  /// `Error loading flowchart: {error}`
  String flowchartLoadError(Object error) {
    return Intl.message(
      'Error loading flowchart: $error',
      name: 'flowchartLoadError',
      desc: '',
      args: [error],
    );
  }

  /// `Create Time`
  String get createTime {
    return Intl.message('Create Time', name: 'createTime', desc: '', args: []);
  }

  /// `Last Update`
  String get lastUpdate {
    return Intl.message('Last Update', name: 'lastUpdate', desc: '', args: []);
  }

  /// `Watched Nodes Count`
  String get watchedNodesCount {
    return Intl.message(
      'Watched Nodes Count',
      name: 'watchedNodesCount',
      desc: '',
      args: [],
    );
  }

  /// `Cannot get project's start element ID`
  String get startElementIdNotFound {
    return Intl.message(
      'Cannot get project\'s start element ID',
      name: 'startElementIdNotFound',
      desc: '',
      args: [],
    );
  }

  /// `Archive file does not exist`
  String get archiveFileNotFound {
    return Intl.message(
      'Archive file does not exist',
      name: 'archiveFileNotFound',
      desc: '',
      args: [],
    );
  }

  /// `Error loading archive: {error}`
  String loadArchiveError(Object error) {
    return Intl.message(
      'Error loading archive: $error',
      name: 'loadArchiveError',
      desc: '',
      args: [error],
    );
  }

  /// `No archives found`
  String get noArchivesFound {
    return Intl.message(
      'No archives found',
      name: 'noArchivesFound',
      desc: '',
      args: [],
    );
  }

  /// `Settings saved`
  String get settingsSaved {
    return Intl.message(
      'Settings saved',
      name: 'settingsSaved',
      desc: '',
      args: [],
    );
  }

  /// `Save settings`
  String get saveSettings {
    return Intl.message(
      'Save settings',
      name: 'saveSettings',
      desc: '',
      args: [],
    );
  }

  /// `General Settings`
  String get generalSettings {
    return Intl.message(
      'General Settings',
      name: 'generalSettings',
      desc: '',
      args: [],
    );
  }

  /// `Audio Settings`
  String get audioSettings {
    return Intl.message(
      'Audio Settings',
      name: 'audioSettings',
      desc: '',
      args: [],
    );
  }

  /// `Language Settings`
  String get languageSettings {
    return Intl.message(
      'Language Settings',
      name: 'languageSettings',
      desc: '',
      args: [],
    );
  }

  /// `Auto save progress`
  String get autoSaveProgress {
    return Intl.message(
      'Auto save progress',
      name: 'autoSaveProgress',
      desc: '',
      args: [],
    );
  }

  /// `Automatically save game progress`
  String get autoSaveDescription {
    return Intl.message(
      'Automatically save game progress',
      name: 'autoSaveDescription',
      desc: '',
      args: [],
    );
  }

  /// `Show video controls`
  String get showVideoControls {
    return Intl.message(
      'Show video controls',
      name: 'showVideoControls',
      desc: '',
      args: [],
    );
  }

  /// `Show controller when playing video`
  String get showVideoControlsDescription {
    return Intl.message(
      'Show controller when playing video',
      name: 'showVideoControlsDescription',
      desc: '',
      args: [],
    );
  }

  /// `Interface language`
  String get interfaceLanguage {
    return Intl.message(
      'Interface language',
      name: 'interfaceLanguage',
      desc: '',
      args: [],
    );
  }

  /// `Version`
  String get version {
    return Intl.message('Version', name: 'version', desc: '', args: []);
  }

  /// `Developer`
  String get developer {
    return Intl.message('Developer', name: 'developer', desc: '', args: []);
  }

  /// `Error opening game: {error}`
  String openGameError(Object error) {
    return Intl.message(
      'Error opening game: $error',
      name: 'openGameError',
      desc: '',
      args: [error],
    );
  }

  /// `Already subscribed`
  String get alreadySubscribed {
    return Intl.message(
      'Already subscribed',
      name: 'alreadySubscribed',
      desc: '',
      args: [],
    );
  }

  /// `This video node has not been unlocked yet. Please watch the previous video first`
  String get videoNodeLocked {
    return Intl.message(
      'This video node has not been unlocked yet. Please watch the previous video first',
      name: 'videoNodeLocked',
      desc: '',
      args: [],
    );
  }

  /// `Like success`
  String get likeSuccess {
    return Intl.message(
      'Like success',
      name: 'likeSuccess',
      desc: '',
      args: [],
    );
  }

  /// `Already liked`
  String get alreadyLiked {
    return Intl.message(
      'Already liked',
      name: 'alreadyLiked',
      desc: '',
      args: [],
    );
  }

  /// `Like failed`
  String get likeFailed {
    return Intl.message('Like failed', name: 'likeFailed', desc: '', args: []);
  }

  /// `Favorite success`
  String get favoriteSuccess {
    return Intl.message(
      'Favorite success',
      name: 'favoriteSuccess',
      desc: '',
      args: [],
    );
  }

  /// `Already favorited`
  String get alreadyFavorited {
    return Intl.message(
      'Already favorited',
      name: 'alreadyFavorited',
      desc: '',
      args: [],
    );
  }

  /// `Favorite failed`
  String get favoriteFailed {
    return Intl.message(
      'Favorite failed',
      name: 'favoriteFailed',
      desc: '',
      args: [],
    );
  }

  /// `Unsubscribe`
  String get unsubscribe {
    return Intl.message('Unsubscribe', name: 'unsubscribe', desc: '', args: []);
  }

  /// `Are you sure you want to unsubscribe?`
  String get confirmUnsubscribe {
    return Intl.message(
      'Are you sure you want to unsubscribe?',
      name: 'confirmUnsubscribe',
      desc: '',
      args: [],
    );
  }

  /// `Unsubscribe success`
  String get unsubscribeSuccess {
    return Intl.message(
      'Unsubscribe success',
      name: 'unsubscribeSuccess',
      desc: '',
      args: [],
    );
  }

  /// `Unsubscribe failed`
  String get unsubscribeFailed {
    return Intl.message(
      'Unsubscribe failed',
      name: 'unsubscribeFailed',
      desc: '',
      args: [],
    );
  }

  /// `Title`
  String get title {
    return Intl.message('Title', name: 'title', desc: '', args: []);
  }

  /// `Required`
  String get required {
    return Intl.message('Required', name: 'required', desc: '', args: []);
  }

  /// `Description`
  String get description {
    return Intl.message('Description', name: 'description', desc: '', args: []);
  }

  /// `Input description`
  String get inputDescription {
    return Intl.message(
      'Input description',
      name: 'inputDescription',
      desc: '',
      args: [],
    );
  }

  /// `Select Content Rating`
  String get selectContentRating {
    return Intl.message(
      'Select Content Rating',
      name: 'selectContentRating',
      desc: '',
      args: [],
    );
  }

  /// `Preview Image`
  String get previewImage {
    return Intl.message(
      'Preview Image',
      name: 'previewImage',
      desc: '',
      args: [],
    );
  }

  /// `Publish`
  String get publish {
    return Intl.message('Publish', name: 'publish', desc: '', args: []);
  }

  /// `Title cannot be empty`
  String get titleCannotBeEmpty {
    return Intl.message(
      'Title cannot be empty',
      name: 'titleCannotBeEmpty',
      desc: '',
      args: [],
    );
  }

  /// `Flow Chart`
  String get flowChart {
    return Intl.message('Flow Chart', name: 'flowChart', desc: '', args: []);
  }

  /// `Please select a project first`
  String get pleaseSelectProject {
    return Intl.message(
      'Please select a project first',
      name: 'pleaseSelectProject',
      desc: '',
      args: [],
    );
  }

  /// `File not found`
  String get fileNotFoundTitle {
    return Intl.message(
      'File not found',
      name: 'fileNotFoundTitle',
      desc: '',
      args: [],
    );
  }

  /// `Failed to open flow chart: $e`
  String get openFlowChartFailed {
    return Intl.message(
      'Failed to open flow chart: \$e',
      name: 'openFlowChartFailed',
      desc: '',
      args: [],
    );
  }

  /// `Invalid time format`
  String get invalidTimeFormat {
    return Intl.message(
      'Invalid time format',
      name: 'invalidTimeFormat',
      desc: '',
      args: [],
    );
  }

  /// `Start time must be earlier than end time`
  String get startTimeBeforeEndTime {
    return Intl.message(
      'Start time must be earlier than end time',
      name: 'startTimeBeforeEndTime',
      desc: '',
      args: [],
    );
  }

  /// `End time cannot exceed the total video length`
  String get endTimeNotExceedTotal {
    return Intl.message(
      'End time cannot exceed the total video length',
      name: 'endTimeNotExceedTotal',
      desc: '',
      args: [],
    );
  }

  /// `Save`
  String get save {
    return Intl.message('Save', name: 'save', desc: '', args: []);
  }

  /// `Play Start and End Time Settings`
  String get playTimeSettings {
    return Intl.message(
      'Play Start and End Time Settings',
      name: 'playTimeSettings',
      desc: '',
      args: [],
    );
  }

  /// `Start Time`
  String get startTimeLabel {
    return Intl.message(
      'Start Time',
      name: 'startTimeLabel',
      desc: '',
      args: [],
    );
  }

  /// `End Time`
  String get endTimeLabel {
    return Intl.message('End Time', name: 'endTimeLabel', desc: '', args: []);
  }

  /// `Hour`
  String get hour {
    return Intl.message('Hour', name: 'hour', desc: '', args: []);
  }

  /// `Minute`
  String get minute {
    return Intl.message('Minute', name: 'minute', desc: '', args: []);
  }

  /// `Second`
  String get second {
    return Intl.message('Second', name: 'second', desc: '', args: []);
  }

  /// `Show video controller`
  String get showVideoController {
    return Intl.message(
      'Show video controller',
      name: 'showVideoController',
      desc: '',
      args: [],
    );
  }

  /// `Show controller during video playback`
  String get showControllerDuringPlayback {
    return Intl.message(
      'Show controller during video playback',
      name: 'showControllerDuringPlayback',
      desc: '',
      args: [],
    );
  }

  /// `Playback Speed`
  String get playbackSpeed {
    return Intl.message(
      'Playback Speed',
      name: 'playbackSpeed',
      desc: '',
      args: [],
    );
  }

  /// `File not found: $jsonFilePath`
  String get fileNotFoundWithPath {
    return Intl.message(
      'File not found: \$jsonFilePath',
      name: 'fileNotFoundWithPath',
      desc: '',
      args: [],
    );
  }

  /// `Load Archive`
  String get loadArchive {
    return Intl.message(
      'Load Archive',
      name: 'loadArchive',
      desc: '',
      args: [],
    );
  }

  /// `View Flow Chart`
  String get viewFlowChart {
    return Intl.message(
      'View Flow Chart',
      name: 'viewFlowChart',
      desc: '',
      args: [],
    );
  }

  /// `Switch to horizontal layout`
  String get switchHorizontal {
    return Intl.message(
      'Switch to horizontal layout',
      name: 'switchHorizontal',
      desc: '',
      args: [],
    );
  }

  /// `Switch to vertical layout`
  String get switchVertical {
    return Intl.message(
      'Switch to vertical layout',
      name: 'switchVertical',
      desc: '',
      args: [],
    );
  }

  /// `Invalid project path`
  String get invalidProjectPath {
    return Intl.message(
      'Invalid project path',
      name: 'invalidProjectPath',
      desc: '',
      args: [],
    );
  }

  /// `Game Window`
  String get gameWindow {
    return Intl.message('Game Window', name: 'gameWindow', desc: '', args: []);
  }

  /// `Project loaded: {path}`
  String projectLoaded(Object path) {
    return Intl.message(
      'Project loaded: $path',
      name: 'projectLoaded',
      desc: '',
      args: [path],
    );
  }

  /// `Project not found: {path}`
  String projectNotFound(Object path) {
    return Intl.message(
      'Project not found: $path',
      name: 'projectNotFound',
      desc: '',
      args: [path],
    );
  }

  /// `New Project`
  String get newProject {
    return Intl.message('New Project', name: 'newProject', desc: '', args: []);
  }

  /// `Project Exists`
  String get projectExistsTitle {
    return Intl.message(
      'Project Exists',
      name: 'projectExistsTitle',
      desc: '',
      args: [],
    );
  }

  /// `This project already exists, do you want to enter this project?`
  String get projectExistsContent {
    return Intl.message(
      'This project already exists, do you want to enter this project?',
      name: 'projectExistsContent',
      desc: '',
      args: [],
    );
  }

  /// `Update Workshop Item`
  String get confirmUpdateWorkshopItem {
    return Intl.message(
      'Update Workshop Item',
      name: 'confirmUpdateWorkshopItem',
      desc: '',
      args: [],
    );
  }

  /// `You are about to update an existing workshop item. This will replace the current version with your changes.`
  String get confirmUpdateWorkshopItemDescription {
    return Intl.message(
      'You are about to update an existing workshop item. This will replace the current version with your changes.',
      name: 'confirmUpdateWorkshopItemDescription',
      desc: '',
      args: [],
    );
  }

  /// `Updating...`
  String get updating {
    return Intl.message('Updating...', name: 'updating', desc: '', args: []);
  }

  /// `Update successful!`
  String get updateSuccess {
    return Intl.message(
      'Update successful!',
      name: 'updateSuccess',
      desc: '',
      args: [],
    );
  }

  /// `Update Workshop Item`
  String get updateWorkshop {
    return Intl.message(
      'Update Workshop Item',
      name: 'updateWorkshop',
      desc: '',
      args: [],
    );
  }

  /// `You have no workshop items to update`
  String get noWorkshopItems {
    return Intl.message(
      'You have no workshop items to update',
      name: 'noWorkshopItems',
      desc: '',
      args: [],
    );
  }

  /// `Select Workshop Item to Update`
  String get selectWorkshopItemToUpdate {
    return Intl.message(
      'Select Workshop Item to Update',
      name: 'selectWorkshopItemToUpdate',
      desc: '',
      args: [],
    );
  }

  /// `Error updating workshop item: {error}`
  String updateWorkshopError(Object error) {
    return Intl.message(
      'Error updating workshop item: $error',
      name: 'updateWorkshopError',
      desc: '',
      args: [error],
    );
  }

  /// `Auto save game`
  String get autoSaveGame {
    return Intl.message(
      'Auto save game',
      name: 'autoSaveGame',
      desc: '',
      args: [],
    );
  }

  /// `Auto save interval`
  String get autoSaveInterval {
    return Intl.message(
      'Auto save interval',
      name: 'autoSaveInterval',
      desc: '',
      args: [],
    );
  }

  /// `Enable Flowchart Check`
  String get enableFlowchartCheck {
    return Intl.message(
      'Enable Flowchart Check',
      name: 'enableFlowchartCheck',
      desc: '',
      args: [],
    );
  }

  /// `Detect disconnected nodes and start point connections`
  String get flowchartCheckDescription {
    return Intl.message(
      'Detect disconnected nodes and start point connections',
      name: 'flowchartCheckDescription',
      desc: '',
      args: [],
    );
  }

  /// `Disconnected video nodes: {count}`
  String disconnectedVideoCount(Object count) {
    return Intl.message(
      'Disconnected video nodes: $count',
      name: 'disconnectedVideoCount',
      desc: '',
      args: [count],
    );
  }

  /// `Start node should connect to 1 video, current: {count}`
  String startNodeVideoCount(Object count) {
    return Intl.message(
      'Start node should connect to 1 video, current: $count',
      name: 'startNodeVideoCount',
      desc: '',
      args: [count],
    );
  }

  /// `Exit Fullscreen`
  String get exitFullscreen {
    return Intl.message(
      'Exit Fullscreen',
      name: 'exitFullscreen',
      desc: '',
      args: [],
    );
  }

  /// `Open Project in New Window`
  String get useNewWindowForEditing {
    return Intl.message(
      'Open Project in New Window',
      name: 'useNewWindowForEditing',
      desc: '',
      args: [],
    );
  }

  /// `When enabled, project editor will open in a new window, otherwise it will open in the current window`
  String get useNewWindowForEditingDescription {
    return Intl.message(
      'When enabled, project editor will open in a new window, otherwise it will open in the current window',
      name: 'useNewWindowForEditingDescription',
      desc: '',
      args: [],
    );
  }

  /// `Workshop item updated`
  String get workshopItemUpdated {
    return Intl.message(
      'Workshop item updated',
      name: 'workshopItemUpdated',
      desc: '',
      args: [],
    );
  }

  /// `Workshop item uploaded`
  String get workshopItemUploaded {
    return Intl.message(
      'Workshop item uploaded',
      name: 'workshopItemUploaded',
      desc: '',
      args: [],
    );
  }

  /// `Upload failed: `
  String get uploadFailedWithColon {
    return Intl.message(
      'Upload failed: ',
      name: 'uploadFailedWithColon',
      desc: '',
      args: [],
    );
  }

  /// `Congratulations! You earned 10 coins!`
  String get congratsEarnedCoins {
    return Intl.message(
      'Congratulations! You earned 10 coins!',
      name: 'congratsEarnedCoins',
      desc: '',
      args: [],
    );
  }

  /// `Preparing project files...`
  String get preparingProjectFiles {
    return Intl.message(
      'Preparing project files...',
      name: 'preparingProjectFiles',
      desc: '',
      args: [],
    );
  }

  /// `Preparing config`
  String get preparingConfig {
    return Intl.message(
      'Preparing config',
      name: 'preparingConfig',
      desc: '',
      args: [],
    );
  }

  /// `Uploading, please wait...`
  String get uploadingPleaseWait {
    return Intl.message(
      'Uploading, please wait...',
      name: 'uploadingPleaseWait',
      desc: '',
      args: [],
    );
  }

  /// `Uploading content`
  String get uploadingContent {
    return Intl.message(
      'Uploading content',
      name: 'uploadingContent',
      desc: '',
      args: [],
    );
  }

  /// `Uploading preview image`
  String get uploadingPreviewImage {
    return Intl.message(
      'Uploading preview image',
      name: 'uploadingPreviewImage',
      desc: '',
      args: [],
    );
  }

  /// `Committing changes`
  String get committingChanges {
    return Intl.message(
      'Committing changes',
      name: 'committingChanges',
      desc: '',
      args: [],
    );
  }

  /// `Insufficient disk space. Based on the Steam upload mechanism, adequate space is required on the disk where the application is located. Please free up disk space and try uploading again.`
  String get diskSpaceInsufficient {
    return Intl.message(
      'Insufficient disk space. Based on the Steam upload mechanism, adequate space is required on the disk where the application is located. Please free up disk space and try uploading again.',
      name: 'diskSpaceInsufficient',
      desc: '',
      args: [],
    );
  }

  /// `No branches to set`
  String get noBranchesToSet {
    return Intl.message(
      'No branches to set',
      name: 'noBranchesToSet',
      desc: '',
      args: [],
    );
  }

  /// `Normal Branch`
  String get normalBranch {
    return Intl.message(
      'Normal Branch',
      name: 'normalBranch',
      desc: '',
      args: [],
    );
  }

  /// `Timed Branch`
  String get timedBranch {
    return Intl.message(
      'Timed Branch',
      name: 'timedBranch',
      desc: '',
      args: [],
    );
  }

  /// `QTE Branch`
  String get qteBranch {
    return Intl.message('QTE Branch', name: 'qteBranch', desc: '', args: []);
  }

  /// `Time Limit (seconds)`
  String get timeLimit {
    return Intl.message(
      'Time Limit (seconds)',
      name: 'timeLimit',
      desc: '',
      args: [],
    );
  }

  /// `Auto Select`
  String get autoSelect {
    return Intl.message('Auto Select', name: 'autoSelect', desc: '', args: []);
  }

  /// `Question Description`
  String get questionDescription {
    return Intl.message(
      'Question Description',
      name: 'questionDescription',
      desc: '',
      args: [],
    );
  }

  /// `Button Opacity`
  String get buttonOpacity {
    return Intl.message(
      'Button Opacity',
      name: 'buttonOpacity',
      desc: '',
      args: [],
    );
  }

  /// `Transparent`
  String get transparent {
    return Intl.message('Transparent', name: 'transparent', desc: '', args: []);
  }

  /// `Opaque`
  String get opaque {
    return Intl.message('Opaque', name: 'opaque', desc: '', args: []);
  }

  /// `Button Text`
  String get buttonText {
    return Intl.message('Button Text', name: 'buttonText', desc: '', args: []);
  }

  /// `Branch {index}:`
  String branchIndexLabel(Object index) {
    return Intl.message(
      'Branch $index:',
      name: 'branchIndexLabel',
      desc: '',
      args: [index],
    );
  }

  /// `Custom Button Position`
  String get customButtonPosition {
    return Intl.message(
      'Custom Button Position',
      name: 'customButtonPosition',
      desc: '',
      args: [],
    );
  }

  /// `Screen Preview`
  String get screenPreview {
    return Intl.message(
      'Screen Preview',
      name: 'screenPreview',
      desc: '',
      args: [],
    );
  }

  /// `Remaining Time: {time} seconds`
  String remainingTimeLabel(Object time) {
    return Intl.message(
      'Remaining Time: $time seconds',
      name: 'remainingTimeLabel',
      desc: '',
      args: [time],
    );
  }

  /// `Horizontal: {horizontalPercent}%, Vertical: {verticalPercent}%`
  String buttonPositionInfo(Object horizontalPercent, Object verticalPercent) {
    return Intl.message(
      'Horizontal: $horizontalPercent%, Vertical: $verticalPercent%',
      name: 'buttonPositionInfo',
      desc: '',
      args: [horizontalPercent, verticalPercent],
    );
  }

  /// `Title Position: Horizontal {horizontalPercent}%, Vertical {verticalPercent}%`
  String titlePositionInfo(Object horizontalPercent, Object verticalPercent) {
    return Intl.message(
      'Title Position: Horizontal $horizontalPercent%, Vertical $verticalPercent%',
      name: 'titlePositionInfo',
      desc: '',
      args: [horizontalPercent, verticalPercent],
    );
  }

  /// `Saving...`
  String get saving {
    return Intl.message('Saving...', name: 'saving', desc: '', args: []);
  }

  /// `Branch settings and flowchart saved`
  String get branchSettingsSaved {
    return Intl.message(
      'Branch settings and flowchart saved',
      name: 'branchSettingsSaved',
      desc: '',
      args: [],
    );
  }

  /// `Save flowchart failed`
  String get saveFlowchartFailed {
    return Intl.message(
      'Save flowchart failed',
      name: 'saveFlowchartFailed',
      desc: '',
      args: [],
    );
  }

  /// `Full video path: {path}`
  String fullVideoPath(Object path) {
    return Intl.message(
      'Full video path: $path',
      name: 'fullVideoPath',
      desc: '',
      args: [path],
    );
  }

  /// `Video file does not exist: {path}`
  String videoFileNotExist(Object path) {
    return Intl.message(
      'Video file does not exist: $path',
      name: 'videoFileNotExist',
      desc: '',
      args: [path],
    );
  }

  /// `Archive updated for node: {nodeId}`
  String archiveUpdatedForNode(Object nodeId) {
    return Intl.message(
      'Archive updated for node: $nodeId',
      name: 'archiveUpdatedForNode',
      desc: '',
      args: [nodeId],
    );
  }

  /// `Node marked as watched: {nodeId}`
  String nodeMarkedAsWatched(Object nodeId) {
    return Intl.message(
      'Node marked as watched: $nodeId',
      name: 'nodeMarkedAsWatched',
      desc: '',
      args: [nodeId],
    );
  }

  /// `Video playback error: {error}`
  String videoPlaybackError(Object error) {
    return Intl.message(
      'Video playback error: $error',
      name: 'videoPlaybackError',
      desc: '',
      args: [error],
    );
  }

  /// `Add Global Value`
  String get addGlobalValue {
    return Intl.message(
      'Add Global Value',
      name: 'addGlobalValue',
      desc: '',
      args: [],
    );
  }

  /// `Edit Global Value`
  String get editGlobalValue {
    return Intl.message(
      'Edit Global Value',
      name: 'editGlobalValue',
      desc: '',
      args: [],
    );
  }

  /// `Value Name`
  String get globalValueName {
    return Intl.message(
      'Value Name',
      name: 'globalValueName',
      desc: '',
      args: [],
    );
  }

  /// `Example: Coins, Health, etc.`
  String get globalValueNameHint {
    return Intl.message(
      'Example: Coins, Health, etc.',
      name: 'globalValueNameHint',
      desc: '',
      args: [],
    );
  }

  /// `Initial Value`
  String get initialValue {
    return Intl.message(
      'Initial Value',
      name: 'initialValue',
      desc: '',
      args: [],
    );
  }

  /// `Please enter initial value`
  String get valueInputHint {
    return Intl.message(
      'Please enter initial value',
      name: 'valueInputHint',
      desc: '',
      args: [],
    );
  }

  /// `Please enter a value name`
  String get pleaseEnterValueName {
    return Intl.message(
      'Please enter a value name',
      name: 'pleaseEnterValueName',
      desc: '',
      args: [],
    );
  }

  /// `Edit Global Value: {name}`
  String editGlobalValueTitle(Object name) {
    return Intl.message(
      'Edit Global Value: $name',
      name: 'editGlobalValueTitle',
      desc: '',
      args: [name],
    );
  }

  /// `Value`
  String get valueLabel {
    return Intl.message('Value', name: 'valueLabel', desc: '', args: []);
  }

  /// `Global Values`
  String get globalValues {
    return Intl.message(
      'Global Values',
      name: 'globalValues',
      desc: '',
      args: [],
    );
  }

  /// `Variable Name`
  String get variableName {
    return Intl.message(
      'Variable Name',
      name: 'variableName',
      desc: '',
      args: [],
    );
  }

  /// `Please enter variable name`
  String get pleaseEnterVariableName {
    return Intl.message(
      'Please enter variable name',
      name: 'pleaseEnterVariableName',
      desc: '',
      args: [],
    );
  }

  /// `Variable already exists`
  String get variableAlreadyExists {
    return Intl.message(
      'Variable already exists',
      name: 'variableAlreadyExists',
      desc: '',
      args: [],
    );
  }

  /// `Number`
  String get numberType {
    return Intl.message('Number', name: 'numberType', desc: '', args: []);
  }

  /// `Text`
  String get textType {
    return Intl.message('Text', name: 'textType', desc: '', args: []);
  }

  /// `Boolean`
  String get booleanType {
    return Intl.message('Boolean', name: 'booleanType', desc: '', args: []);
  }

  /// `Type`
  String get variableType {
    return Intl.message('Type', name: 'variableType', desc: '', args: []);
  }

  /// `Add to Flowchart`
  String get addToFlowchart {
    return Intl.message(
      'Add to Flowchart',
      name: 'addToFlowchart',
      desc: '',
      args: [],
    );
  }

  /// `Add Variable`
  String get addVariable {
    return Intl.message(
      'Add Variable',
      name: 'addVariable',
      desc: '',
      args: [],
    );
  }

  /// `Please enter a valid number`
  String get pleaseEnterValidNumber {
    return Intl.message(
      'Please enter a valid number',
      name: 'pleaseEnterValidNumber',
      desc: '',
      args: [],
    );
  }

  /// `Please enter true or false`
  String get pleaseEnterTrueOrFalse {
    return Intl.message(
      'Please enter true or false',
      name: 'pleaseEnterTrueOrFalse',
      desc: '',
      args: [],
    );
  }

  /// `Please enter a value`
  String get pleaseEnterValue {
    return Intl.message(
      'Please enter a value',
      name: 'pleaseEnterValue',
      desc: '',
      args: [],
    );
  }

  /// `Failed to get workshop items`
  String get failedToGetWorkshopItems {
    return Intl.message(
      'Failed to get workshop items',
      name: 'failedToGetWorkshopItems',
      desc: '',
      args: [],
    );
  }

  /// `World Saving Guide`
  String get workshopRecommendedTitle {
    return Intl.message(
      'World Saving Guide',
      name: 'workshopRecommendedTitle',
      desc: '',
      args: [],
    );
  }

  /// `Written by the village chief of this engine, villagers all approve, this guide will automatically evolve with version updates`
  String get workshopRecommendedDescription {
    return Intl.message(
      'Written by the village chief of this engine, villagers all approve, this guide will automatically evolve with version updates',
      name: 'workshopRecommendedDescription',
      desc: '',
      args: [],
    );
  }

  /// `Set Options and Value Changes`
  String get setOptionsAndValueChanges {
    return Intl.message(
      'Set Options and Value Changes',
      name: 'setOptionsAndValueChanges',
      desc: '',
      args: [],
    );
  }

  /// `Select Branch to Set`
  String get selectBranchToSet {
    return Intl.message(
      'Select Branch to Set',
      name: 'selectBranchToSet',
      desc: '',
      args: [],
    );
  }

  /// `Branch: {branchText}`
  String branchWithText(Object branchText) {
    return Intl.message(
      'Branch: $branchText',
      name: 'branchWithText',
      desc: '',
      args: [branchText],
    );
  }

  /// `No global values found, please add values in global value management first`
  String get noGlobalValuesFoundAddFirst {
    return Intl.message(
      'No global values found, please add values in global value management first',
      name: 'noGlobalValuesFoundAddFirst',
      desc: '',
      args: [],
    );
  }

  /// `Greater Than`
  String get greaterThan {
    return Intl.message(
      'Greater Than',
      name: 'greaterThan',
      desc: '',
      args: [],
    );
  }

  /// `Less Than`
  String get lessThan {
    return Intl.message('Less Than', name: 'lessThan', desc: '', args: []);
  }

  /// `Equal To`
  String get equalTo {
    return Intl.message('Equal To', name: 'equalTo', desc: '', args: []);
  }

  /// `Greater Than or Equal`
  String get greaterThanOrEqual {
    return Intl.message(
      'Greater Than or Equal',
      name: 'greaterThanOrEqual',
      desc: '',
      args: [],
    );
  }

  /// `Less Than or Equal`
  String get lessThanOrEqual {
    return Intl.message(
      'Less Than or Equal',
      name: 'lessThanOrEqual',
      desc: '',
      args: [],
    );
  }

  /// `Not Equal`
  String get notEqual {
    return Intl.message('Not Equal', name: 'notEqual', desc: '', args: []);
  }

  /// `Range`
  String get range {
    return Intl.message('Range', name: 'range', desc: '', args: []);
  }

  /// `Add`
  String get add {
    return Intl.message('Add', name: 'add', desc: '', args: []);
  }

  /// `Subtract`
  String get subtract {
    return Intl.message('Subtract', name: 'subtract', desc: '', args: []);
  }

  /// `Multiply`
  String get multiply {
    return Intl.message('Multiply', name: 'multiply', desc: '', args: []);
  }

  /// `Divide`
  String get divide {
    return Intl.message('Divide', name: 'divide', desc: '', args: []);
  }

  /// `Set To`
  String get setTo {
    return Intl.message('Set To', name: 'setTo', desc: '', args: []);
  }

  /// `Set Branch "{branchText}" Conditions and Value Changes`
  String setBranchConditionsAndChanges(Object branchText) {
    return Intl.message(
      'Set Branch "$branchText" Conditions and Value Changes',
      name: 'setBranchConditionsAndChanges',
      desc: '',
      args: [branchText],
    );
  }

  /// `Set and Enable Conditions for Current Option`
  String get setAndEnableConditions {
    return Intl.message(
      'Set and Enable Conditions for Current Option',
      name: 'setAndEnableConditions',
      desc: '',
      args: [],
    );
  }

  /// `Min Value`
  String get minValue {
    return Intl.message('Min Value', name: 'minValue', desc: '', args: []);
  }

  /// `to`
  String get to {
    return Intl.message('to', name: 'to', desc: '', args: []);
  }

  /// `Max Value`
  String get maxValue {
    return Intl.message('Max Value', name: 'maxValue', desc: '', args: []);
  }

  /// `Set and Enable Value Changes After Selecting Option`
  String get setAndEnableValueChanges {
    return Intl.message(
      'Set and Enable Value Changes After Selecting Option',
      name: 'setAndEnableValueChanges',
      desc: '',
      args: [],
    );
  }

  /// `Saving branch settings...`
  String get savingBranchSettings {
    return Intl.message(
      'Saving branch settings...',
      name: 'savingBranchSettings',
      desc: '',
      args: [],
    );
  }

  /// `Branch "{branchText}" conditions and value changes saved`
  String nameBranchSettingsSaved(Object branchText) {
    return Intl.message(
      'Branch "$branchText" conditions and value changes saved',
      name: 'nameBranchSettingsSaved',
      desc: '',
      args: [branchText],
    );
  }

  /// `Branch settings and flowchart saved`
  String get generalBranchSettingsSaved {
    return Intl.message(
      'Branch settings and flowchart saved',
      name: 'generalBranchSettingsSaved',
      desc: '',
      args: [],
    );
  }

  /// `Save failed, please check logs`
  String get saveFailed {
    return Intl.message(
      'Save failed, please check logs',
      name: 'saveFailed',
      desc: '',
      args: [],
    );
  }

  /// `Apply`
  String get apply {
    return Intl.message('Apply', name: 'apply', desc: '', args: []);
  }

  /// `Button Display Time`
  String get buttonDisplayTime {
    return Intl.message(
      'Button Display Time',
      name: 'buttonDisplayTime',
      desc: '',
      args: [],
    );
  }

  /// `How many seconds before video ends to show branch buttons`
  String get buttonDisplayTimeDescription {
    return Intl.message(
      'How many seconds before video ends to show branch buttons',
      name: 'buttonDisplayTimeDescription',
      desc: '',
      args: [],
    );
  }

  /// `When set to 0, buttons show only when video ends`
  String get buttonDisplayTimeNote {
    return Intl.message(
      'When set to 0, buttons show only when video ends',
      name: 'buttonDisplayTimeNote',
      desc: '',
      args: [],
    );
  }

  /// `seconds`
  String get seconds {
    return Intl.message('seconds', name: 'seconds', desc: '', args: []);
  }

  /// `Text Only (No Button Background)`
  String get buttonTextOnly {
    return Intl.message(
      'Text Only (No Button Background)',
      name: 'buttonTextOnly',
      desc: '',
      args: [],
    );
  }

  /// `QTE Button Duration`
  String get qteDuration {
    return Intl.message(
      'QTE Button Duration',
      name: 'qteDuration',
      desc: '',
      args: [],
    );
  }

  /// `Time for player to react`
  String get qteDurationDescription {
    return Intl.message(
      'Time for player to react',
      name: 'qteDurationDescription',
      desc: '',
      args: [],
    );
  }

  /// `QTE Success Branch`
  String get qteSuccessBranch {
    return Intl.message(
      'QTE Success Branch',
      name: 'qteSuccessBranch',
      desc: '',
      args: [],
    );
  }

  /// `QTE Fail Branch`
  String get qteFailBranch {
    return Intl.message(
      'QTE Fail Branch',
      name: 'qteFailBranch',
      desc: '',
      args: [],
    );
  }

  /// `QTE Button Position`
  String get qteButtonPosition {
    return Intl.message(
      'QTE Button Position',
      name: 'qteButtonPosition',
      desc: '',
      args: [],
    );
  }

  /// `Button Display Time: {duration} seconds`
  String qteButtonDurationSeconds(Object duration) {
    return Intl.message(
      'Button Display Time: $duration seconds',
      name: 'qteButtonDurationSeconds',
      desc: '',
      args: [duration],
    );
  }

  /// `QTE\n{duration}s`
  String qteButtonDisplayTime(Object duration) {
    return Intl.message(
      'QTE\n${duration}s',
      name: 'qteButtonDisplayTime',
      desc: '',
      args: [duration],
    );
  }

  /// `Success`
  String get qteSuccessLabel {
    return Intl.message('Success', name: 'qteSuccessLabel', desc: '', args: []);
  }

  /// `Fail`
  String get qteFailLabel {
    return Intl.message('Fail', name: 'qteFailLabel', desc: '', args: []);
  }

  /// `QTE Button Position: {horizontalPercent}%, {verticalPercent}%`
  String qtePositionInfo(Object horizontalPercent, Object verticalPercent) {
    return Intl.message(
      'QTE Button Position: $horizontalPercent%, $verticalPercent%',
      name: 'qtePositionInfo',
      desc: '',
      args: [horizontalPercent, verticalPercent],
    );
  }

  /// `Enable click to pause video`
  String get enableVideoClickPause {
    return Intl.message(
      'Enable click to pause video',
      name: 'enableVideoClickPause',
      desc: '',
      args: [],
    );
  }

  /// `When enabled, clicking the video area will toggle play/pause state`
  String get enableVideoClickPauseDesc {
    return Intl.message(
      'When enabled, clicking the video area will toggle play/pause state',
      name: 'enableVideoClickPauseDesc',
      desc: '',
      args: [],
    );
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<S> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'en'),
      Locale.fromSubtags(languageCode: 'af'),
      Locale.fromSubtags(languageCode: 'am'),
      Locale.fromSubtags(languageCode: 'fr'),
      Locale.fromSubtags(languageCode: 'zh', countryCode: 'CN'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<S> load(Locale locale) => S.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
