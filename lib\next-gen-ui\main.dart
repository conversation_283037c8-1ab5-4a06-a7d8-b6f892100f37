// Copyright 2023 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

import 'dart:io' show Platform;
import 'dart:ui';

import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';
import 'package:window_size/window_size.dart';

import 'assets.dart';
import 'title_screen/title_screen.dart';

void main() {
  if (!kIsWeb && (Platform.isWindows || Platform.isLinux || Platform.isMacOS)) {
    WidgetsFlutterBinding.ensureInitialized();
    
    // 设置最小窗口尺寸
    setWindowMinSize(const Size(800, 500));
    
    // 获取屏幕尺寸
    final window = PlatformDispatcher.instance.views.first;
    final screenSize = window.physicalSize / window.devicePixelRatio;
    
    // 设置窗口默认尺寸为屏幕大小
    setWindowFrame(Rect.fromLTWH(0, 0, screenSize.width, screenSize.height));
  }
  
  Animate.restartOnHotReload = true;
  runApp(
    FutureProvider<FragmentPrograms?>(
      create: (context) => loadFragmentPrograms(),
      initialData: null,
      child: const NextGenApp(),
    ),
  );
}

class NextGenApp extends StatefulWidget {
  const NextGenApp({super.key});

  @override
  State<NextGenApp> createState() => _NextGenAppState();
}

class _NextGenAppState extends State<NextGenApp> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      themeMode: ThemeMode.dark,
      darkTheme: ThemeData(brightness: Brightness.dark),
      home: Scaffold(
        // 移除所有默认边距和内边距
        body: MediaQuery.removePadding(
          context: context,
          removeTop: true,
          removeBottom: true,
          removeLeft: true,
          removeRight: true,
          child: SizedBox.expand( // 确保占满整个可用空间
            child: Stack(
              fit: StackFit.expand, // 确保Stack的子元素占满整个Stack
              children: [
                TitleScreen(
                  projectPath: '',
                  projectName: '',
                  workshopItemId: '0',
                  onStartPressed: () {
                    // 这里不需要做任何事情，因为这个文件只是作为参考
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
