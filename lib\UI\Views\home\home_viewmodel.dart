import 'package:stacked/stacked.dart';

class HomeViewModel extends BaseViewModel {
  int firstIndex = 1;
  int secondIndex = 0;
  ViewType currentView = ViewType.home;

  void changeFirstIndex(int index) {
    firstIndex = index;
    notifyListeners();
  }

  void changeSecondIndex(int index) {
    secondIndex = index;
    notifyListeners();
  }

  void changeView(ViewType viewType) {
    currentView = viewType;
    notifyListeners();
  }
}

enum ViewType {
  home,
  openGame,
  workshop,
  createGame,
}