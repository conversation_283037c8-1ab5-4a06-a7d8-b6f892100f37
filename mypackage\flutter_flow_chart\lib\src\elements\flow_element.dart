// ignore_for_file: avoid_positional_boolean_parameters, avoid_dynamic_calls

import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_flow_chart/flutter_flow_chart.dart';
import 'package:flutter_flow_chart/src/elements/connection_params.dart';
import 'package:uuid/uuid.dart';

/// Kinf od element
enum ElementKind {
  ///
  start,

  ///
  video,
  
  ///
  rectangle,

  ///
  diamond,

  ///
  storage,

  ///
  oval,

  ///
  parallelogram,

  ///
  hexagon,

  ///
  image,
  
  ///
  globalValue,
}

/// Handler supported by elements
enum Handler {
  ///
  topCenter,

  ///
  bottomCenter,

  ///
  rightCenter,

  ///
  leftCenter;

  /// Convert to [Alignment]
  Alignment toAlignment() {
    switch (this) {
      case Handler.topCenter:
        return Alignment.topCenter;
      case Handler.bottomCenter:
        return Alignment.bottomCenter;
      case Handler.rightCenter:
        return Alignment.centerRight;
      case Handler.leftCenter:
        return Alignment.centerLeft;
    }
  }
}

/// Class to store [FlowElement]s and notify its changes
class FlowElement extends ChangeNotifier {
  ///
  FlowElement({
    Offset position = Offset.zero,
    this.size = Size.zero,
    this.text = '',
    this.path= '',
    this.allTime = '',
    this.startTime = '',
    this.endTime = '',
    this.projectPath = '',
    this.projectName = '',
    this.textColor = Colors.black,
    this.fontFamily,
    this.textSize = 24,
    this.textIsBold = false,
    this.kind = ElementKind.rectangle,
    this.handlers = const [
      Handler.topCenter,
      Handler.bottomCenter,
      Handler.rightCenter,
      Handler.leftCenter,
    ],
    this.handlerSize = 15.0,
    this.backgroundColor = Colors.white,
    this.borderColor = Colors.blue,
    this.borderThickness = 3,
    this.elevation = 4,
    this.data,
    this.isDraggable = true,
    this.isResizable = false,
    this.isConnectable = true,
    this.isDeletable = false,
    this.branchTitle,
    this.branchButtonOpacity,
    this.branchButtonPosition,
    this.branchTitlePosition,
    this.branchButtonTexts,
    this.branchButtonPositions,
    List<ConnectionParams>? next,
  })  : next = next ?? [],
        id = const Uuid().v4(),
        isEditingText = false,
        // fixing offset issue under extreme scaling
        position = position -
            Offset(
              size.width / 2 + handlerSize / 2,
              size.height / 2 + handlerSize / 2,
            );

  ///
  factory FlowElement.fromMap(Map<String, dynamic> map) {
    final e = FlowElement(
      size: Size(map['size.width'] as double, map['size.height'] as double),
      text: map['text'] as String,
      path: map['path'] as String,
      allTime: map['allTime'] as String,
      startTime: map['startTime'] as String,
      endTime: map['endTime'] as String,
      projectPath: map['projectPath'] as String? ?? '', 
      projectName: map['projectName'] as String? ?? '',
      textColor: Color(map['textColor'] as int),
      fontFamily: map['fontFamily'] as String?,
      textSize: map['textSize'] as double,
      textIsBold: map['textIsBold'] as bool,
      kind: ElementKind.values[map['kind'] as int],
      handlers: List<Handler>.from(
        (map['handlers'] as List<dynamic>).map<Handler>(
          (x) => Handler.values[x as int],
        ),
      ),
      handlerSize: map['handlerSize'] as double,
      backgroundColor: Color(map['backgroundColor'] as int),
      borderColor: Color(map['borderColor'] as int),
      borderThickness: map['borderThickness'] as double,
      elevation: map['elevation'] as double,
      next: (map['next'] as List).isNotEmpty
          ? List<ConnectionParams>.from(
              (map['next'] as List<dynamic>).map<dynamic>(
                (x) => ConnectionParams.fromMap(x as Map<String, dynamic>),
              ),
            )
          : [],
      isDraggable: map['isDraggable'] as bool? ?? true,
      isResizable: map['isResizable'] as bool? ?? false,
      isConnectable: map['isConnectable'] as bool? ?? true,
      isDeletable: map['isDeletable'] as bool? ?? false,
      branchTitle: map['branchTitle'] as String?,
      branchButtonOpacity: map['branchButtonOpacity'] as double?,
      branchButtonPosition: map['branchButtonPosition'] != null
          ? Offset(
              (map['branchButtonPosition'] as Map<String, dynamic>)['dx'] as double,
              (map['branchButtonPosition'] as Map<String, dynamic>)['dy'] as double,
            )
          : null,
      branchTitlePosition: map['branchTitlePosition'] != null
          ? Offset(
              (map['branchTitlePosition'] as Map<String, dynamic>)['dx'] as double,
              (map['branchTitlePosition'] as Map<String, dynamic>)['dy'] as double,
            )
          : null,
      branchButtonTexts: map['branchButtonTexts'] != null
          ? Map<String, String>.from(map['branchButtonTexts'] as Map)
          : null,
      branchButtonPositions: map['branchButtonPositions'] != null
          ? _parseButtonPositions(map['branchButtonPositions'] as Map<String, dynamic>)
          : null,
    )
      ..setId(map['id'] as String)
      ..position = Offset(
        map['positionDx'] as double,
        map['positionDy'] as double,
      )
      ..serializedData = map['data'] as String?;
    return e;
  }

  // 辅助函数，解析按钮位置映射
  static Map<String, Offset>? _parseButtonPositions(Map<String, dynamic> positionsMap) {
    final Map<String, Offset> result = {};
    
    positionsMap.forEach((key, value) {
      if (value is Map<String, dynamic> && 
          value.containsKey('dx') && 
          value.containsKey('dy')) {
        result[key] = Offset(
          value['dx'] as double,
          value['dy'] as double,
        );
      }
    });
    
    return result.isNotEmpty ? result : null;
  }

  ///
  factory FlowElement.fromJson(String source) =>
      FlowElement.fromMap(json.decode(source) as Map<String, dynamic>);

  /// Unique id set when adding a [FlowElement] with [Dashboard.addElement()]
  String id;

  /// The position of the [FlowElement]
  Offset position;

  /// The size of the [FlowElement]
  Size size;

  /// Element text
  String text;
  
  /// Element path
  String path;
  String allTime;
  String startTime;
  String endTime;
  
  String projectPath;

  /// Project name for file organization
  String projectName;

  /// Text color
  Color textColor;

  /// Text font family
  String? fontFamily;

  /// Text size
  double textSize;

  /// Makes text bold if true
  bool textIsBold;

  /// Element shape
  ElementKind kind;

  /// Connection handlers
  List<Handler> handlers;

  /// The size of element handlers
  double handlerSize;

  /// Background color of the element
  Color backgroundColor;

  /// Border color of the element
  Color borderColor;

  /// Border thickness of the element
  double borderThickness;

  /// Shadow elevation
  double elevation;

  /// List of connections from this element
  List<ConnectionParams> next;

  /// Whether this element can be dragged around
  bool isDraggable;

  /// Whether this element can be resized
  bool isResizable;

  /// Whether this element can be deleted quickly by clicking on the trash icon
  bool isDeletable;

  /// Whether this element can be connected to others
  bool isConnectable;

  /// Whether the text of this element is being edited with a form field
  bool isEditingText;

  /// 分支标题，显示在分支选择按钮的上方
  String? branchTitle;

  /// 分支按钮的透明度
  double? branchButtonOpacity;

  /// 分支按钮在屏幕上的位置（主位置，用于所有按钮的统一位置）
  Offset? branchButtonPosition;

  /// 分支标题在屏幕上的位置
  Offset? branchTitlePosition;

  /// 分支按钮文字，按钮ID与文字的映射
  Map<String, String>? branchButtonTexts;

  /// 分支按钮位置，按钮ID与位置的映射（单独位置设置）
  Map<String, Offset>? branchButtonPositions;

  /// Kind-specific data
  final dynamic data;

  /// Kind-specific data to load/save
  String? serializedData;

  @override
  String toString() {
    return 'FlowElement{kind: $kind, text: $text}';
  }

  /// Get the handler center of this handler for the given alignment.
  Offset getHandlerPosition(Alignment alignment) {
    // The zero position coordinate is the top-left of this element.
    final ret = Offset(
      position.dx + (size.width * ((alignment.x + 1) / 2)) + handlerSize / 2,
      position.dy + (size.height * ((alignment.y + 1) / 2) + handlerSize / 2),
    );
    return ret;
  }

  /// Sets a new scale
  void setScale(double currentZoom, double factor) {
    size = size / currentZoom * factor;
    handlerSize = handlerSize / currentZoom * factor;
    textSize = textSize / currentZoom * factor;
    for (final element in next) {
      element.arrowParams.setScale(currentZoom, factor);
    }

    notifyListeners();
  }

  /// Used internally to set an unique Uuid to this element
  void setId(String id) {
    this.id = id;
  }

  /// Set text
  void setText(String text) {
    this.text = text;
    notifyListeners();
  }

  /// Set text color
  void setTextColor(Color color) {
    textColor = color;
    notifyListeners();
  }

  /// Set text font family
  void setFontFamily(String? fontFamily) {
    this.fontFamily = fontFamily;
    notifyListeners();
  }

  /// Set text size
  void setTextSize(double size) {
    textSize = size;
    notifyListeners();
  }

  /// Set text bold
  void setTextIsBold(bool isBold) {
    textIsBold = isBold;
    notifyListeners();
  }

  /// Set background color
  void setBackgroundColor(Color color) {
    backgroundColor = color;
    notifyListeners();
  }

  /// Set border color
  void setBorderColor(Color color) {
    borderColor = color;
    notifyListeners();
  }

  /// Set border thickness
  void setBorderThickness(double thickness) {
    borderThickness = thickness;
    notifyListeners();
  }

  /// Set elevation
  void setElevation(double elevation) {
    this.elevation = elevation;
    notifyListeners();
  }

  /// Change element position in the dashboard
  void changePosition(Offset newPosition) {
    position = newPosition;
    notifyListeners();
  }

  /// Change element size
  void changeSize(Size newSize) {
    size = newSize;
    if (size.width < 40) size = Size(40, size.height);
    if (size.height < 40) size = Size(size.width, 40);
    notifyListeners();
  }

  @override
  bool operator ==(covariant FlowElement other) {
    if (identical(this, other)) return true;

    return other.id == id;
  }

  @override
  int get hashCode {
    return position.hashCode ^
        size.hashCode ^
        text.hashCode ^
        textColor.hashCode ^
        fontFamily.hashCode ^
        textSize.hashCode ^
        textIsBold.hashCode ^
        id.hashCode ^
        kind.hashCode ^
        handlers.hashCode ^
        handlerSize.hashCode ^
        backgroundColor.hashCode ^
        borderColor.hashCode ^
        borderThickness.hashCode ^
        elevation.hashCode ^
        next.hashCode ^
        isResizable.hashCode ^
        isConnectable.hashCode ^
        isDeletable.hashCode;
  }

  /// 设置分支标题
  void setBranchTitle(String title) {
    branchTitle = title;
    notifyListeners();
  }

  /// 设置分支按钮透明度
  void setBranchButtonOpacity(double opacity) {
    branchButtonOpacity = opacity;
    notifyListeners();
  }

  /// 设置分支按钮位置（统一位置）
  void setBranchButtonPosition(Offset position) {
    branchButtonPosition = position;
    notifyListeners();
  }

  /// 设置分支标题位置
  void setBranchTitlePosition(Offset position) {
    branchTitlePosition = position;
    notifyListeners();
  }

  /// 设置特定分支按钮的文字
  void setBranchButtonText(String buttonId, String text) {
    branchButtonTexts ??= {};
    branchButtonTexts![buttonId] = text;
    notifyListeners();
  }
  
  /// 批量设置分支按钮文字
  void setBranchButtonTexts(Map<String, String> texts) {
    branchButtonTexts = texts;
    notifyListeners();
  }

  /// 设置特定分支按钮的位置
  void setBranchButtonSpecificPosition(String buttonId, Offset position) {
    branchButtonPositions ??= {};
    branchButtonPositions![buttonId] = position;
    notifyListeners();
  }
  
  /// 批量设置分支按钮位置
  void setBranchButtonPositions(Map<String, Offset> positions) {
    branchButtonPositions = positions;
    notifyListeners();
  }

  /// 获取特定按钮的文字
  String getBranchButtonText(String buttonId) {
    if (branchButtonTexts != null && branchButtonTexts!.containsKey(buttonId)) {
      return branchButtonTexts![buttonId]!;
    }
    // 返回默认文字（使用连接目标元素的文字）
    return '';
  }

  /// 获取特定按钮的位置
  Offset getBranchButtonPosition(String buttonId) {
    if (branchButtonPositions != null && branchButtonPositions!.containsKey(buttonId)) {
      return branchButtonPositions![buttonId]!;
    }
    // 返回默认位置
    return branchButtonPosition ?? const Offset(0.5, 0.8);
  }

  ///
  Map<String, dynamic> toMap() {
    final Map<String, dynamic> buttonPositionMap = branchButtonPosition != null 
        ? {'dx': branchButtonPosition!.dx, 'dy': branchButtonPosition!.dy}
        : {};

    final Map<String, dynamic> titlePositionMap = branchTitlePosition != null
        ? {'dx': branchTitlePosition!.dx, 'dy': branchTitlePosition!.dy}
        : {};

    final Map<String, dynamic> buttonPositionsMap = {};
    if (branchButtonPositions != null) {
      branchButtonPositions!.forEach((key, offset) {
        buttonPositionsMap[key] = {'dx': offset.dx, 'dy': offset.dy};
      });
    }

    return <String, dynamic>{
      'positionDx': position.dx,
      'positionDy': position.dy,
      'size.width': size.width,
      'size.height': size.height,
      'text': text,
      'path': path,
      'allTime': allTime,
      'startTime': startTime,
      'endTime': endTime,
      'textColor': textColor.value,
      'fontFamily': fontFamily,
      'textSize': textSize,
      'textIsBold': textIsBold,
      'id': id,
      'kind': kind.index,
      'handlers': handlers.map((x) => x.index).toList(),
      'handlerSize': handlerSize,
      'backgroundColor': backgroundColor.value,
      'borderColor': borderColor.value,
      'borderThickness': borderThickness,
      'elevation': elevation,
      'data': serializedData,
      'next': next.map((x) => x.toMap()).toList(),
      'isDraggable': isDraggable,
      'isResizable': isResizable,
      'isConnectable': isConnectable,
      'isDeletable': isDeletable,
      'projectPath': projectPath,
      'projectName': projectName,
      'branchTitle': branchTitle,
      'branchButtonOpacity': branchButtonOpacity,
      'branchButtonPosition': branchButtonPosition != null ? buttonPositionMap : null,
      'branchTitlePosition': branchTitlePosition != null ? titlePositionMap : null,
      'branchButtonTexts': branchButtonTexts,
      'branchButtonPositions': branchButtonPositions != null ? buttonPositionsMap : null,
    };
  }

  ///
  String toJson() => json.encode(toMap());
}
