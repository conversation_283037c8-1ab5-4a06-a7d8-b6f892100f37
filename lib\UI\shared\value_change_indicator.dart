import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

/// 数值变化指示器
/// 当数值上升或下降时显示相应的动画提示框
/// 
/// 上升：蓝色上箭头动画（从粗到细）
/// 下降：红色下箭头动画（从粗到细）
class ValueChangeIndicator extends StatelessWidget {
  /// 变化类型
  final ValueChangeType changeType;
  
  /// 动画持续时间
  final Duration duration;
  
  /// 变量名称
  final String? valueName;
  
  /// 创建一个数值变化指示器
  const ValueChangeIndicator({
    Key? key,
    required this.changeType,
    this.duration = const Duration(milliseconds: 1600),
    this.valueName,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 根据变化类型决定箭头颜色和方向
    final Color arrowColor = changeType == ValueChangeType.increase 
        ? Colors.blue 
        : Colors.red;
    
    // 根据变化类型决定整个框的移动方向
    final double beginSlide = changeType == ValueChangeType.increase 
        ? 0.5   // 上升类型：从下往上移动（正值）
        : -0.5; // 下降类型：从上往下移动（负值）
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            valueName ?? '敬對',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 28,
              decoration: TextDecoration.none,
            ),
          ),
          const SizedBox(width: 8),
          
          // 箭头动画
          SizedBox(
            width: 48,
            height: 48, // 保持原来的高度
            child: Stack(
              alignment: Alignment.center,
              children: [
                // 初始粗箭头，带垂直移动
                _buildThickArrow(arrowColor)
                  .animate()
                  .slideY(
                    begin: changeType == ValueChangeType.increase ? 0.3 : -0.3, // 减小移动幅度
                    end: 0.0, // 移动到中间
                    duration: duration * 0.4,
                    curve: Curves.easeOut,
                  )
                  .fadeOut(
                    delay: duration * 0.3,
                    duration: duration * 0.5,
                    curve: Curves.easeOut,
                  ),
                
                // 最终细箭头，带垂直移动
                _buildThinArrow(arrowColor)
                  .animate()
                  .fadeIn(
                    delay: duration * 0.3,
                    duration: duration * 0.7,
                    curve: Curves.easeIn,
                  )
                  .slideY(
                    delay: duration * 0.3,
                    begin: changeType == ValueChangeType.increase ? 0.2 : -0.2, // 减小移动幅度
                    end: 0.0, // 移动到中间
                    duration: duration * 0.6,
                    curve: Curves.easeOutCubic,
                  ),
              ],
            ),
          ),
        ],
      ),
    ).animate()
      .slideY(
        begin: beginSlide, // 根据变化类型决定起始位置
        end: 0,
        duration: duration * 0.3,
        curve: Curves.easeOutQuad,
      )
      .then()
      .fadeOut(
        delay: duration * 0.7,
        duration: duration * 0.3,
      );
  }
  
  /// 构建粗箭头
  Widget _buildThickArrow(Color color) {
    // 绘制一个类似于图片中的粗箭头
    return CustomPaint(
      size: const Size(48, 48),
      painter: _ArrowPainter(
        color: color,
        direction: changeType == ValueChangeType.increase ? ArrowDirection.up : ArrowDirection.down,
        isThick: true,
      ),
    );
  }
  
  /// 构建细箭头
  Widget _buildThinArrow(Color color) {
    // 绘制一个类似于图片中的细箭头
    return CustomPaint(
      size: const Size(48, 48),
      painter: _ArrowPainter(
        color: color,
        direction: changeType == ValueChangeType.increase ? ArrowDirection.up : ArrowDirection.down,
        isThick: false,
      ),
    );
  }
}

/// 箭头方向
enum ArrowDirection {
  up,
  down,
}

/// 自定义箭头绘制
class _ArrowPainter extends CustomPainter {
  final Color color;
  final ArrowDirection direction;
  final bool isThick;
  
  _ArrowPainter({
    required this.color,
    required this.direction,
    required this.isThick,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill
      ..strokeWidth = isThick ? 6.0 : 4.0;
    
    final center = Offset(size.width / 2, size.height / 2);
    
    if (isThick) {
      // 绘制粗箭头（类似于图片中的三角形）
      final path = Path();
      
      if (direction == ArrowDirection.down) {
        // 绘制下箭头
        path.moveTo(center.dx - 16, center.dy - 8);
        path.lineTo(center.dx + 16, center.dy - 8);
        path.lineTo(center.dx, center.dy + 16);
        path.close();
      } else {
        // 绘制上箭头
        path.moveTo(center.dx - 16, center.dy + 8);
        path.lineTo(center.dx + 16, center.dy + 8);
        path.lineTo(center.dx, center.dy - 16);
        path.close();
      }
      
      canvas.drawPath(path, paint);
    } else {
      // 绘制细箭头（类似于图片中的V形）
      final path = Path();
      
      if (direction == ArrowDirection.down) {
        // 绘制下箭头
        path.moveTo(center.dx - 12, center.dy - 8);
        path.lineTo(center.dx, center.dy + 8);
        path.lineTo(center.dx + 12, center.dy - 8);
      } else {
        // 绘制上箭头
        path.moveTo(center.dx - 12, center.dy + 8);
        path.lineTo(center.dx, center.dy - 8);
        path.lineTo(center.dx + 12, center.dy + 8);
      }
      
      paint.style = PaintingStyle.stroke;
      canvas.drawPath(path, paint);
    }
  }
  
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}

/// 数值变化类型
enum ValueChangeType {
  /// 数值增加
  increase,
  
  /// 数值减少
  decrease,
} 