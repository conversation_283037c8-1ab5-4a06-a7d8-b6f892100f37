# Generated code do not commit.
file(TO_CMAKE_PATH "D:\\Flutter\\SDK\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "D:\\Flutter\\ve" PROJECT_DIR)

set(FLUTTER_VERSION "0.1.0" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 0 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=D:\\Flutter\\SDK\\flutter"
  "PROJECT_DIR=D:\\Flutter\\ve"
  "FLUTTER_ROOT=D:\\Flutter\\SDK\\flutter"
  "FLUTTER_EPHEMERAL_DIR=D:\\Flutter\\ve\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=D:\\Flutter\\ve"
  "FLUTTER_TARGET=D:\\Flutter\\ve\\lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=D:\\Flutter\\ve\\.dart_tool\\package_config.json"
)
