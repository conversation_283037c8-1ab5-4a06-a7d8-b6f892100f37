import 'package:flutter/material.dart';
import 'package:media_kit/media_kit.dart';
import 'package:media_kit_video/media_kit_video.dart';

class VideoWindow extends StatefulWidget {
  final VideoController controller;
  final Offset initialPosition;
  final Function(Offset) onPositionChanged;

  const VideoWindow({
    super.key,
    required this.controller,
    required this.initialPosition,
    required this.onPositionChanged,
  });

  @override
  _VideoWindowState createState() => _VideoWindowState();
}

class _VideoWindowState extends State<VideoWindow> {
  late Offset position;

  @override
  void initState() {
    super.initState();
    position = widget.initialPosition; // 初始化位置
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: position.dx,
      top: position.dy,
      child: Draggable(
        feedback: Material(
          elevation: 8,
          child: SizedBox(
            width: 360,
            height: 180,
            child: Video(
              controller: widget.controller,
              controls: MaterialDesktopVideoControls,
            ),
          ),
        ),
        childWhenDragging: Container(),
        child: SizedBox(
            width: 360,
            height: 180,
          child: Card(
            elevation: 8,
            child: Video(
              controller: widget.controller,
              controls: MaterialDesktopVideoControls,
            ),
          ),
        ),
        onDragEnd: (details) {
          setState(() {
            position = details.offset; // 更新位置
            widget.onPositionChanged(position); // 调用回调函数
          });
        },
      ),
    );
  }
}
