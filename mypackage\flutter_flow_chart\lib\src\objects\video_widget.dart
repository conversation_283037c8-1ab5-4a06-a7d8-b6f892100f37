import 'package:flutter/material.dart';
import 'package:flutter_flow_chart/src/elements/flow_element.dart';
import 'package:flutter_flow_chart/src/dashboard.dart';
import 'package:flutter_flow_chart/src/objects/locked_video_widget.dart';
import 'dart:io';

class VideoWidget extends StatelessWidget {
  const VideoWidget({
    required this.element,
    required this.projectPath,
    required this.projectName,
    required this.dashboard,
    super.key,
  });

  final FlowElement element;
  final String projectPath;
  final String projectName;
  final Dashboard dashboard;


  @override
  Widget build(BuildContext context) {
    // 检查是否处于播放模式且节点未被观看过
    final bool isPlayMode = dashboard.isPlayMode;
    final bool isNodeWatched = dashboard.isNodeWatched(element.id);
    final bool isStartNode = element.kind == ElementKind.start;
    
    // 如果是播放模式且节点未被观看过且不是起始节点，显示锁定状态
    if (isPlayMode && !isNodeWatched && !isStartNode) {
      return LockedVideoWidget(element: element, projectPath: projectPath);
    }
    
    String thumbnailPath = '${projectPath}/${element.id}.jpg';
    
    // 固定宽高比16:9
    final width = element.size.width;
    final height = width * 9 / 16;
    
    // 确定提示信息
    String? tooltipMessage;
    if (isPlayMode) {
      // 播放模式下，未观看的节点显示"未解锁"，已观看的节点不显示提示
      tooltipMessage = !isNodeWatched && !isStartNode ? "未解锁的视频节点" : null;
    } else {
      // 编辑模式下显示详细信息
      tooltipMessage = "${element.path}\n${element.startTime} -> ${element.endTime}";
    }
    
    // 如果tooltipMessage为null，则不显示任何提示
    Widget content = SizedBox(
      width: width,
      height: height,
      child: Stack(
        alignment: Alignment.center,
        children: [
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: element.borderColor,
                width: element.borderThickness,
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.file(
                File(thumbnailPath),
                width: width,
                height: height,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return const Center(child: Text('暂无缩略图'));
                },
              ),
            ),
          ),
          // 只在编辑模式或已观看的节点上显示标题
          if (!isPlayMode || isNodeWatched || isStartNode)
            Positioned(
              top: 4,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.8),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  element.text,
                  style: const TextStyle(
                    color: Colors.black,
                    fontSize: 16,
                  ),
                ),
              ),
            ),
        ],
      ),
    );

    return tooltipMessage != null 
      ? Tooltip(
          message: tooltipMessage,
          preferBelow: false,
          verticalOffset: 20,
          child: content,
        )
      : content;
  }
}
