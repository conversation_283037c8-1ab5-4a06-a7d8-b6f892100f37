import 'package:flutter/material.dart';
import 'package:flutter_flow_chart/flutter_flow_chart.dart';

/// 全局数值显示组件
class GlobalValueWidget extends StatelessWidget {
  /// 构造函数
  const GlobalValueWidget({
    required this.element,
    super.key,
  });

  /// 要显示的元素
  final FlowElement element;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: element.size.width,
      height: element.size.height,
      decoration: BoxDecoration(
        color: element.backgroundColor,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: element.borderColor,
          width: element.borderThickness,
        ),
        boxShadow: element.elevation > 0
            ? [
                BoxShadow(
                  color: Colors.black.withOpacity(0.3),
                  spreadRadius: 1,
                  blurRadius: element.elevation,
                  offset: const Offset(1, 2),
                ),
              ]
            : null,
      ),
      alignment: Alignment.center,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 变量名
          Text(
            element.text,
            textAlign: TextAlign.center,
            style: TextStyle(
              color: element.textColor,
              fontSize: element.textSize,
              fontWeight: element.textIsBold ? FontWeight.bold : FontWeight.normal,
              fontFamily: element.fontFamily,
            ),
          ),
          const SizedBox(height: 8),
          // 变量值
          Text(
            element.serializedData ?? '0',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: element.textColor,
              fontSize: element.textSize * 0.8,
              fontWeight: FontWeight.normal,
              fontFamily: element.fontFamily,
            ),
          ),
        ],
      ),
    );
  }
} 