import 'package:flutter/material.dart';
import 'example_usage.dart';
import 'example_advanced_usage.dart';

/// 示例入口点应用
/// 用于演示数值变化提示框的使用
class ValueChangeExampleApp extends StatelessWidget {
  const ValueChangeExampleApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '数值变化提示框示例',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
      ),
      home: const ExampleEntryPage(),
    );
  }
}

/// 示例选择页面
class ExampleEntryPage extends StatelessWidget {
  const ExampleEntryPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('数值变化提示框示例'),
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // 标题
              const Icon(
                Icons.trending_up,
                size: 80,
                color: Colors.blue,
              ),
              const SizedBox(height: 16),
              const Text(
                '数值变化提示框演示',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                '本示例展示了在数值增加或减少时显示的动画提示框',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(height: 40),
              
              // 基础示例按钮
              ElevatedButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(builder: (context) => const ValueChangeExample()),
                  );
                },
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
                child: const Text('基础示例', style: TextStyle(fontSize: 18)),
              ),
              const SizedBox(height: 20),
              
              // 进阶示例按钮
              ElevatedButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(builder: (context) => const AdvancedValueChangeExample()),
                  );
                },
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                  backgroundColor: Colors.deepPurple,
                  foregroundColor: Colors.white,
                ),
                child: const Text('进阶示例', style: TextStyle(fontSize: 18)),
              ),
              
              const SizedBox(height: 40),
              const Text(
                '提示: 点击按钮查看相应的示例',
                style: TextStyle(
                  fontStyle: FontStyle.italic,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// 主函数
void main() {
  runApp(const ValueChangeExampleApp());
} 