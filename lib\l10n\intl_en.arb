{"@@locale": "en", "gameTitle": "Interactive Video Engine", "settings": "Settings", "allSettings": "All Settings", "credits": "Credits", "creditsCreativeTitle": "Creative & Planning", "creditsGameProducer": "Game Producer", "creditsCreativeSource": "Creative Source", "creditsSoftwarePlanner": "Software Planner", "creditsGameplayPlanner": "Gameplay Planner", "creditsProgrammingTitle": "Programming", "creditsSoftwareArchitect": "Software Architect", "creditsCodeWriter": "Code Writer", "creditsVisualTitle": "Visual Design", "creditsArtDesigner": "Art Designer", "creditsAnimationDesigner": "Animation Designer", "creditsMarketingTitle": "Marketing", "creditsVideoProducer": "Video Producer", "creditsCopywriter": "Copy<PERSON>", "creditsThanksTitle": "Thanks for playing my game!", "creditsSpecialThanks": "Special Thanks", "creditsSubtitle": "View teams and contributors involved in software development", "openGameInNewWindow": "Open Game in New Window", "openGameInNewWindowDesc": "When enabled, open game projects in a new window, otherwise open them in the current window", "publicMode": "Public Mode/Live Streaming Mode", "publicModeDesc": "When enabled, the workshop interface will force the use of 'Everyone' content rating and not show NSFW tags", "verticalLayoutDescription": "Vertical video editing interface (recommended for high resolution screens)", "language": "Language", "fullscreen": "Fullscreen Mode", "mainWindowFullscreen": "Main Window Fullscreen", "createNewGame": "Create Game", "workshop": "Workshop", "openGame": "Open Game", "home": "Home", "activity": "Activity", "profile": "Profile", "downloads": "Downloads", "friends": "Friends", "messages": "Messages", "elementParams": "Element Parameters", "backgroundColor": "Background Color", "thickness": "<PERSON><PERSON><PERSON><PERSON>", "borderColor": "Border Color", "elevation": "Elevation", "videoPlayback": "Video Playback", "playbackControl": "Playback Control", "play": "Play", "pause": "Pause", "stop": "Stop", "playbackRate": "Playback Rate", "videoCover": "Video Cover", "captureCurrentFrame": "Capture Current Frame", "timeEdit": "Time Edit", "startTime": "Start Time", "endTime": "End Time", "setCurrentTime": "Set Current Time", "addVideo": "Add Video", "addDiamond": "Add Diamond", "addRectangle": "Add Rectangle", "addResizableRectangle": "Add Resizable Rectangle", "addOval": "Add Oval", "addParallelogram": "Add Parallelogram", "addHexagon": "Add Hexagon", "addStorage": "Add Storage", "addImage": "Add Image", "removeAll": "Remove All", "saveFlowchart": "Save Flowchart", "loadFlowchart": "Load Flowchart", "workshopItems": "Workshop Items", "loadingFailed": "Loading Failed", "retryLoading": "Click to Retry", "openGameTitle": "Open Game", "showController": "Show Controller", "hideController": "Hide Controller", "myGames": "My Games", "popularGames": "Popular Games", "news": "News", "recentlyPlayed": "Recently Played", "recentlyEdited": "Recently Edited", "achievementDisplay": "Achievement Display", "achievements": "Achievements", "screenshots": "Screenshots", "player": "Player", "games": "Games", "search": "Search", "modifyTimeAndCover": "Modify Time and Cover", "delete": "Delete", "removeAllConnections": "Remove All Connections", "toggleConnectable": "Toggle Connectable", "toggleResizable": "Toggle Resizable", "segmented": "Segmented", "curved": "Curved", "rectangular": "Rectangular", "startPoint": "Start", "loadingFailedRetry": "Loading Failed, Click to Retry", "flowchartMissingStart": "Flowchart missing start element", "videoTimeAndCover": "Video Time and Cover", "coverLoadFailed": "Cover Load Failed", "close": "Close", "confirm": "Confirm", "playPause": "Play/Pause", "invalidStartTime": "Invalid Start Time", "invalidEndTime": "Invalid End Time", "playbackStatus": "Playback Status", "currentPosition": "Current Position", "volume": "Volume Level", "completed": "Completion Status", "duration": "Total Duration", "rate": "Playback Rate", "playbackProgress": "Playback Progress", "volumeControl": "Volume Control", "audioTrack": "Audio Track", "subtitle": "Subtitle", "noSubtitle": "No Subtitle", "closeSubtitle": "Close Subtitle", "modifyChapterTitle": "Modify Chapter Title", "setBranchParams": "Set Branch Type and Parameters", "noAudioTrack": "No AudioTrack", "enterProjectName": "Enter Project Name", "projectNameHint": "Project Name", "cancel": "Cancel", "projectCreated": "Project Created Successfully", "fileNotFound": "File Not Found", "upload": "Upload", "uploadWorkshop": "Upload to Workshop", "importImage": "Import Image", "removeImage": "Remove Image", "previewImageDefault": "<PERSON><PERSON><PERSON>", "ageRating": "Age Rating", "visibility": "Visibility", "uploadNow": "Upload Now", "uploading": "Uploading", "uploadFailed": "Upload Failed", "uploadSuccess": "Upload Success", "download": "Download", "downloadWorkshop": "Download from Workshop", "downloading": "Downloading", "downloadFailed": "Download Failed", "downloadSuccess": "Download Success", "invalidFileType": "Invalid File Type", "unsupportedFileFormat": "Unsupported file format, workshop only allows video, jpg and json files", "noValidFilesFound": "No valid files found for upload, please ensure your project includes supported files only", "SearchWorkshop": "Search in the workshop", "style": "Style", "AncientChinese": "Ancient Chinese", "shape": "<PERSON><PERSON><PERSON>", "ilpEditor": "Game Editor", "steamLimitedAccount": "Steam Limited Account", "sort": "Sort", "joinDiscord": "Join Discord to discussion", "back": "Back", "no": "No", "resume": "Resume", "retry": "Retry", "seed": "Seed", "about": "About", "exit": "Exit", "confirmExitApp": "Are you sure you want to exit the application?", "challenge": "Challenges", "gallery": "Gallery", "steamChallenge": "Start challenge", "steamGallery": "Steam workshop gallery", "Subscribe": "Subscribe", "SubscribeAndDownload": "Subscribe & download", "clickToSubscribeAndDownload": "Click to subscribe and download", "createChallenge": "Create challenge", "challengeName": "Challenge name", "ilpDesc": "Description", "Everyone": "Everyone", "NSFW": "NSFW", "public": "Public", "friendsonly": "Friends Only", "private": "Private", "Anime": "Anime", "Realistic": "Realistic", "Pixel": "Pixel", "Ancient Chinese": "Ancient Chinese", "Other": "Other", "Landscape": "Landscape", "Portrait": "Portrait", "Square": "Square", "puzzleHint": "Already reduced one wrong answer", "steamWorkshop": "Steam Workshop", "steamAuthorOtherFiles": "Author's Other Files", "publishTime": "Publish Time", "updateTime": "Update Time", "adultAgreementTitle": "Please make sure you are over 18 years old", "adultAgreementContent": "The Steam Workshop contains co created content from players worldwide, which may involve content that is not suitable for viewing in public places.", "Unsubscribed": "Unsubscribed", "itemNotSubscribed": "Item not subscribed, it will start downloading automatically after subscription", "subscribeSuccess": "Subscribe success, downloading...", "subscribeFailed": "Subscribe failed: ", "downloadingPleaseWait": "Downloading, please wait", "Subscribed": "Subscribed", "sortBySubscribers": "By Subscribers", "sortByVote": "By Votes", "sortByFavorites": "By Favorites", "sortByPublishDate": "By Publish Date", "sortByUpdateDate": "By Update Date", "sortBy": "Sort By", "toggleSortDirection": "Toggle Sort Direction", "autoFullScreenVideo": "Auto Fullscreen Video", "newWindowFullScreen": "New Window Fullscreen", "newWindowFullScreenDescription": "When opening a new window, make it fullscreen automatically", "filePreparingPleaseRetry": "File preparing, please try again later", "remoteReservedWord": "'remote' is a reserved word, please use a different project name", "workshopRules": "Workshop Rules", "workshopRulesDescription": "Before submitting interactive videos to the creative workshop, please ensure that they do not violate Steam's terms of service, otherwise the interactive videos will be removed:", "workshopRulesSpecial": "Especially interactive videos, preview images and descriptions should follow these rules:", "workshopRuleNoAdult": "No photographic or realistic pornographic or nude content", "workshopRuleNoOffensive": "No offensive content or violent, bloody content", "workshopRuleNoCopyright": "No copyright infringement", "workshopRuleNoAds": "No advertisements", "workshopRuleNoMisleading": "No misleading preview images", "archive": "Archive", "flowchart": "Flowchart", "startGame": "START GAME", "viewFlowchart": "View Flowchart", "nodeDetails": "Node Details", "elementId": "ID", "elementType": "Type", "elementText": "Text", "startNode": "Start Node", "videoNode": "Video Node", "rectangleNode": "Rectangle Node", "diamondNode": "Diamond Node", "storageNode": "Storage Node", "ovalNode": "Oval Node", "parallelogramNode": "Parallelogram Node", "hexagonNode": "Hexagon Node", "imageNode": "Image Node", "unknownNodeType": "Unknown Type", "flowchartFileNotFound": "FLOWCHART.json file not found", "flowchartLoadError": "Error loading flowchart: {error}", "createTime": "Create Time", "lastUpdate": "Last Update", "watchedNodesCount": "Watched Nodes Count", "startElementIdNotFound": "Cannot get project's start element ID", "archiveFileNotFound": "Archive file does not exist", "loadArchiveError": "Error loading archive: {error}", "noArchivesFound": "No archives found", "settingsSaved": "Setting<PERSON> saved", "saveSettings": "Save settings", "generalSettings": "General Settings", "audioSettings": "Audio Settings", "languageSettings": "Language Settings", "autoSaveProgress": "Auto save progress", "autoSaveDescription": "Automatically save game progress", "showVideoControls": "Show video controls", "showVideoControlsDescription": "Show controller when playing video", "interfaceLanguage": "Interface language", "version": "Version", "developer": "Developer", "openGameError": "Error opening game: {error}", "alreadySubscribed": "Already subscribed", "videoNodeLocked": "This video node has not been unlocked yet. Please watch the previous video first", "likeSuccess": "Like success", "alreadyLiked": "Already liked", "likeFailed": "Like failed", "favoriteSuccess": "Favorite success", "alreadyFavorited": "Already favorited", "favoriteFailed": "Favorite failed", "unsubscribe": "Unsubscribe", "confirmUnsubscribe": "Are you sure you want to unsubscribe?", "unsubscribeSuccess": "Unsubscribe success", "unsubscribeFailed": "Unsubscribe failed", "title": "Title", "required": "Required", "description": "Description", "inputDescription": "Input description", "selectContentRating": "Select Content Rating", "previewImage": "Preview Image", "publish": "Publish", "titleCannotBeEmpty": "Title cannot be empty", "flowChart": "Flow Chart", "pleaseSelectProject": "Please select a project first", "fileNotFoundTitle": "File not found", "openFlowChartFailed": "Failed to open flow chart: $e", "invalidTimeFormat": "Invalid time format", "startTimeBeforeEndTime": "Start time must be earlier than end time", "endTimeNotExceedTotal": "End time cannot exceed the total video length", "save": "Save", "playTimeSettings": "Play Start and End Time Settings", "startTimeLabel": "Start Time", "endTimeLabel": "End Time", "hour": "Hour", "minute": "Minute", "second": "Second", "showVideoController": "Show video controller", "showControllerDuringPlayback": "Show controller during video playback", "playbackSpeed": "Playback Speed", "fileNotFoundWithPath": "File not found: $jsonFilePath", "loadArchive": "Load Archive", "viewFlowChart": "View Flow Chart", "switchHorizontal": "Switch to horizontal layout", "switchVertical": "Switch to vertical layout", "invalidProjectPath": "Invalid project path", "gameWindow": "Game Window", "projectLoaded": "Project loaded: {path}", "projectNotFound": "Project not found: {path}", "newProject": "New Project", "projectExistsTitle": "Project Exists", "projectExistsContent": "This project already exists, do you want to enter this project?", "confirmUpdateWorkshopItem": "Update Workshop Item", "confirmUpdateWorkshopItemDescription": "You are about to update an existing workshop item. This will replace the current version with your changes.", "updating": "Updating...", "updateSuccess": "Update successful!", "updateWorkshop": "Update Workshop Item", "noWorkshopItems": "You have no workshop items to update", "selectWorkshopItemToUpdate": "Select Workshop Item to Update", "updateWorkshopError": "Error updating workshop item: {error}", "autoSaveGame": "Auto save game", "autoSaveInterval": "Auto save interval", "enableFlowchartCheck": "Enable Flowchart Check", "flowchartCheckDescription": "Detect disconnected nodes and start point connections", "disconnectedVideoCount": "Disconnected video nodes: {count}", "startNodeVideoCount": "Start node should connect to 1 video, current: {count}", "exitFullscreen": "Exit Fullscreen", "useNewWindowForEditing": "Open Project in New Window", "useNewWindowForEditingDescription": "When enabled, project editor will open in a new window, otherwise it will open in the current window", "workshopItemUpdated": "Workshop item updated", "workshopItemUploaded": "Workshop item uploaded", "uploadFailedWithColon": "Upload failed: ", "congratsEarnedCoins": "Congratulations! You earned 10 coins!", "preparingProjectFiles": "Preparing project files...", "preparingConfig": "Preparing config", "uploadingPleaseWait": "Uploading, please wait...", "uploadingContent": "Uploading content", "uploadingPreviewImage": "Uploading preview image", "committingChanges": "Committing changes", "diskSpaceInsufficient": "Insufficient disk space. Based on the Steam upload mechanism, adequate space is required on the disk where the application is located. Please free up disk space and try uploading again.", "noBranchesToSet": "No branches to set", "normalBranch": "Normal Branch", "timedBranch": "Timed Branch", "qteBranch": "QTE Branch", "timeLimit": "Time Limit (seconds)", "autoSelect": "Auto Select", "questionDescription": "Question Description", "buttonOpacity": "Button Opacity", "transparent": "Transparent", "opaque": "Opaque", "buttonText": "Button Text", "branchIndexLabel": "Branch {index}:", "customButtonPosition": "Custom Button Position", "screenPreview": "Screen Preview", "remainingTimeLabel": "Remaining Time: {time} seconds", "buttonPositionInfo": "Horizontal: {horizontalPercent}%, Vertical: {verticalPercent}%", "titlePositionInfo": "Title Position: Horizontal {horizontalPercent}%, Vertical {verticalPercent}%", "saving": "Saving...", "branchSettingsSaved": "Branch settings and flowchart saved", "saveFlowchartFailed": "Save flowchart failed", "fullVideoPath": "Full video path: {path}", "videoFileNotExist": "Video file does not exist: {path}", "archiveUpdatedForNode": "Archive updated for node: {nodeId}", "nodeMarkedAsWatched": "Node marked as watched: {nodeId}", "videoPlaybackError": "Video playback error: {error}", "addGlobalValue": "Add Global Value", "editGlobalValue": "Edit Global Value", "globalValueName": "Value Name", "globalValueNameHint": "Example: Coins, Health, etc.", "initialValue": "Initial Value", "valueInputHint": "Please enter initial value", "pleaseEnterValueName": "Please enter a value name", "editGlobalValueTitle": "Edit Global Value: {name}", "valueLabel": "Value", "globalValues": "Global Values", "variableName": "Variable Name", "pleaseEnterVariableName": "Please enter variable name", "variableAlreadyExists": "Variable already exists", "numberType": "Number", "textType": "Text", "booleanType": "Boolean", "variableType": "Type", "addToFlowchart": "Add to Flowchart", "addVariable": "Add Variable", "pleaseEnterValidNumber": "Please enter a valid number", "pleaseEnterTrueOrFalse": "Please enter true or false", "pleaseEnterValue": "Please enter a value", "failedToGetWorkshopItems": "Failed to get workshop items", "workshopRecommendedTitle": "World Saving Guide", "workshopRecommendedDescription": "Written by the village chief of this engine, villagers all approve, this guide will automatically evolve with version updates", "setOptionsAndValueChanges": "Set Options and Value Changes", "selectBranchToSet": "Select Branch to Set", "branchWithText": "Branch: {branchText}", "noGlobalValuesFoundAddFirst": "No global values found, please add values in global value management first", "greaterThan": "Greater Than", "lessThan": "Less Than", "equalTo": "Equal To", "greaterThanOrEqual": "Greater Than or Equal", "lessThanOrEqual": "Less Than or Equal", "notEqual": "Not Equal", "range": "Range", "add": "Add", "subtract": "Subtract", "multiply": "Multiply", "divide": "Divide", "setTo": "Set To", "setBranchConditionsAndChanges": "Set Branch \"{branchText}\" Conditions and Value Changes", "setAndEnableConditions": "Set and Enable Conditions for Current Option", "minValue": "Min Value", "to": "to", "maxValue": "Max Value", "setAndEnableValueChanges": "Set and Enable Value Changes After Selecting Option", "savingBranchSettings": "Saving branch settings...", "nameBranchSettingsSaved": "Branch \"{branchText}\" conditions and value changes saved", "generalBranchSettingsSaved": "Branch settings and flowchart saved", "saveFailed": "Save failed, please check logs", "apply": "Apply", "buttonDisplayTime": "Button Display Time", "buttonDisplayTimeDescription": "How many seconds before video ends to show branch buttons", "buttonDisplayTimeNote": "When set to 0, buttons show only when video ends", "seconds": "seconds", "buttonTextOnly": "Text Only (No But<PERSON> Background)", "qteDuration": "QTE Button Duration", "qteDurationDescription": "Time for player to react", "qteSuccessBranch": "QTE Success Branch", "qteFailBranch": "QTE Fail Branch", "qteButtonPosition": "QTE Button Position", "qteButtonDurationSeconds": "Button Display Time: {duration} seconds", "qteButtonDisplayTime": "QTE\n{duration}s", "qteSuccessLabel": "Success", "qteFailLabel": "Fail", "qtePositionInfo": "QTE Button Position: {horizontalPercent}%, {verticalPercent}%", "enableVideoClickPause": "Enable click to pause video", "enableVideoClickPauseDesc": "When enabled, clicking the video area will toggle play/pause state"}