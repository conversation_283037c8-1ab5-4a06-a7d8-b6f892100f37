// ignore_for_file: public_member_api_docs, always_specify_types, avoid_positional_boolean_parameters, avoid_classes_with_only_static_members
import "dart:ffi";
import "../typedefs.dart";

@Packed(4)
final class SteamInputDeviceDisconnected extends Struct {
  static int get callbackId => 2802;

  @UnsignedLongLong()
  external InputHandle disconnectedDeviceHandle;
}

extension SteamInputDeviceDisconnectedExtensions
    on Pointer<SteamInputDeviceDisconnected> {
  InputHandle get disconnectedDeviceHandle => ref.disconnectedDeviceHandle;
}
