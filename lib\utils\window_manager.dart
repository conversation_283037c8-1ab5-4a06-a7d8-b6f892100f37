import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:fullscreen_window/fullscreen_window.dart';
import 'package:path/path.dart' as path;

/// 窗口管理器类，用于处理窗口操作
class WindowManager {
  /// 在新实例中打开游戏项目
  ///
  /// [projectPath] 项目路径
  /// [workshopItemId] 创意工坊项目ID
  /// [title] 窗口标题
  /// [windowSize] 窗口尺寸，如果提供则使用该尺寸，否则使用默认尺寸
  /// [extraArguments] 额外参数，将被合并到传递给新窗口的参数中
  static Future<Process> openGameInNewInstance({
    required String projectPath,
    required String projectName,
    required String workshopItemId,
    Size? windowSize,
    Map<String, dynamic>? extraArguments,
  }) async {
    try {
      // 加载用户设置，检查是否需要全屏打开新窗口
      final prefs = await SharedPreferences.getInstance();
      final bool shouldOpenFullscreen =
          prefs.getBool('newWindowFullScreen') ?? false;

      // 准备传递给新窗口的参数
      final Map<String, dynamic> arguments = {
        'projectPath': projectPath,
        'projectName': projectName,
        'workshopItemId': workshopItemId,
        'shouldOpenFullscreen': shouldOpenFullscreen, // 传递全屏设置给新窗口
        'mode': 'new_instance', // 标识这是一个新实例
      };

      // 合并额外参数
      if (extraArguments != null) {
        arguments.addAll(extraArguments);
      }

      // 将参数转换为JSON字符串并进行Base64编码，避免命令行参数中的特殊字符问题
      final String argsJson = jsonEncode(arguments);
      final String argsBase64 = base64Encode(utf8.encode(argsJson));
      print('传递给新实例的参数(Base64编码): $argsBase64');
      print('参数原始内容: $argsJson');

      // 获取当前可执行文件路径
      String executablePath;
      // 获取当前工作目录
      String workingDirectory = Directory.current.path;

      if (Platform.isWindows) {
        executablePath = Platform.resolvedExecutable;
      } else if (Platform.isLinux || Platform.isMacOS) {
        executablePath = Platform.resolvedExecutable;
      } else {
        throw UnsupportedError('不支持的平台: ${Platform.operatingSystem}');
      }

      print('启动新实例: $executablePath');
      print('工作目录: $workingDirectory');

      // 启动新进程运行当前应用
      // 使用Base64编码的参数，以避免命令行参数解析问题
      final process = await Process.start(
        executablePath,
        ['--new-instance-base64', argsBase64],
        workingDirectory: workingDirectory, // 设置工作目录，确保资源正确加载
        mode: ProcessStartMode.detached,
      );

      // 注册进程退出处理
      process.exitCode.then((exitCode) {
        print('子进程已退出，退出码: $exitCode');
      });

      // 收集错误输出用于调试
      process.stderr.transform(utf8.decoder).listen((data) {
        print('子进程错误输出: $data');
      });

      print('新实例启动成功，PID: ${process.pid}');

      // 给进程一些启动时间
      await Future.delayed(const Duration(milliseconds: 500));

      return process;
    } catch (e) {
      print('创建新实例失败: $e');
      rethrow; // 重新抛出异常，让调用者处理
    }
  }

  /// 向后兼容的旧窗口创建方法，使用多实例代替多窗口
  /// 保留接口兼容性，但实际使用多实例启动
  static Future<WindowControllerWrapper> openGameInNewWindow({
    required String projectPath,
    required String projectName,
    required String workshopItemId,
    Size? windowSize,
    Map<String, dynamic>? extraArguments,
  }) async {
    try {
      final process = await openGameInNewInstance(
        projectPath: projectPath,
        projectName: projectName,
        workshopItemId: workshopItemId,
        windowSize: windowSize,
        extraArguments: extraArguments,
      );

      // 返回一个包装器，提供类似WindowController的API
      return WindowControllerWrapper(process);
    } catch (e) {
      print('创建新窗口失败 (兼容模式): $e');
      rethrow;
    }
  }
}

/// 包装Process提供类似以前API的兼容性
class WindowControllerWrapper {
  final Process _process;
  final int windowId = -1; // 使用-1表示这是实例而非窗口

  WindowControllerWrapper(this._process);

  /// 关闭实例
  Future<void> close() async {
    if (Platform.isWindows) {
      _process.kill(ProcessSignal.sigterm);
    } else {
      _process.kill();
    }
  }

  /// 这些方法仅为兼容API，实际不执行任何操作
  void setTitle(String title) {}
  void setFrame(Rect rect) {}
  void center() {}
  void show() {}
}
