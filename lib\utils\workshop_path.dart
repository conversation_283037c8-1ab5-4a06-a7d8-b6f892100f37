import 'dart:io';
import 'package:path/path.dart' as path;
import 'package:steamworks/steamworks.dart';
import 'steam_globals.dart';

class WorkshopPath {
  /// 获取创意工坊项目的根目录路径
  static String getShopPath(String workshopItemId) {
    final appId = SteamGlobals.instance.appId;
    
    final currentDir = Directory.current;
    final workshopPath = path.join(currentDir.path, '../..', 'workshop', 'content', '$appId', workshopItemId);
    
    // 打印路径信息以便调试
    print('创意工坊项目路径: $workshopPath');
    print('当前目录: ${currentDir.path}');
    print('应用ID: $appId');
    print('创意工坊项目ID: $workshopItemId');
    
    // 检查路径是否存在
    final dir = Directory(workshopPath);
    if (!dir.existsSync()) {
      print('警告: 创意工坊项目路径不存在: $workshopPath');
    }
    
    return workshopPath;
  }
  
  /// 获取创意工坊项目中特定文件的完整路径
  static String getFilePathInWorkshop(String workshopItemId, String filePath) {
    final basePath = getShopPath(workshopItemId);
    return path.join(basePath, filePath);
  }
  
  /// 获取创意工坊项目中FLOWCHART.json文件的路径
  static String getFlowchartPath(String workshopItemId) {
    return getFilePathInWorkshop(workshopItemId, 'FLOWCHART.json');
  }
  
  /// 获取创意工坊项目中的第一张图片路径（用于封面）
  static Future<String?> getFirstImagePath(String workshopItemId) async {
    final basePath = getShopPath(workshopItemId);
    final dir = Directory(basePath);
    
    if (await dir.exists()) {
      try {
        final List<FileSystemEntity> entities = await dir.list().toList();
        for (var entity in entities) {
          if (entity is File) {
            final extension = path.extension(entity.path).toLowerCase();
            if (['.jpg', '.jpeg', '.png', '.gif'].contains(extension)) {
              return entity.path;
            }
          }
        }
      } catch (e) {
        print('获取工作坊项目图片时出错: $e');
      }
    }
    
    return null;
  }
}