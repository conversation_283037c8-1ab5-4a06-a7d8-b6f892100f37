import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ve/next-gen-ui/styles.dart';
import 'package:ve/generated/l10n.dart';

class CreditsView extends StatefulWidget {
  const CreditsView({Key? key}) : super(key: key);

  @override
  State<CreditsView> createState() => _CreditsViewState();
}

class _CreditsViewState extends State<CreditsView> with TickerProviderStateMixin {
  late AnimationController _scrollController;
  late AnimationController _backgroundController;
  late AnimationController _particleController;
  final List<Particle> _particles = [];
  final int _particleCount = 50;
  
  // 定义为静态常量
  static const String an = '陟诚';

  @override
  void initState() {
    super.initState();
    
    // 创建背景动画控制器
    _backgroundController = AnimationController(
      vsync: this, 
      duration: const Duration(seconds: 20),
    )..repeat();
    
    // 创建粒子动画控制器
    _particleController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 10),
    )..repeat();
    
    // 创建滚动动画控制器 - 增加持续时间确保所有内容可见
    _scrollController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 240), // 增加到240秒
    );
    
    // 初始化粒子
    _initParticles();
    
    // 开始滚动动画
    _scrollController.forward();
    
    // 监听滚动结束事件
    _scrollController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        // 动画结束后，等待一会儿再重新开始
        Future.delayed(const Duration(seconds: 3), () {
          _scrollController.reset();
          _scrollController.forward();
        });
      }
    });
  }

  void _initParticles() {
    final random = math.Random();
    for (int i = 0; i < _particleCount; i++) {
      _particles.add(
        Particle(
          x: random.nextDouble() * 1.sw,
          y: random.nextDouble() * 1.sh,
          size: random.nextDouble() * 10 + 2,
          speedX: (random.nextDouble() - 0.5) * 2,
          speedY: (random.nextDouble() - 0.5) * 2,
          color: AppColors.orbColors[random.nextInt(AppColors.orbColors.length)]
              .withOpacity(random.nextDouble() * 0.5 + 0.1),
        ),
      );
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _backgroundController.dispose();
    _particleController.dispose();
    super.dispose();
  }
  

  
  // 定义制作人员数据
  List<CreditSection> _getCreditSections(BuildContext context) {
    final s = S.of(context);
    return [
      CreditSection(
        title: s.creditsCreativeTitle,
        items: [
          CreditItem(role: s.creditsGameProducer, name: an),
          CreditItem(role: s.creditsCreativeSource, name: an),
          CreditItem(role: s.creditsSoftwarePlanner, name: an),
          CreditItem(role: s.creditsGameplayPlanner, name: an),
        ],
      ),
      CreditSection(
        title: s.creditsProgrammingTitle,
        items: [
          CreditItem(role: s.creditsSoftwareArchitect, name: an),
          CreditItem(role: s.creditsCodeWriter, name: an),
        ],
      ),
      CreditSection(
        title: s.creditsVisualTitle,
        items: [
          CreditItem(role: s.creditsArtDesigner, name: an),
          CreditItem(role: s.creditsAnimationDesigner, name: an),
        ],
      ),
      CreditSection(
        title: s.creditsMarketingTitle,
        items: [
          CreditItem(role: s.creditsVideoProducer, name: an),
          CreditItem(role: s.creditsCopywriter, name: an),
        ],
      ),
      CreditSection(
        title: s.creditsThanksTitle,
        items: [
          CreditItem(role: s.creditsSpecialThanks, name: "娇娇_pkQjjVm\n小仓凛凛 • 科锐兹 • 六四不是一组\nmo8098372466 • 誓言风化千年 • 鉴赏家协会-小罗 • 晚风舟\n落花樱Sakura • 会飞的阿天 • 下一世再聚缘 • 鹿人店的金银花鹿\nwzzzzzzzz8 • 天涯沦落人 • 陌柏 • 一只紫薯\n爱做梦的阿奇 • Miko35P • mo3061897037 • 孤风斩影\n八重○艾莉卡 • 白磷P4 • 千穗 • 兔哟哟\n花见月猫姬 • 轻言细语 • 紫苑寺鸣海 • 十字唱诗班\n祈瞳 • mo7065549790 • mo7775244663 • 德行行清没想到吧名字竟有四种读法\n小风啊 • mo6367504750 • Xuntio • 琢子的极乐净土\nmo7388873135 • 菘蓝_Suzuran • mo1742729987 • Greyeon\nLunoEkusu • oubeichen • 杂鱼LOCK酱 • 侯登科\n星痕幻惜 • SovietLenin • Garakowa Marshmallow • 请叫我名侦探\n信随风起 • mo6266641793 • 余弦定理的估计 • 猴子611\n鬼上身 • 盒刷念路 • Katze am Fenster • 枫叶将故事染色\nmirakyux • mo5222442222 • 拉面君come_in • 城堡团长\nRebel • 儿忠大魔王 • mo1784247196 • mo3629571109\n七星娱乐 • 髏_ • 清雪榛子 • 如林若冰\n白湙程 • DryIce • mo2828291625 • 疯猫596\n益世 • 拾玖刑天 • 莫问清 • 小骡战士\n非酋菌 • carrotstealercat • TPking • 一炁無方\n不可逆转的宿命 • 我是邪王真眼的狗 • 神无喵 • 无垠空\n落寞的洛墨 • 韵律奏鸣 • 小祈的被窝 • 三文鱼\nAlgenib • 啊與世事與啊 • 逆光鸽 • mo5214049420\n一名小萌新Canny • 进化吧胡萝卜 • 芙澜 • Prosie\n郑跃强 • 微生咏歌 • 不正经的wsw • Cloud009\n杨杨Elena • 七味唐辛子 • gelao • 风铃的声音～\nmo7437676303 • 孟小然 • 卡特之旅 • 譯泽\n神楽Akumi • 姜涤雪 • mo5406096903 • 梦耀曦晨是只龙\n中登 • 缄默zhe • triint • Stelaro\nmczjx • 亾寰 • 小肥球 • 参数型逻辑\nyaoyao368 • 倉小唯 • 沁暄歆逸 • 游天梦穗\nYZCZERO&YZH • 希尔维亚的秘密基地 • 黑骑士の羁绊 • DR7\n望祭 • Onodye • 明天不散步了 • Lucascinko\nZero_Returnee • 小小流饭 • 犬馬不復來🌊 • 萬次\n久島鸥 • 芙樂艾 • 化回 • 千W的小曲儿\n梓恩 • 八二年的可乐 • 夜刀神清十郎 • 万独孤\n程家小将 • 心做し__ • 密斯卡托尼克的中专仔 • 廖木三郎\nmo7614771733 • Let me alone • 野良生 • Dreamboat\n冰风 • 狼_拜月 • 萌白苏生 • 神谕想要兔子\nmo86215699 • 落雨天上烟 • 安寝喵 • Lia333\nKana_ • 如月咏星 • 镜框 • 悠二gk\n白芝士气泡水 • FunX • 东东poi • FRISH\n诸世宇 • 悉尼的地科 • 葱香小嘤桃 • 狼_拜月\n渡鸦117 • 天下星 • 桜時雨 • 没头螺丝\n爱喝苹果汁 • Aeileen_Celia • No_Utopia • 米哈伊尔\nLiTi • 布洛妮娅爱玩BA • 多拉A梦wjj • 阿斯塔罗特\nMega • 薯薯想死在vrchat里 • Katze am Fenster • 魔劼座\nVpr字 • 浪浪星云 • 周旦 • 觅密空间\n云端噢 • 叶冬雪 • 浩劫 • 无聊的路人癸\n努力早睡的小猫 • 寒夏秋风 • 可靠的汉斯 • 高深\n玻璃信 • 苏塞克斯cc • 小坑酱 • 釘釘在競技場\n由由 • mo4150839874 • 萌萌雨 • Lynx87\nChinese_Ma • mo1440789839 • 只是出来打酱油 • 夜焉时\n一木栖 • Guan_Li_Yuan • mo8491831023 • 忆昔矣\n。。。_JNqOgwg • Onodye • 曾经的局座 • 中午12点的猫\n名字什么不必要 • 铸剑雨 • 宇宙第一亮仔 • DryIce\n暮今 • 奈斯菟谜呦 • 梦天歌 • 骚k哒\n悠月凝光 • 少年真不错 • ༺ཌ࿈锋埋࿈ད༻ • 朱培毅\n咕咕菜鸽子 • Clemons • 哎呦嚯呀那个哐哐才 • 弥弥古丽\n我是炉火huuo • 粗心的然 • 梦始终 • yca\nmo3835334018 • mo8327988121 • 木槿暖夏 • 知无终\nbasce • o0o0o0o • 呱呱_来自马来西亚 • 兰兰淦金金\nC次方 • mo8832960641 • mo5086589599 • 薪宇&铎履\nmo4795620058 • 路过的黑桃K • mo9786275242 • 何子荏\n天马行空驰驰 • mo5795755328 • ycx • mo9601996875\nmo3044155115 • 蓝泊 • Wzyy鱼茶syh • 夏云仙\nCapWG • Lainconscious • 寒潮是也 • mo4319180997\nmo7889325405 • mo9249451589 • mo4760838916 • 缘翎阳\n诺言一 • mo7771823989 • 金魚鉢 • 听梦\n清辞_Dmar • mo1137652833 • mo8987464362 • 小王子shigeruchan\nwuyuao • 无道拙 • mo7953423945 • 楚之歌\n黑与白314 • mo2312826384 • mo2757468512 • 百辰时井\n捡到一分钱 • 辰启星明 • mo5809160750 • rocky\nmo7970387437 • katlod • mo3631253431 • 斐雨\nmo7324532384 • mo1520303704 • 萌即昰正义 • zrrzw\n葉桜sakura • 菘蓝_Suzuran • mo8180131017 • 那一面红旗\nmo9743588029 • 栏杆 • mo8376451708 • mo3849252888\n—宸— • 静候naive • mo9408590610 • 道_天\n你好animal • 月亮鲸鱼 • Bourbon • 千刊\nHOCYI • 叶梦曦喵 • mo7775365260 • 纯白星空\n_sy • 双木与行 • 樱小路衣远 • emmm诚emmm\n喜欢吃萝卜的狗 • 沐橙雪秋 • 瞳顾 • 枕月眠\nmo1580120376 • h79t • 迷路猪 • zpc_19\nmo5768800525 • v零 • StitchXQ • 最爱丛雨\nmo6270705132 • mo6107985501 • 秋之牧风 • 变穷啦\n朱泳杰 • 疯狂新奇柿 • 沈若飞 • 紫域晨星\n吾月 • 懿雪 • 无名灬 • 日月而明\nmo7417947793 • 枫落凉城 • mo2263243548 • 回笼觉主咸鱼\n山井丰 • 雨时归 • 神剑万花筒 • 芬格里\n1036528763 • 重命名空气人 • Star Dream Studio • M1lo5uiii\nmddman • 欣阁 • 慕槿颜 • 暮霖胧Stardew\n高贵女人 • 人间过客1048323512 • 田园脆鸡堡 • mo2500043654\n博隆 • 盒友天下大同 • 玉风h • 虛空使徒\n四九浔一 • 白兰 • yir憔悴 • 幽薮\nhomo拉酱 • 百晓呀 • aysk17 • mo3868811000\n流辰蝴蝶 • 私缇 • MistyStille • 雨葵\n芸歌子 • Wilawi • 在下陆诩 • mo7329153249\n龙吟天明 • freelamn • 石墨烯_c • 雾酥055\n南咲佳 • 月寒1214 • 折芳馨遗所失 • 听歌的鸽子\n眷安 • 雪風 • 雨野 • 御宅卍解\n曦云逸 • 桐庭有灵 • 王诗文参上 • 叶者。。。\n壶鸽 • 孤心凉月 • mo3441130619 • 金乘三不做咸鱼啦\nmo6606918916 • 难以捉摸的黑白 • mo5429284452 • 忆恒怅\nmo6732627354 • mo4537933905 • 黑糖银子 • Vanx范克斯\n李夕竹 • 沙辰哀耶 • No4Marry • mo7276384292\n以前还是好人 • mo6209664243 • 有内涵的原子 • 天堂月歌\nBlitzMomo • chihaya_anonchan • mo1110357073 • 林大叔\n月茶茶月茶 • 王尊沧职 • 剪掉过去 • 喵娃囧子\nOUSNADY • mo1600524699 • 残雪雪雪Nya • 究鉽戨姬蔠倁寂\n??? • 乐剑清、乐琰、陈小猫、千年和阿角 • 软糯香甜可爱兔丸子 • 黄桃酪酪\n优质炼瞳从入门到入土 • 残霞琉璃 • 宥黠 • 夏沫9420\n圣境微流 • mo8832960641 • 轻言细语"),
        ],
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    final creditSections = _getCreditSections(context);
    
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // 背景动画层
          AnimatedBuilder(
            animation: _backgroundController,
            builder: (context, child) {
              return CustomPaint(
                size: Size(1.sw, 1.sh),
                painter: BackgroundPainter(_backgroundController.value),
              );
            },
          ),
          
          // 粒子动画层
          AnimatedBuilder(
            animation: _particleController,
            builder: (context, child) {
              return CustomPaint(
                size: Size(1.sw, 1.sh),
                painter: ParticlePainter(_particles, _particleController.value),
              );
            },
          ),
          
          // 内容层
          SafeArea(
            child: Column(
              children: [
                Padding(
                  padding: EdgeInsets.only(top: 16.h, right: 16.w),
                  child: Align(
                    alignment: Alignment.topRight,
                    child: IconButton(
                      icon: const Icon(Icons.close, color: Colors.white, size: 30),
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                  ),
                ),
                
                SizedBox(height: 20.h),
                
                // 标题
                ShaderMask(
                  shaderCallback: (bounds) {
                    return LinearGradient(
                      colors: AppColors.orbColors,
                      stops: const [0.0, 0.5, 1.0],
                      transform: GradientRotation(_backgroundController.value * 2 * math.pi),
                    ).createShader(bounds);
                  },
                  child: Text(
                    S.of(context).credits,
                    style: TextStyles.h2.copyWith(
                      fontSize: 48,
                      letterSpacing: 8,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                
                SizedBox(height: 40.h),
                
                // 滚动内容
                Expanded(
                  child: AnimatedBuilder(
                    animation: _scrollController,
                    builder: (context, child) {
                      // 计算滚动位置
                      final scrollPosition = _scrollController.value;
                      final startPosition = 1.sh * 0.6;
                      // 更准确地估算总高度，考虑到特别感谢部分的长度
                      double estimatedHeight = 0;
                      for (var section in creditSections) {
                        estimatedHeight += 100.h; // 标题和间距
                        for (var item in section.items) {
                          // 估算每个item的高度，特别是特别感谢部分
                          if (item.role == S.of(context).creditsSpecialThanks) {
                            // 特别感谢部分现在重新变成多行格式，按换行符计算行数
                            final lineCount = item.name.split('\n').length;
                            estimatedHeight += lineCount * 35.h + 80.h; // 每行35h + 额外间距
                          } else {
                            estimatedHeight += 80.h; // 普通item高度
                          }
                        }
                        estimatedHeight += 60.h; // section间距
                      }
                      // 增加额外的缓冲空间确保完整显示
                      estimatedHeight += 0.h;
                      final endPosition = -estimatedHeight;
                      final position = startPosition + scrollPosition * (endPosition - startPosition);
                      
                      return Transform.translate(
                        offset: Offset(0, position),
                        child: child,
                      );
                    },
                    child: Column(
                      children: creditSections.map((section) {
                        return _buildCreditSection(section);
                      }).toList(),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCreditSection(CreditSection section) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 30.h),
      child: Column(
        children: [
          // 部分标题
          ShaderMask(
            shaderCallback: (bounds) {
              return LinearGradient(
                colors: [AppColors.accent1, Colors.white],
                stops: const [0.0, 1.0],
              ).createShader(bounds);
            },
            child: Text(
              section.title,
              style: TextStyles.h3.copyWith(
                letterSpacing: 4,
                fontSize: 28,
              ),
            ),
          ),
          
          SizedBox(height: 20.h),
          
          // 部分内容
          ...section.items.map((item) {
            return Padding(
              padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 8.w),
              child: Column(
                children: [
                  // 职位（副标题，渐变色，字重更轻，字母间距更大）
                  ShaderMask(
                    shaderCallback: (bounds) {
                      return LinearGradient(
                        colors: [AppColors.accent1, AppColors.orbColors[1]],
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                      ).createShader(bounds);
                    },
                    child: Text(
                      item.role,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w400,
                        letterSpacing: 2,
                        fontFamily: 'Exo',
                        color: Colors.white,
                        shadows: [
                          Shadow(
                            color: Colors.black.withOpacity(0.2),
                            blurRadius: 4,
                            offset: Offset(1, 2),
                          ),
                        ],
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  SizedBox(height: 8.h),
                  // 名字（主标题，白色发光，字重更高，带底部渐变横线）
                  Stack(
                    alignment: Alignment.center,
                    children: [
                      // 发光效果
                      Text(
                        item.name,
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          fontFamily: 'Exo',
                          color: Colors.white,
                          letterSpacing: 1.5,
                          shadows: [
                            Shadow(
                              color: AppColors.orbColors[0].withOpacity(0.7),
                              blurRadius: 12,
                            ),
                            Shadow(
                              color: Colors.white.withOpacity(0.5),
                              blurRadius: 4,
                            ),
                          ],
                        ),
                        textAlign: TextAlign.center,
                      ),
                      // 底部渐变横线
                      Positioned(
                        bottom: -8,
                        left: 32,
                        right: 32,
                        child: Container(
                          height: 3,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(2),
                            gradient: LinearGradient(
                              colors: [AppColors.orbColors[0], AppColors.orbColors[2]],
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            );
          }).toList(),
        ],
      ),
    );
  }
}

// 粒子类
class Particle {
  double x;
  double y;
  double size;
  double speedX;
  double speedY;
  Color color;

  Particle({
    required this.x,
    required this.y,
    required this.size,
    required this.speedX,
    required this.speedY,
    required this.color,
  });

  void update(double delta, Size size) {
    x += speedX;
    y += speedY;

    if (x < 0 || x > size.width) {
      speedX *= -1;
    }
    if (y < 0 || y > size.height) {
      speedY *= -1;
    }
  }
}

// 粒子绘制器
class ParticlePainter extends CustomPainter {
  final List<Particle> particles;
  final double animation;

  ParticlePainter(this.particles, this.animation);

  @override
  void paint(Canvas canvas, Size size) {
    for (var particle in particles) {
      particle.update(0.016, size);
      
      final paint = Paint()
        ..color = particle.color
        ..style = PaintingStyle.fill;

      final glowPaint = Paint()
        ..color = particle.color.withOpacity(0.3)
        ..style = PaintingStyle.fill
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 15);

      canvas.drawCircle(
        Offset(particle.x, particle.y),
        particle.size * 2.0,
        glowPaint,
      );
      
      canvas.drawCircle(
        Offset(particle.x, particle.y),
        particle.size,
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant ParticlePainter oldDelegate) => true;
}

// 背景绘制器
class BackgroundPainter extends CustomPainter {
  final double animation;

  BackgroundPainter(this.animation);

  @override
  void paint(Canvas canvas, Size size) {
    final centerX = size.width / 2;
    final centerY = size.height / 2;
    
    // 绘制渐变背景
    final backgroundPaint = Paint()
      ..shader = RadialGradient(
        colors: [
          Colors.indigo.shade900,
          Colors.black,
        ],
        stops: const [0.2, 1.0],
        center: Alignment.center,
        radius: 1.0,
      ).createShader(Rect.fromLTWH(0, 0, size.width, size.height));
    
    canvas.drawRect(
      Rect.fromLTWH(0, 0, size.width, size.height),
      backgroundPaint,
    );
    
    // 绘制光效网格
    final gridPaint = Paint()
      ..color = Colors.cyanAccent.withOpacity(0.1)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;
    
    const gridSize = 50.0;
    final gridCount = (size.width / gridSize).ceil();
    
    for (int i = 0; i <= gridCount; i++) {
      final x = i * gridSize;
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        gridPaint,
      );
    }
    
    for (int i = 0; i <= (size.height / gridSize).ceil(); i++) {
      final y = i * gridSize;
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        gridPaint,
      );
    }
    
    // 绘制圆形光晕
    final glowPaint = Paint()
      ..shader = RadialGradient(
        colors: [
          AppColors.orbColors[0].withOpacity(0.3),
          AppColors.orbColors[1].withOpacity(0.1),
          Colors.transparent,
        ],
        stops: const [0.0, 0.5, 1.0],
      ).createShader(
        Rect.fromCircle(
          center: Offset(
            centerX + math.sin(animation * 2 * math.pi) * 100,
            centerY + math.cos(animation * 2 * math.pi) * 50,
          ),
          radius: math.max(size.width, size.height) * 0.4,
        ),
      );
    
    canvas.drawCircle(
      Offset(
        centerX + math.sin(animation * 2 * math.pi) * 100,
        centerY + math.cos(animation * 2 * math.pi) * 50,
      ),
      math.max(size.width, size.height) * 0.4,
      glowPaint,
    );
  }

  @override
  bool shouldRepaint(covariant BackgroundPainter oldDelegate) => true;
}

// 制作人员部分
class CreditSection {
  final String title;
  final List<CreditItem> items;

  CreditSection({
    required this.title,
    required this.items,
  });
}

// 制作人员条目
class CreditItem {
  final String name;
  final String role;

  CreditItem({
    required this.name,
    required this.role,
  });
} 