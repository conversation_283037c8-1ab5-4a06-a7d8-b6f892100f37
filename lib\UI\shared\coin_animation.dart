import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

/// 金币获取动画组件
/// 显示金币获取的动画效果，让用户感到活泼愉悦
class CoinAnimation extends StatefulWidget {
  final int coinAmount;
  final VoidCallback? onAnimationComplete;

  const CoinAnimation({
    Key? key,
    required this.coinAmount,
    this.onAnimationComplete,
  }) : super(key: key);

  @override
  State<CoinAnimation> createState() => _CoinAnimationState();
}

class _CoinAnimationState extends State<CoinAnimation> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  final List<_CoinParticle> _particles = [];
  final Random _random = Random();

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2000),
    );

    // 创建金币粒子
    _createParticles();

    // 启动动画
    _controller.forward().then((_) {
      if (widget.onAnimationComplete != null) {
        widget.onAnimationComplete!();
      }
    });
  }

  void _createParticles() {
    // 创建10个金币粒子
    for (int i = 0; i < 10; i++) {
      _particles.add(_CoinParticle(
        initialPosition: Offset(_random.nextDouble() * 100 - 50, 0),
        targetPosition: Offset(_random.nextDouble() * 200 - 100, -150 - _random.nextDouble() * 100),
        rotationSpeed: _random.nextDouble() * 10 - 5,
        delay: _random.nextInt(500),
      ));
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // 背景遮罩
        Positioned.fill(
          child: Container(
            color: Colors.black.withOpacity(0.3),
          ),
        ),
        
        // 金币数量显示
        Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '+${widget.coinAmount}',
                style: const TextStyle(
                  fontSize: 48,
                  fontWeight: FontWeight.bold,
                  color: Colors.amber,
                  shadows: [
                    Shadow(color: Colors.black45, blurRadius: 10, offset: Offset(2, 2)),
                  ],
                ),
              ).animate()
                .scale(begin: Offset(0.5, 0.5), end: Offset(1.2, 1.2), duration: 400.ms, curve: Curves.easeOutBack)
                .then(duration: 200.ms)
                .scale(begin: Offset(1.2, 1.2), end: Offset(1.0, 1.0)),
              
              const SizedBox(height: 16),
              
              const Text(
                '金币奖励',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ).animate()
                .fadeIn(delay: 300.ms, duration: 400.ms)
                .moveY(begin: 20, end: 0, delay: 300.ms, duration: 400.ms, curve: Curves.easeOutQuad),
            ],
          ),
        ),
        
        // 金币粒子
        ...List.generate(_particles.length, (index) {
          final particle = _particles[index];
          
          return AnimatedBuilder(
            animation: _controller,
            builder: (context, child) {
              // 计算当前位置
              final progress = _calculateProgress(_controller.value, particle.delay / 1000);
              final position = Offset.lerp(
                particle.initialPosition,
                particle.targetPosition,
                progress,
              )!;
              
              // 添加重力效果
              final gravityEffect = progress > 0.5 ? (progress - 0.5) * 2 * 100 : 0.0;
              
              return Positioned(
                left: MediaQuery.of(context).size.width / 2 + position.dx,
                top: MediaQuery.of(context).size.height / 2 + position.dy + gravityEffect,
                child: Transform.rotate(
                  angle: _controller.value * particle.rotationSpeed,
                  child: Opacity(
                    opacity: progress < 0.1 ? progress * 10 : (progress > 0.8 ? (1 - progress) * 5 : 1.0),
                    child: child!,
                  ),
                ),
              );
            },
            child: const Icon(
              Icons.monetization_on,
              color: Colors.amber,
              size: 30,
            ),
          );
        }),
      ],
    );
  }
  
  double _calculateProgress(double rawProgress, double delay) {
    // 考虑延迟
    final adjustedProgress = rawProgress - delay;
    if (adjustedProgress <= 0) return 0;
    if (adjustedProgress >= 1) return 1;
    return adjustedProgress;
  }
}

class _CoinParticle {
  final Offset initialPosition;
  final Offset targetPosition;
  final double rotationSpeed;
  final int delay; // 毫秒

  _CoinParticle({
    required this.initialPosition,
    required this.targetPosition,
    required this.rotationSpeed,
    required this.delay,
  });
}