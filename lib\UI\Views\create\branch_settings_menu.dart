import 'package:flutter/material.dart';
import 'package:flutter_flow_chart/flutter_flow_chart.dart';
import 'package:ve/generated/l10n.dart';
import 'package:path/path.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'dart:io';  // 添加Directory类的导入
import 'hooks_mobile.dart'  // 导入saveDashboard函数
    if (dart.library.js) 'hooks_web.dart';
import 'dart:convert';  // 添加jsonEncode函数的导入
import 'dart:math' as math;

// 定义分支类型枚举
enum BranchType {
  normal,   // 常规分支
  timed,    // 限时分支
  qte       // QTE分支
}

/// 分支设置菜单
class BranchSettingsMenu extends StatelessWidget {
  const BranchSettingsMenu({
    required this.element,
    required this.dashboard,
    super.key,
  });

  final FlowElement element;
  final Dashboard dashboard;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        // 使用对话框方式显示设置
        _showBranchSettingsDialog(context);
      },
      child: Text(S.of(context).setBranchParams),
    );
  }

  // 显示分支设置对话框
  void _showBranchSettingsDialog(BuildContext context) {
    // 保存本地化字符串的引用，避免在widget deactivate后使用context
    final S localizations = S.of(context);

    // 判断是否有分支
    if (element.next.isEmpty || element.next.length < 2) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(localizations.noBranchesToSet))
      );
      return;
    }

    // 创建控制器和状态变量
    final TextEditingController branchTitleController = TextEditingController(
      text: element.branchTitle ?? '',
    );

    double buttonOpacity = element.branchButtonOpacity ?? 0.9;
    // 确保透明度值在有效范围内，修改最小值从0.1到0.0
    buttonOpacity = buttonOpacity.clamp(0.0, 1.0);
    
    // 新增：按钮是否仅显示文字（无背景）
    bool buttonTextOnly = false;
    if (element.serializedData != null && element.serializedData!.isNotEmpty) {
      try {
        final data = jsonDecode(element.serializedData!) as Map<String, dynamic>;
        if (data.containsKey('buttonTextOnly')) {
          buttonTextOnly = data['buttonTextOnly'] as bool;
        }
      } catch (e) {
        debugPrint('解析按钮文字设置出错: $e');
      }
    }

    // 为每个分支创建控制器
    final Map<String, TextEditingController> buttonTextControllers = {};
    final Map<String, Offset> buttonPositions = {};

    // 初始化每个分支的控制器和位置
    for (var connection in element.next) {
      final destId = connection.destElementId;
      // 初始化文字控制器
      final defaultText = element.getBranchButtonText(destId);
      final textController = TextEditingController(
        text: defaultText.isNotEmpty ? defaultText : _getDestElementText(destId),
      );
      buttonTextControllers[destId] = textController;
      // 初始化位置
      buttonPositions[destId] = element.getBranchButtonPosition(destId);
    }

    // 当前选中的分支ID
    String? selectedBranchId = element.next.isNotEmpty ? element.next[0].destElementId : null;

    // 是否设置自定义位置，初始值为是否已有所有位置设置
    bool hasCustomPositions = element.branchButtonPositions != null && element.next.every(
      (connection) => element.branchButtonPositions!.containsKey(connection.destElementId)
    );

    // 获取分支类型（如果已设置）
    int currentTabIndex = 0; // 默认为常规分支
    if (element.serializedData != null && element.serializedData!.isNotEmpty) {
      try {
        final data = jsonDecode(element.serializedData!) as Map<String, dynamic>;
        if (data.containsKey('branchType')) {
          currentTabIndex = data['branchType'] as int;
        }
      } catch (e) {
        debugPrint('解析分支类型数据出错: $e');
      }
    }

    // 限时分支设置
    int timeLimit = 5; // 默认5秒，表示按钮显示时间
    String? autoSelectBranchId; // 默认自动选择分支

    // QTE分支设置
    int qteDuration = 3; // QTE按钮持续显示时间，默认3秒
    String? qteSuccessBranchId; // QTE成功时前往的分支
    String? qteFailBranchId; // QTE失败时前往的分支
    Offset qteButtonPosition = Offset(0.5, 0.5); // QTE按钮位置，默认屏幕中央

    // 如果有已保存的限时分支设置，则加载
    if (element.serializedData != null && element.serializedData!.isNotEmpty) {
      try {
        final data = jsonDecode(element.serializedData!) as Map<String, dynamic>;
        if (data.containsKey('timeLimit')) {
          timeLimit = data['timeLimit'] as int;
        }
        if (data.containsKey('autoSelectBranchId')) {
          autoSelectBranchId = data['autoSelectBranchId'] as String;
        }
        // 加载QTE分支设置
        if (data.containsKey('qteDuration')) {
          qteDuration = data['qteDuration'] as int;
        }
        if (data.containsKey('qteSuccessBranchId')) {
          qteSuccessBranchId = data['qteSuccessBranchId'] as String;
        }
        if (data.containsKey('qteFailBranchId')) {
          qteFailBranchId = data['qteFailBranchId'] as String;
        }
        if (data.containsKey('qteButtonPosition')) {
          final posData = data['qteButtonPosition'] as Map<String, dynamic>;
          qteButtonPosition = Offset(
            posData['dx'] as double,
            posData['dy'] as double
          );
        }
      } catch (e) {
        debugPrint('解析分支设置出错: $e');
      }
    }

    // 如果没有设置自动选择分支，默认选第一个
    if (autoSelectBranchId == null && element.next.isNotEmpty) {
      autoSelectBranchId = element.next[0].destElementId;
    }

    // 如果没有设置QTE成功/失败分支，默认设置前两个
    if (element.next.length >= 2) {
      qteSuccessBranchId ??= element.next[0].destElementId;
      qteFailBranchId ??= element.next[1].destElementId;
    }

    // 保存函数，返回是否成功的布尔值
    Future<bool> saveSettings() async {
      // 1. 保存分支标题
      element.setBranchTitle(branchTitleController.text);

      // 1.1 保存分支标题位置（如果已设置）
      if (element.branchTitlePosition != null) {
        element.setBranchTitlePosition(element.branchTitlePosition!);
      }

      // 2. 保存按钮透明度
      element.setBranchButtonOpacity(buttonOpacity);

      // 3. 保存每个分支按钮的文字
      final Map<String, String> buttonTexts = {};
      buttonTextControllers.forEach((destId, controller) {
        buttonTexts[destId] = controller.text;
      });
      element.setBranchButtonTexts(buttonTexts);

      // 4. 如果启用自定义位置，保存每个分支按钮的位置
      if (hasCustomPositions) {
        final Map<String, Offset> positions = Map.from(buttonPositions);
        element.setBranchButtonPositions(positions);
      } else {
        // 如果不启用自定义位置，清除位置设置
        element.setBranchButtonPositions({});
      }

      // 5. 保存分支类型和相关设置
      Map<String, dynamic> branchData = {};
      
      // 先读取原始serializedData，确保保留其他设置数据（如选项及数值变化设置）
      if (element.serializedData != null && element.serializedData!.isNotEmpty) {
        try {
          final data = jsonDecode(element.serializedData!) as Map<String, dynamic>;
          // 复制所有现有数据，但排除我们将要更新的字段
          data.forEach((key, value) {
            // 只保留不是分支类型控制的字段，如分支条件和数值变化设置
            if (key != 'branchType' && 
                key != 'timeLimit' && 
                key != 'autoSelectBranchId' && 
                key != 'qteDuration' && 
                key != 'qteSuccessBranchId' && 
                key != 'qteFailBranchId' && 
                key != 'qteButtonPosition' &&
                key != 'buttonTextOnly') {
              branchData[key] = value;
            }
          });
        } catch (e) {
          debugPrint('解析现有serializedData出错: $e');
        }
      }
      
      branchData['branchType'] = currentTabIndex;

      // 新增：保存按钮仅显示文字设置
      branchData['buttonTextOnly'] = buttonTextOnly;

      // 保存按钮显示时间设置（常规分支和限时分支都支持）
      if (currentTabIndex == 0 || currentTabIndex == 1) {
        branchData['timeLimit'] = timeLimit;
      }
      
      // 仅限时分支保存自动选择分支ID
      if (currentTabIndex == 1) {
        branchData['autoSelectBranchId'] = autoSelectBranchId;
      }
      
      // QTE分支特有设置
      if (currentTabIndex == 2) {
        branchData['qteDuration'] = qteDuration;
        branchData['qteSuccessBranchId'] = qteSuccessBranchId;
        branchData['qteFailBranchId'] = qteFailBranchId;
        branchData['qteButtonPosition'] = {
          'dx': qteButtonPosition.dx,
          'dy': qteButtonPosition.dy
        };
      }

      // 将Map转换为JSON字符串，保存到元素的serializedData字段
      element.serializedData = jsonEncode(branchData);

      // 6. 保存流程图到文件系统
      try {
        final savePath = dashboard.projectPath;
        if (savePath.isNotEmpty) {
          final directory = Directory(savePath);
          if (!await directory.exists()) {
            await directory.create(recursive: true);
          }

          // 使用saveDashboard函数保存流程图
          await saveDashboard(dashboard, directory);
          return true;
        }
        return true; // 只保存分支设置也算成功
      } catch (e) {
        debugPrint('保存流程图失败: $e');
        return false;
      }
    }

    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return StatefulBuilder(
          builder: (builderContext, setState) {
            return Dialog(
              child: Container(
                width: 800,
                constraints: BoxConstraints(
                  maxHeight: MediaQuery.of(builderContext).size.height * 0.85,
                  minHeight: math.min(600, MediaQuery.of(builderContext).size.height * 0.85),
                ),
                padding: const EdgeInsets.all(16),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 标题
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          localizations.setBranchParams,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                          ),
                        ),
                        IconButton(
                          icon: Icon(Icons.close),
                          onPressed: () => Navigator.of(dialogContext).pop(),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // 标签页选择器
                    Row(
                      children: [
                        _buildTabButton(
                          context,
                          localizations.normalBranch,
                          currentTabIndex == 0,
                          () => setState(() => currentTabIndex = 0)
                        ),
                        _buildTabButton(
                          context,
                          localizations.timedBranch,
                          currentTabIndex == 1,
                          () => setState(() => currentTabIndex = 1)
                        ),
                        _buildTabButton(
                          context,
                          localizations.qteBranch,
                          currentTabIndex == 2,
                          () => setState(() => currentTabIndex = 2)
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // 分隔线
                    Divider(),

                    // 标签页内容区域
                    Expanded(
                      child: SingleChildScrollView(
                        child: ConstrainedBox(
                          constraints: BoxConstraints(
                            minHeight: 400,
                          ),
                          child: IntrinsicHeight(
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // 左侧面板 - 分支列表和标题
                                Expanded(
                                  flex: 2,
                                  child: SingleChildScrollView(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        // 添加按钮显示时间设置（常规分支和限时分支都显示）
                                        if (currentTabIndex == 0 || currentTabIndex == 1) ...[
                                          Text(
                                            localizations.buttonDisplayTime,
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                              fontSize: 16,
                                            ),
                                          ),
                                          Text(
                                            localizations.buttonDisplayTimeDescription,
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: Colors.grey[700],
                                            ),
                                          ),
                                          Text(
                                            localizations.buttonDisplayTimeNote,
                                            style: TextStyle(
                                              fontSize: 11,
                                              color: Colors.grey[600],
                                              fontStyle: FontStyle.italic,
                                            ),
                                          ),
                                          const SizedBox(height: 8),
                                          Row(
                                            children: [
                                              Expanded(
                                                child: Slider(
                                                  value: timeLimit.toDouble(),
                                                  min: 0,
                                                  max: 30,
                                                  divisions: 29,
                                                  label: '$timeLimit ${localizations.seconds}',
                                                  onChanged: (value) {
                                                    setState(() {
                                                      timeLimit = value.round();
                                                    });
                                                  },
                                                ),
                                              ),
                                              SizedBox(
                                                width: 40,
                                                child: Text('$timeLimit', style: TextStyle(fontWeight: FontWeight.bold)),
                                              ),
                                            ],
                                          ),
                                          const SizedBox(height: 16),
                                          Divider(),
                                        ],

                                        // 限时分支特有的自动选择设置
                                        if (currentTabIndex == 1) ...[
                                          Text(
                                            localizations.autoSelect,
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                              fontSize: 16,
                                            ),
                                          ),
                                          const SizedBox(height: 8),

                                          // 自动选择下拉框
                                          DropdownButtonFormField<String>(
                                            value: autoSelectBranchId,
                                            decoration: InputDecoration(
                                              border: OutlineInputBorder(),
                                              contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                                            ),
                                            items: element.next.map((connection) {
                                              final destId = connection.destElementId;
                                              final text = buttonTextControllers[destId]?.text ?? localizations.branchIndexLabel(element.next.indexOf(connection) + 1).replaceAll(':', '');
                                              return DropdownMenuItem<String>(
                                                value: destId,
                                                child: Text(text),
                                              );
                                            }).toList(),
                                            onChanged: (value) {
                                              setState(() {
                                                autoSelectBranchId = value;
                                              });
                                            },
                                          ),
                                          const SizedBox(height: 16),
                                          Divider(),
                                        ],

                                        // QTE分支特有的设置
                                        if (currentTabIndex == 2) ...[
                                          Text(
                                            localizations.qteDuration,
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                              fontSize: 16,
                                            ),
                                          ),
                                          Text(
                                            localizations.qteDurationDescription,
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: Colors.grey[700],
                                            ),
                                          ),
                                          const SizedBox(height: 8),
                                          Row(
                                            children: [
                                              Expanded(
                                                child: Slider(
                                                  value: qteDuration.toDouble(),
                                                  min: 1,
                                                  max: 10,
                                                  divisions: 9,
                                                  label: '$qteDuration ${localizations.seconds}',
                                                  onChanged: (value) {
                                                    setState(() {
                                                      qteDuration = value.round();
                                                    });
                                                  },
                                                ),
                                              ),
                                              SizedBox(
                                                width: 40,
                                                child: Text('$qteDuration', style: TextStyle(fontWeight: FontWeight.bold)),
                                              ),
                                            ],
                                          ),
                                          const SizedBox(height: 16),
                                          Divider(),
                                          
                                          // QTE成功分支选择
                                          Text(
                                            localizations.qteSuccessBranch,
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                              fontSize: 16,
                                            ),
                                          ),
                                          const SizedBox(height: 8),
                                          DropdownButtonFormField<String>(
                                            value: qteSuccessBranchId,
                                            decoration: InputDecoration(
                                              border: OutlineInputBorder(),
                                              contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                                            ),
                                            items: element.next.map((connection) {
                                              final destId = connection.destElementId;
                                              final text = buttonTextControllers[destId]?.text ?? localizations.branchIndexLabel(element.next.indexOf(connection) + 1).replaceAll(':', '');
                                              return DropdownMenuItem<String>(
                                                value: destId,
                                                child: Text(text),
                                              );
                                            }).toList(),
                                            onChanged: (value) {
                                              setState(() {
                                                qteSuccessBranchId = value;
                                              });
                                            },
                                          ),
                                          const SizedBox(height: 16),
                                          
                                          // QTE失败分支选择
                                          Text(
                                            localizations.qteFailBranch,
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                              fontSize: 16,
                                            ),
                                          ),
                                          const SizedBox(height: 8),
                                          DropdownButtonFormField<String>(
                                            value: qteFailBranchId,
                                            decoration: InputDecoration(
                                              border: OutlineInputBorder(),
                                              contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                                            ),
                                            items: element.next.map((connection) {
                                              final destId = connection.destElementId;
                                              final text = buttonTextControllers[destId]?.text ?? localizations.branchIndexLabel(element.next.indexOf(connection) + 1).replaceAll(':', '');
                                              return DropdownMenuItem<String>(
                                                value: destId,
                                                child: Text(text),
                                              );
                                            }).toList(),
                                            onChanged: (value) {
                                              setState(() {
                                                qteFailBranchId = value;
                                              });
                                            },
                                          ),
                                          const SizedBox(height: 16),
                                          Divider(),
                                        ],

                                        Text(
                                          localizations.questionDescription,
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 16,
                                          ),
                                        ),
                                        const SizedBox(height: 8),
                                        TextField(
                                          controller: branchTitleController,
                                          decoration: InputDecoration(
                                            hintText: localizations.questionDescription,
                                            border: OutlineInputBorder(),
                                          ),
                                          textAlign: TextAlign.center,
                                        ),

                                        const SizedBox(height: 16),

                                        // 只在非QTE分支显示透明度设置
                                        if (currentTabIndex != 2) ...[
                                          // 分支按钮透明度设置
                                          Text(
                                            localizations.buttonOpacity,
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                              fontSize: 16,
                                            ),
                                          ),
                                          const SizedBox(height: 8),
                                          Row(
                                            children: [
                                              Text(localizations.transparent),
                                              Expanded(
                                                child: Slider(
                                                  value: buttonOpacity,
                                                  min: 0.0, // 修改最小值为0，实现完全透明
                                                  max: 1.0,
                                                  divisions: 10, // 调整为0到1.0的11个档位
                                                  label: '${(buttonOpacity * 100).round()}%',
                                                  onChanged: (value) {
                                                    setState(() {
                                                      buttonOpacity = value;
                                                    });
                                                  },
                                                ),
                                              ),
                                              Text(localizations.opaque),
                                            ],
                                          ),
                                          
                                          // 新增：仅显示文字选项（无按钮背景）
                                          const SizedBox(height: 12),
                                          Row(
                                            children: [
                                              Text(
                                                localizations.buttonTextOnly,
                                                style: TextStyle(
                                                  fontWeight: FontWeight.bold,
                                                ),
                                              ),
                                              const SizedBox(width: 12),
                                              Checkbox(
                                                value: buttonTextOnly,
                                                onChanged: (value) {
                                                  setState(() {
                                                    buttonTextOnly = value ?? false;
                                                  });
                                                },
                                              ),
                                            ],
                                          ),

                                          const SizedBox(height: 16),

                                          // 分支列表区域
                                          Text(
                                            localizations.buttonText,
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                              fontSize: 16,
                                            ),
                                          ),
                                          const SizedBox(height: 8),
                                          Container(
                                            height: 200, // 固定高度
                                            decoration: BoxDecoration(
                                              border: Border.all(color: Colors.grey.shade300),
                                              borderRadius: BorderRadius.circular(4),
                                            ),
                                            child: ListView.builder(
                                              shrinkWrap: true,
                                              itemCount: element.next.length,
                                              itemBuilder: (context, index) {
                                                final connection = element.next[index];
                                                final destId = connection.destElementId;
                                                final isSelected = selectedBranchId == destId;

                                                return ListTile(
                                                  selected: isSelected,
                                                  selectedTileColor: Colors.blue.withOpacity(0.1),
                                                  title: Row(
                                                    children: [
                                                      Text(localizations.branchIndexLabel(index + 1)),
                                                      const SizedBox(width: 8),
                                                      Expanded(
                                                        child: TextField(
                                                          controller: buttonTextControllers[destId],
                                                          decoration: InputDecoration(
                                                            hintText: localizations.buttonText,
                                                            border: OutlineInputBorder(),
                                                            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                                                          ),
                                                          textAlign: TextAlign.center,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                  onTap: () {
                                                    setState(() {
                                                      selectedBranchId = destId;
                                                    });
                                                  },
                                                );
                                              },
                                            ),
                                          ),
                                        ],
                                      ],
                                    ),
                                  ),
                                ),

                                // 中间分隔线
                                VerticalDivider(),

                                // 右侧面板 - 按钮位置设置
                                Expanded(
                                  flex: 3,
                                  child: SingleChildScrollView(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        // 自定义位置切换开关
                                        if (currentTabIndex != 2) ...[
                                          Row(
                                            children: [
                                              Text(
                                                localizations.customButtonPosition,
                                                style: TextStyle(
                                                  fontWeight: FontWeight.bold,
                                                  fontSize: 16,
                                                ),
                                              ),
                                              const SizedBox(width: 16),
                                              Checkbox(
                                                value: hasCustomPositions,
                                                onChanged: (value) {
                                                  setState(() {
                                                    hasCustomPositions = value ?? false;

                                                    // 如果切换到不自定义，清空所有位置设置
                                                    if (!hasCustomPositions) {
                                                      buttonPositions.clear();
                                                    } else {
                                                      // 如果切换到自定义，为每个按钮设置初始位置
                                                      final defaultPositions = _generateDefaultPositions(element.next.length);
                                                      for (int i = 0; i < element.next.length; i++) {
                                                        final destId = element.next[i].destElementId;
                                                        buttonPositions[destId] = defaultPositions[i];
                                                      }
                                                    }
                                                  });
                                                },
                                              ),
                                            ],
                                          ),

                                          const SizedBox(height: 16),
                                        ],

                                        // 按钮位置预览区域
                                        Container(
                                          height: 350, // 固定高度
                                          decoration: BoxDecoration(
                                            border: Border.all(color: Colors.grey.shade300),
                                            borderRadius: BorderRadius.circular(4),
                                          ),
                                          child: Stack(
                                            children: [
                                              // 背景文字
                                              Positioned.fill(
                                                child: Center(
                                                  child: Text(
                                                    localizations.screenPreview,
                                                    style: const TextStyle(
                                                      color: Colors.grey,
                                                      fontSize: 16,
                                                    ),
                                                  ),
                                                ),
                                              ),

                                              // 标题文字预览 - 添加可拖动功能
                                              if (branchTitleController.text.isNotEmpty)
                                                Positioned(
                                                  top: (element.branchTitlePosition?.dy ?? 0.1) * 300, // 使用百分比计算top位置
                                                  left: (element.branchTitlePosition?.dx ?? 0.5) * 400, // 使用纯百分比定位，去掉-100偏移
                                                  child: GestureDetector(
                                                    onPanUpdate: (details) {
                                                      setState(() {
                                                        // 计算新位置
                                                        final previewWidth = 400.0;
                                                        final previewHeight = 300.0;
                                                        final oldDx = (element.branchTitlePosition?.dx ?? 0.5) * previewWidth;
                                                        final oldDy = (element.branchTitlePosition?.dy ?? 0.1) * previewHeight;
                                                        final newDx = ((oldDx + details.delta.dx) / previewWidth).clamp(0.0, 1.0); // 确保水平位置在0-100%范围内
                                                        final newDy = ((oldDy + details.delta.dy) / previewHeight).clamp(0.0, 1.0); // 确保垂直位置在0-100%范围内
                                                        element.branchTitlePosition = Offset(newDx, newDy);
                                                      });
                                                    },
                                                    child: Container(
                                                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                                                      decoration: BoxDecoration(
                                                        color: Colors.black.withAlpha((0.6 * 255).round()),
                                                        borderRadius: BorderRadius.circular(4),
                                                        border: Border.all(color: Colors.yellow.withAlpha(100), width: 1),
                                                      ),
                                                      child: Text(
                                                        branchTitleController.text,
                                                        style: const TextStyle(
                                                          color: Colors.white,
                                                          fontSize: 14,
                                                        ),
                                                        textAlign: TextAlign.center, // 居中显示文本
                                                      ),
                                                    ),
                                                  ),
                                                ),

                                              // 按钮显示时间预览（常规和限时分支都显示）
                                              if (currentTabIndex == 0 || currentTabIndex == 1)
                                                Positioned(
                                                  top: branchTitleController.text.isNotEmpty ? 60 : 20,
                                                  left: 0,
                                                  right: 0,
                                                  child: Center(
                                                    child: Container(
                                                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                                                      decoration: BoxDecoration(
                                                        color: Colors.red.withAlpha((0.7 * 255).round()),
                                                        borderRadius: BorderRadius.circular(20),
                                                      ),
                                                      child: Text(
                                                        "${localizations.buttonDisplayTime}: $timeLimit ${localizations.seconds}",
                                                        style: const TextStyle(
                                                          color: Colors.white,
                                                          fontSize: 14,
                                                          fontWeight: FontWeight.bold,
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                ),

                                              // 非QTE分支的按钮预览
                                              if (currentTabIndex != 2) ...[
                                                if (hasCustomPositions) ...[
                                                  for (var entry in buttonPositions.entries)
                                                    Positioned(
                                                      left: entry.value.dx * 400, // 适应容器宽度
                                                      top: entry.value.dy * 300,  // 适应容器高度
                                                      child: GestureDetector(
                                                        onPanUpdate: (details) {
                                                          // 移除对selectedBranchId的检查，允许拖动任何按钮
                                                          setState(() {
                                                            // 计算新位置，确保在容器范围内
                                                            final newDx = (entry.value.dx * 400 + details.delta.dx) / 400;
                                                            final newDy = (entry.value.dy * 300 + details.delta.dy) / 300;

                                                            buttonPositions[entry.key] = Offset(
                                                              newDx.clamp(0.0, 1.0),
                                                              newDy.clamp(0.0, 1.0),
                                                            );

                                                            // 当拖动时自动选中当前按钮
                                                            selectedBranchId = entry.key;
                                                          });
                                                        },
                                                        child: Container(
                                                          width: 60,
                                                          height: 30,
                                                          decoration: buttonTextOnly
                                                              ? null // 如果仅显示文字，不设置背景装饰
                                                              : BoxDecoration(
                                                                  color: entry.key == selectedBranchId
                                                                      ? Colors.blue.withAlpha((buttonOpacity * 255).round())
                                                                      : Colors.grey.withAlpha((buttonOpacity * 255).round()),
                                                                  borderRadius: BorderRadius.circular(4),
                                                                  border: entry.key == selectedBranchId
                                                                      ? Border.all(color: Colors.yellow, width: 2)
                                                                      : (entry.key == autoSelectBranchId && currentTabIndex == 1)
                                                                        ? Border.all(color: Colors.red, width: 2)
                                                                        : null,
                                                                ),
                                                          child: Stack(
                                                            children: [
                                                              Center(
                                                                child: Text(
                                                                  buttonTextControllers[entry.key]?.text ?? '',
                                                                  style: TextStyle(
                                                                    color: buttonTextOnly 
                                                                        ? (entry.key == selectedBranchId ? Colors.blue : Colors.black)
                                                                        : Colors.white,
                                                                    fontSize: 10,
                                                                    fontWeight: buttonTextOnly ? FontWeight.bold : FontWeight.normal,
                                                                  ),
                                                                  overflow: TextOverflow.ellipsis,
                                                                ),
                                                              ),
                                                              // 标记为自动选择的按钮
                                                              if (entry.key == autoSelectBranchId && currentTabIndex == 1)
                                                                Positioned(
                                                                  top: 0,
                                                                  right: 0,
                                                                  child: Container(
                                                                    width: 10,
                                                                    height: 10,
                                                                    decoration: const BoxDecoration(
                                                                      color: Colors.red,
                                                                      shape: BoxShape.circle,
                                                                    ),
                                                                  ),
                                                                ),
                                                            ],
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                ] else ...[
                                                  Positioned(
                                                    bottom: 40,
                                                    left: 0,
                                                    right: 0,
                                                    child: Row(
                                                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                                      children: [
                                                        for (var connection in element.next)
                                                          Container(
                                                            width: 50,
                                                            height: 25,
                                                            decoration: buttonTextOnly
                                                                ? null // 如果仅显示文字，不设置背景装饰
                                                                : BoxDecoration(
                                                                    color: connection.destElementId == selectedBranchId
                                                                        ? Colors.blue.withAlpha((buttonOpacity * 255).round())
                                                                        : Colors.grey.withAlpha((buttonOpacity * 255).round()),
                                                                    borderRadius: BorderRadius.circular(4),
                                                                    border: connection.destElementId == selectedBranchId
                                                                        ? Border.all(color: Colors.yellow, width: 2)
                                                                        : (connection.destElementId == autoSelectBranchId && currentTabIndex == 1)
                                                                          ? Border.all(color: Colors.red, width: 2)
                                                                          : null,
                                                                  ),
                                                            child: Stack(
                                                              children: [
                                                                Center(
                                                                  child: Text(
                                                                    buttonTextControllers[connection.destElementId]?.text ?? '',
                                                                    style: TextStyle(
                                                                      color: buttonTextOnly
                                                                          ? (connection.destElementId == selectedBranchId ? Colors.blue : Colors.black)
                                                                          : Colors.white,
                                                                      fontSize: 9,
                                                                      fontWeight: buttonTextOnly ? FontWeight.bold : FontWeight.normal,
                                                                    ),
                                                                    overflow: TextOverflow.ellipsis,
                                                                  ),
                                                                ),
                                                                // 标记为自动选择的按钮
                                                                if (connection.destElementId == autoSelectBranchId && currentTabIndex == 1)
                                                                  Positioned(
                                                                    top: 0,
                                                                    right: 0,
                                                                    child: Container(
                                                                      width: 8,
                                                                      height: 8,
                                                                      decoration: const BoxDecoration(
                                                                        color: Colors.red,
                                                                        shape: BoxShape.circle,
                                                                      ),
                                                                    ),
                                                                  ),
                                                              ],
                                                            ),
                                                          ),
                                                      ],
                                                    ),
                                                  ),
                                                ],
                                              ],

                                              // QTE分支按钮预览
                                              if (currentTabIndex == 2)
                                                Positioned(
                                                  left: qteButtonPosition.dx * 400,
                                                  top: qteButtonPosition.dy * 300,
                                                  child: GestureDetector(
                                                    onPanUpdate: (details) {
                                                      setState(() {
                                                        // 计算新位置，确保在容器范围内
                                                        final newDx = (qteButtonPosition.dx * 400 + details.delta.dx) / 400;
                                                        final newDy = (qteButtonPosition.dy * 300 + details.delta.dy) / 300;
                                                        
                                                        qteButtonPosition = Offset(
                                                          newDx.clamp(0.0, 1.0),
                                                          newDy.clamp(0.0, 1.0),
                                                        );
                                                      });
                                                    },
                                                    child: Container(
                                                      width: 80,
                                                      height: 80,
                                                      decoration: BoxDecoration(
                                                        color: Colors.red.withAlpha(150),
                                                        shape: BoxShape.circle,
                                                        border: Border.all(color: Colors.yellow, width: 2),
                                                      ),
                                                      child: Center(
                                                        child: Text(
                                                          "QTE\n$qteDuration${localizations.seconds}",
                                                          style: const TextStyle(
                                                            color: Colors.white,
                                                            fontSize: 14,
                                                            fontWeight: FontWeight.bold,
                                                          ),
                                                          textAlign: TextAlign.center,
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              
                                              // QTE分支的成功/失败提示信息
                                              if (currentTabIndex == 2)
                                                Positioned(
                                                  top: 20,
                                                  left: 0,
                                                  right: 0,
                                                  child: Column(
                                                    children: [
                                                      Container(
                                                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                                                        decoration: BoxDecoration(
                                                          color: Colors.green.withAlpha(150),
                                                          borderRadius: BorderRadius.circular(4),
                                                        ),
                                                        child: Text(
                                                          "${localizations.qteSuccessLabel}: ${buttonTextControllers[qteSuccessBranchId]?.text ?? ''}",
                                                          style: const TextStyle(
                                                            color: Colors.white,
                                                            fontWeight: FontWeight.bold,
                                                          ),
                                                        ),
                                                      ),
                                                      SizedBox(height: 8),
                                                      Container(
                                                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                                                        decoration: BoxDecoration(
                                                          color: Colors.red.withAlpha(150),
                                                          borderRadius: BorderRadius.circular(4),
                                                        ),
                                                        child: Text(
                                                          "${localizations.qteFailLabel}: ${buttonTextControllers[qteFailBranchId]?.text ?? ''}",
                                                          style: const TextStyle(
                                                            color: Colors.white,
                                                            fontWeight: FontWeight.bold,
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                                
                                              // QTE位置信息
                                              if (currentTabIndex == 2)
                                                Positioned(
                                                  bottom: 10,
                                                  left: 0,
                                                  right: 0,
                                                  child: Center(
                                                    child: Text(
                                                      "${localizations.qteButtonPosition}: ${(qteButtonPosition.dx * 100).round()}%, ${(qteButtonPosition.dy * 100).round()}%",
                                                      style: const TextStyle(fontSize: 12, color: Colors.red),
                                                    ),
                                                  ),
                                                ),

                                              // 选中分支的位置信息
                                              if (hasCustomPositions && selectedBranchId != null && currentTabIndex != 2)
                                                Positioned(
                                                  bottom: 10,
                                                  left: 0,
                                                  right: 0,
                                                  child: Center(
                                                    child: Column(
                                                      children: [
                                                        Text(
                                                          localizations.buttonPositionInfo(
                                                            (buttonPositions[selectedBranchId]!.dx * 100).round(),
                                                            (buttonPositions[selectedBranchId]!.dy * 100).round()
                                                          ),
                                                          style: const TextStyle(fontSize: 12),
                                                        ),
                                                        if (branchTitleController.text.isNotEmpty && element.branchTitlePosition != null)
                                                          Text(
                                                            localizations.titlePositionInfo(
                                                              ((element.branchTitlePosition!.dx) * 100).round(),
                                                              ((element.branchTitlePosition!.dy) * 100).round()
                                                            ),
                                                            style: const TextStyle(fontSize: 12, color: Colors.orange),
                                                          ),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),

                    // 底部按钮区域
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        TextButton(
                          onPressed: () {
                            Navigator.of(dialogContext).pop(); // 取消并关闭对话框
                          },
                          child: Text(localizations.cancel),
                        ),
                        ElevatedButton(
                          onPressed: () async {
                            // 显示加载中提示
                            showDialog(
                              context: dialogContext,
                              barrierDismissible: false,
                              builder: (ctx) => AlertDialog(
                                content: Row(
                                  children: [
                                    CircularProgressIndicator(),
                                    SizedBox(width: 20),
                                    Text(localizations.saving)
                                  ],
                                ),
                              ),
                            );
                            
                            // 执行保存
                            final success = await saveSettings();
                            
                            // 关闭加载对话框和设置对话框
                            if (dialogContext.mounted) {
                              Navigator.of(dialogContext).pop(); // 关闭加载对话框
                              Navigator.of(dialogContext).pop(success); // 关闭设置对话框并返回结果
                            }
                            
                            // 返回到原始context显示结果
                            if (success) {
                              if (context.mounted) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(content: Text(localizations.branchSettingsSaved))
                                );
                              }
                            } else {
                              if (context.mounted) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(content: Text(localizations.saveFlowchartFailed))
                                );
                              }
                            }
                          },
                          child: Text(localizations.save),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          }
        );
      },
    );
  }

  // 构建标签页按钮
  Widget _buildTabButton(BuildContext context, String title, bool isSelected, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
        decoration: BoxDecoration(
          color: isSelected ? Colors.blue : Colors.grey.shade200,
          borderRadius: BorderRadius.circular(4),
        ),
        margin: const EdgeInsets.only(right: 8),
        child: Text(
          title,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.black,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  // 生成默认的位置数组，在屏幕的不同位置分布
  List<Offset> _generateDefaultPositions(int count) {
    List<Offset> positions = [];

    if (count <= 3) {
      // 如果按钮数量小于等于3个，水平排列在底部
      double step = 1.0 / (count + 1);
      for (int i = 0; i < count; i++) {
        positions.add(Offset((i + 1) * step, 0.8));
      }
    } else {
      // 如果按钮数量大于3个，在屏幕四周分布
      // 先放四个角落
      positions.add(const Offset(0.2, 0.2)); // 左上
      positions.add(const Offset(0.8, 0.2)); // 右上
      positions.add(const Offset(0.2, 0.8)); // 左下
      positions.add(const Offset(0.8, 0.8)); // 右下

      // 如果还有更多按钮，在中间位置排列
      if (count > 4) {
        double step = 1.0 / (count - 3);
        for (int i = 4; i < count; i++) {
          positions.add(Offset((i - 3) * step, 0.5));
        }
      }
    }

    return positions;
  }

  // 获取目标元素的文本
  String _getDestElementText(String destId) {
    // 查找目标元素并返回其text字段
    try {
      // 在dashboard中查找destId对应的元素
      for (final element in dashboard.elements) {
        if (element.id == destId && element.text.isNotEmpty) {
          return element.text;
        }
      }
    } catch (e) {
      debugPrint('获取目标元素文本时出错: $e');
    }

    // 如果找不到元素或text为空，返回默认文本
    return 'Branch $destId';
  }
}