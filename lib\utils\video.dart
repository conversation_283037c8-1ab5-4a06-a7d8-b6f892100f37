import 'package:flutter/material.dart';
import 'package:media_kit/media_kit.dart'; // Provides [Player], [Media], [Playlist] etc.
import 'package:media_kit_video/media_kit_video.dart'; // Provides [VideoController] & [Video] etc.
import 'package:ve/generated/l10n.dart';
import 'package:ve/main.dart'; // 导入AppStateContainer
import 'dart:async'; // 导入StreamSubscription
import 'package:ve/utils/fullscreen_manager.dart'; // 导入FullscreenManager

class VideoPlayerManager {
  late final Player player;
  late final VideoController controller;
  double playbackRate = 1.0;

  VideoPlayerManager() {
    player = Player(
      configuration: const PlayerConfiguration(
        libass: true,
        logLevel: MPVLogLevel.debug,
        // 添加硬件加速配置
        vo: 'gpu'
      )
    );
    controller = VideoController(player);
  }

  void setPlaybackRate(double rate) {
    playbackRate = rate;
    player.setRate(rate);
  }

  void dispose() {
    player.dispose();
  }

  Widget buildVideoWidget(BuildContext context, {bool showControls = true, bool enableClickPause = false}) {
    // 使用自定义有状态组件来管理视频播放器状态
    return _VideoPlayerWithControls(
      player: player,
      controller: controller,
      showControls: showControls,
      videoManager: this,
      enableClickPause: enableClickPause,
    );
  }

  void showTracks(BuildContext context, GlobalKey key) {
    _showMenu(context, key, _getAudioTracks(context));
  }

  void showSubtitles(BuildContext context, GlobalKey key) {
    _showMenu(context, key, _getSubtitleTracks(context));
  }

  Future<List<PopupMenuItem>> _getAudioTracks(BuildContext context) async {
    final state = player.state;
    final tracks = state.tracks.audio;
    
    List<PopupMenuItem<AudioTrack>> items = tracks.skip(1).map((track) {
      int index = tracks.indexOf(track);
      String title = index == 1 
        ? tracks.length == 2 
          ? S.of(context).noAudioTrack 
          : S.of(context).noAudioTrack 
        : track.title ?? '${S.of(context).audioTrack} ${index - 1}';
      return PopupMenuItem<AudioTrack>(
        value: track,
        child: ListTile(
          title: Text(title),
          subtitle: Text(track.language ?? ''),
        ),
      );
    }).toList();
    return items;
  }

  Future<List<PopupMenuItem>> _getSubtitleTracks(BuildContext context) async {
    final state = player.state;
    final tracks = state.tracks.subtitle;
    List<PopupMenuItem<SubtitleTrack>> items = tracks.skip(1).map((track) {
      int index = tracks.indexOf(track);
      String title = index == 1 
        ? tracks.length == 2 
          ? S.of(context).noSubtitle 
          : S.of(context).closeSubtitle 
        : track.title ?? '${S.of(context).subtitle} ${index - 1}';
      return PopupMenuItem<SubtitleTrack>(
        value: track,
        child: ListTile(
          title: Text(title),
          subtitle: Text(track.language ?? ''),
        ),
      );
    }).toList();
    return items;
  }

  void _showMenu(BuildContext context, GlobalKey key, Future<List<PopupMenuItem>> futureItems) {
    final RenderBox button = key.currentContext!.findRenderObject() as RenderBox;
    final RenderBox overlay = Overlay.of(context).context.findRenderObject() as RenderBox;
    final RelativeRect position = RelativeRect.fromRect(
      Rect.fromPoints(
        button.localToGlobal(button.size.bottomLeft(Offset.zero), ancestor: overlay),
        button.localToGlobal(button.size.topRight(Offset.zero), ancestor: overlay),
      ),
      Offset.zero & overlay.size,
    );

    futureItems.then((items) {
      showMenu<dynamic>(
        context: context,
        position: position,
        items: items,
        elevation: 8.0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.0),
        ),
      ).then((value) {
        if (value is AudioTrack) {
          player.setAudioTrack(value);
        } else if (value is SubtitleTrack) {
          player.setSubtitleTrack(value);
        }
      });
    });
  }

  MaterialDesktopVideoControlsThemeData desktopControls(BuildContext context) {
    return MaterialDesktopVideoControlsThemeData(bottomButtonBar: [
      const MaterialDesktopSkipPreviousButton(),
      const MaterialDesktopPlayOrPauseButton(),
      const MaterialDesktopSkipNextButton(),
      const MaterialDesktopVolumeButton(),
      const MaterialDesktopPositionIndicator(),
      const Spacer(),
      TrackSelectionButton(onPressed: () => showTracks(context, _audioTrackButtonKey), key: _audioTrackButtonKey),
      SubtitleSelectionButton(onPressed: () => showSubtitles(context, _subtitleButtonKey), key: _subtitleButtonKey),
      CustomFullscreenButton(onPressed: () {
        final fullscreenManager = FullscreenManager();
        fullscreenManager.toggleFullscreen();
      }),
    ]);
  }

  final GlobalKey _audioTrackButtonKey = GlobalKey();
  final GlobalKey _subtitleButtonKey = GlobalKey();
}

class TrackSelectionButton extends StatelessWidget {
  final VoidCallback onPressed;
  final Widget? icon;
  final double? iconSize;
  final Color? iconColor;

  const TrackSelectionButton({
    super.key,
    required this.onPressed,
    this.icon,
    this.iconSize,
    this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    return IconButton(
      onPressed: onPressed,
      icon: icon ?? const Icon(Icons.headset),
      iconSize: iconSize ?? _theme(context).buttonBarButtonSize,
      color: iconColor ?? _theme(context).buttonBarButtonColor,
    );
  }
}

class SubtitleSelectionButton extends StatelessWidget {
  final VoidCallback onPressed;
  final Widget? icon;
  final double? iconSize;
  final Color? iconColor;

  const SubtitleSelectionButton({
    super.key,
    required this.onPressed,
    this.icon,
    this.iconSize,
    this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    return IconButton(
      onPressed: onPressed,
      icon: icon ?? const Icon(Icons.closed_caption),
      iconSize: iconSize ?? _theme(context).buttonBarButtonSize,
      color: iconColor ?? _theme(context).buttonBarButtonColor,
    );
  }
}

// 获取MaterialDesktopVideoControls的主题数据
MaterialDesktopVideoControlsThemeData _theme(BuildContext context) {
  return Theme.of(context).extension<MaterialDesktopVideoControlsThemeData>() ?? 
      const MaterialDesktopVideoControlsThemeData();
}

// 自定义视频播放器组件，能够监听播放器状态变化
class _VideoPlayerWithControls extends StatefulWidget {
  final Player player;
  final VideoController controller;
  final bool showControls;
  final VideoPlayerManager videoManager;
  final bool enableClickPause;

  const _VideoPlayerWithControls({
    required this.player,
    required this.controller,
    required this.showControls,
    required this.videoManager,
    this.enableClickPause = false,
  });

  @override
  State<_VideoPlayerWithControls> createState() => _VideoPlayerWithControlsState();
}

class _VideoPlayerWithControlsState extends State<_VideoPlayerWithControls> {
  bool isPaused = true; // 初始状态设为暂停
  bool hasStartedPlaying = false; // 跟踪视频是否已经加载并开始播放过
  late final StreamSubscription _playingSubscription;
  late final FullscreenManager _fullscreenManager;

  @override
  void initState() {
    super.initState();
    // 初始化时获取当前播放状态
    isPaused = !widget.player.state.playing;
    
    // 初始化全屏管理器
    _fullscreenManager = FullscreenManager();
    
    // 监听播放状态变化
    _playingSubscription = widget.player.stream.playing.listen((playing) {
      if (mounted) {
        setState(() {
          // 如果视频开始播放，标记已经播放过
          if (playing) {
            hasStartedPlaying = true;
          }
          isPaused = !playing;
        });
      }
    });
  }

  @override
  void dispose() {
    // 取消监听
    _playingSubscription.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 获取自定义控制器主题
    final controlsTheme = widget.videoManager.desktopControls(context);
    
    return Container(
      color: Colors.black,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // 视频播放器
          GestureDetector(
            onTap: () {
              if (widget.showControls) {
                // 如果显示控制器，则不处理点击事件（让控制器处理）
                return;
              }
              
              // 只有当enableClickPause为true时才响应点击暂停/播放
              if (widget.enableClickPause) {
                // 切换播放/暂停状态
                if (widget.player.state.playing) {
                  // 暂停视频
                  widget.player.pause();
                } else {
                  // 播放视频
                  widget.player.play();
                }
              }
            },
            child: Center(
              child: MaterialDesktopVideoControlsTheme(
                normal: controlsTheme,
                fullscreen: controlsTheme,
                child: Video(
                  key: ValueKey(widget.showControls),
                  controller: widget.controller,
                  controls: widget.showControls ? MaterialDesktopVideoControls : null,
                ),
              ),
            ),
          ),
          
          // 中央播放按钮，仅在视频已经播放过并且处于暂停状态且不显示控制器时显示
          // 同时需要enableClickPause为true才显示
          if (isPaused && !widget.showControls && hasStartedPlaying && widget.enableClickPause)
            GestureDetector(
              onTap: () {
                // 点击播放按钮时播放视频
                widget.player.play();
              },
              child: Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.5),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.play_arrow,
                  color: Colors.white,
                  size: 50,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

// 自定义全屏按钮
class CustomFullscreenButton extends StatefulWidget {
  final VoidCallback onPressed;
  final Widget? icon;
  final double? iconSize;
  final Color? iconColor;

  const CustomFullscreenButton({
    super.key,
    required this.onPressed,
    this.icon,
    this.iconSize,
    this.iconColor,
  });

  @override
  State<CustomFullscreenButton> createState() => _CustomFullscreenButtonState();
}

class _CustomFullscreenButtonState extends State<CustomFullscreenButton> {
  late final FullscreenManager _fullscreenManager;
  
  @override
  void initState() {
    super.initState();
    _fullscreenManager = FullscreenManager();
    // 监听全屏状态变化
    _fullscreenManager.addListener(() {
      if (mounted) {
        setState(() {});
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return IconButton(
      onPressed: widget.onPressed,
      icon: widget.icon ?? 
          Icon(_fullscreenManager.isFullscreen ? Icons.fullscreen_exit : Icons.fullscreen),
      iconSize: widget.iconSize ?? _theme(context).buttonBarButtonSize,
      color: widget.iconColor ?? _theme(context).buttonBarButtonColor,
    );
  }
}
