import 'dart:io';
import 'package:path/path.dart' as path;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:steamworks/steamworks.dart';

/// 全局Steam信息管理类
/// 用于存储Steam用户ID和程序ID，避免重复获取
class SteamGlobals {
  static SteamGlobals? _instance;
  
  /// 单例实例
  static SteamGlobals get instance {
    _instance ??= SteamGlobals._();
    return _instance!;
  }
  
  /// Steam账户ID (32位)
  int? _accountId;
  
  /// 是否已初始化
  bool _initialized = false;
  
  /// 是否使用备用账户ID
  bool _usingFallbackId = false;
  
  /// 本地存储的键名
  static const String _steamIdKey = 'steam_account_id';
  
  /// 私有构造函数
  SteamGlobals._();
  
  
  /// 获取Steam账户ID (32位)
  int get accountId {
    if (!_initialized) {
      _initSteamInfo();
    }
    
    // 如果账户ID为0，尝试从本地存储加载
    if ((_accountId == null || _accountId == 0) && !_usingFallbackId) {
      _loadSavedAccountId();
    }
    
    return _accountId ?? 0;
  }
  
  /// 获取Steam应用ID
  int get appId {
    return 3464020; // 默认应用ID
  }
  
  /// 是否使用了备用账户ID
  bool get isUsingFallbackId => _usingFallbackId;
  
  /// 初始化Steam信息
  void _initSteamInfo() {
    try {
      // 检查SteamClient是否已初始化
      if (SteamClient.instance == null) {
        print('SteamClient实例未初始化，使用默认值');
        _initialized = true;
        _loadSavedAccountId(); // 尝试从本地存储加载账户ID
        return;
      }
      
      // 获取Steam用户ID
      final steamId64 = SteamClient.instance.steamUser.getSteamId();
      _accountId = steamId64 & 0xFFFFFFFF;
      
      print('初始化Steam全局信息: accountId=$_accountId, steamId64=$steamId64');
      _initialized = true;
      
      // 保存有效的账户ID到本地存储
      if (_accountId != null && _accountId! > 0) {
        _saveAccountId(_accountId!);
      }
    } catch (e) {
      print('初始化Steam全局信息失败: $e，尝试备用方式获取');

      // 尝试从本地存储加载账户ID
      _loadSavedAccountId();
    }
  }
  
  /// 从本地存储加载保存的账户ID
  Future<void> _loadSavedAccountId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedId = prefs.getInt(_steamIdKey);
      
      if (savedId != null && savedId > 0) {
        _accountId = savedId;
        _usingFallbackId = true;
        print('从本地存储加载账户ID成功: accountId=$_accountId');
      } else {
        // 如果本地存储中没有有效的账户ID，尝试从Steam路径中提取
        _extractAccountIdFromSteamPath();
      }
    } catch (e) {
      print('从本地存储加载账户ID失败: $e');
      // 尝试从Steam路径中提取账户ID
      _extractAccountIdFromSteamPath();
    }
  }
  
  /// 保存账户ID到本地存储
  Future<void> _saveAccountId(int accountId) async {
    try {
      if (accountId > 0) {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setInt(_steamIdKey, accountId);
        print('成功保存账户ID到本地存储: accountId=$accountId');
      }
    } catch (e) {
      print('保存账户ID到本地存储失败: $e');
    }
  }
  
  /// 从Steam路径中提取账户ID
  void _extractAccountIdFromSteamPath() {
    try {
      if (Platform.isWindows) {
        // 尝试从环境变量获取Steam路径
        final steamPath = Platform.environment['SteamPath'] ?? 'C:\\Program Files (x86)\\Steam';
        final userdataDir = Directory('$steamPath\\userdata');
        
        if (userdataDir.existsSync()) {
          // 获取userdata目录下的所有子目录（每个子目录名应该是一个账户ID）
          final subdirs = userdataDir.listSync()
              .where((entity) => entity is Directory)
              .map((entity) => path.basename(entity.path))
              .toList();
          
          if (subdirs.isNotEmpty) {
            // 尝试将第一个子目录名转换为整数作为账户ID
            try {
              final extractedId = int.parse(subdirs.first);
              if (extractedId > 0) {
                _accountId = extractedId;
                _usingFallbackId = true;
                print('从Steam路径提取账户ID成功: accountId=$_accountId');
                
                // 保存提取的账户ID到本地存储
                _saveAccountId(extractedId);
              }
            } catch (parseError) {
              print('解析从Steam路径提取的账户ID失败: $parseError');
            }
          }
        }
      }
    } catch (e) {
      print('从Steam路径提取账户ID失败: $e');
    }
  }
  
  /// 初始化全局Steam信息
  static void init() {
    instance._initSteamInfo();
  }
  
  /// 手动设置账户ID（用于测试或特殊情况）
  Future<void> setAccountId(int accountId) async {
    if (accountId > 0) {
      _accountId = accountId;
      _usingFallbackId = true;
      print('手动设置账户ID: accountId=$accountId');
      
      // 保存到本地存储
      await _saveAccountId(accountId);
    }
  }
}