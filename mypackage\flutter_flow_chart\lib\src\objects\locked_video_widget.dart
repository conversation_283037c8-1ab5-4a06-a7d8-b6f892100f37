import 'package:flutter/material.dart';
import 'package:flutter_flow_chart/src/elements/flow_element.dart';
import 'dart:io';

/// 用于显示锁定状态的视频节点小部件
class LockedVideoWidget extends StatelessWidget {
  const LockedVideoWidget({
    required this.element,
    required this.projectPath,
    super.key,
  });

  final FlowElement element;
  final String projectPath;

  @override
  Widget build(BuildContext context) {
    String thumbnailPath = '${projectPath}/${element.id}.jpg';
    
    // 固定宽高比16:9
    final width = element.size.width;
    final height = width * 9 / 16;
    
    return SizedBox(
      width: width,
      height: height,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // 背景容器
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: element.borderColor,
                width: element.borderThickness,
              ),
              color: Colors.black.withOpacity(0.7), // 暗化背景
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.file(
                File(thumbnailPath),
                width: width,
                height: height,
                fit: BoxFit.cover,
                color: Colors.black.withOpacity(0.5), // 图片暗化
                colorBlendMode: BlendMode.darken,
                errorBuilder: (context, error, stackTrace) {
                  return const Center(child: Text('暂无缩略图'));
                },
              ),
            ),
          ),
          // 锁定图标
          Icon(
            Icons.lock,
            color: Colors.white,
            size: 40,
          ),
        ],
      ),
    );
  }
}