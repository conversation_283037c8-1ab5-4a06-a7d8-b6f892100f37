import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';

class MaterialLocalizationGomDelegate extends LocalizationsDelegate<MaterialLocalizations> {
  const MaterialLocalizationGomDelegate();

  @override
  bool isSupported(Locale locale) => locale.languageCode == 'gom';

  @override
  Future<MaterialLocalizations> load(Locale locale) async {
    return DefaultMaterialLocalizations();
  }

  @override
  bool shouldReload(MaterialLocalizationGomDelegate old) => false;
}

class CupertinoLocalizationGomDelegate extends LocalizationsDelegate<CupertinoLocalizations> {
  const CupertinoLocalizationGomDelegate();

  @override
  bool isSupported(Locale locale) => locale.languageCode == 'gom';

  @override
  Future<CupertinoLocalizations> load(Locale locale) async {
    return DefaultCupertinoLocalizations();
  }

  @override
  bool shouldReload(CupertinoLocalizationGomDelegate old) => false;
}