import 'package:flutter/material.dart';
import '../utils/value_change_helper.dart';

/// 进阶示例：如何在实际应用中使用ValueChangeHelper
class AdvancedValueChangeExample extends StatefulWidget {
  const AdvancedValueChangeExample({Key? key}) : super(key: key);

  @override
  State<AdvancedValueChangeExample> createState() => _AdvancedValueChangeExampleState();
}

class _AdvancedValueChangeExampleState extends State<AdvancedValueChangeExample> {
  int _health = 100;
  int _score = 0;
  int _coins = 500;
  
  void _damagePlayer() {
    final oldHealth = _health;
    setState(() {
      _health = (_health - 15).clamp(0, 100);
      
      // 使用辅助类显示生命值变化提示
      ValueChangeHelper.compareAndShow(
        context,
        oldValue: oldHealth,
        newValue: _health,
        valueName: '生命值',
        position: Alignment.topLeft, // 左上角显示生命值变化
      );
    });
  }
  
  void _healPlayer() {
    final oldHealth = _health;
    setState(() {
      _health = (_health + 10).clamp(0, 100);
      
      // 使用辅助类显示生命值变化提示
      ValueChangeHelper.compareAndShow(
        context,
        oldValue: oldHealth,
        newValue: _health,
        valueName: '生命值',
        position: Alignment.topLeft, // 左上角显示生命值变化
      );
    });
  }
  
  void _addScore() {
    final oldScore = _score;
    setState(() {
      _score += 50;
      
      // 使用辅助类显示分数变化提示
      ValueChangeHelper.compareAndShow(
        context,
        oldValue: oldScore,
        newValue: _score,
        valueName: '分数',
        position: Alignment.topRight, // 右上角显示分数变化
      );
    });
  }
  
  void _spendCoins() {
    final oldCoins = _coins;
    setState(() {
      _coins = (_coins - 100).clamp(0, 9999);
      
      // 使用辅助类显示金币变化提示
      ValueChangeHelper.compareAndShow(
        context,
        oldValue: oldCoins,
        newValue: _coins,
        valueName: '金币',
        position: Alignment.bottomRight, // 右下角显示金币变化
      );
    });
  }
  
  void _earnCoins() {
    final oldCoins = _coins;
    setState(() {
      _coins += 200;
      
      // 使用辅助类显示金币变化提示
      ValueChangeHelper.compareAndShow(
        context,
        oldValue: oldCoins,
        newValue: _coins,
        valueName: '金币',
        position: Alignment.bottomRight, // 右下角显示金币变化
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('数值变化提示进阶示例'),
      ),
      body: Column(
        children: [
          // 顶部状态栏
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.black87,
            child: Row(
              children: [
                // 生命值
                Expanded(
                  child: Row(
                    children: [
                      const Icon(Icons.favorite, color: Colors.red),
                      const SizedBox(width: 8),
                      Text(
                        '$_health',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
                
                // 分数
                Expanded(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      const Icon(Icons.star, color: Colors.amber),
                      const SizedBox(width: 8),
                      Text(
                        '$_score',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          // 主要内容
          Expanded(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text(
                    '游戏场景示例',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 40),
                  
                  // 生命值操作
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ElevatedButton(
                        onPressed: _damagePlayer,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                        ),
                        child: const Text('受到伤害 (-15)'),
                      ),
                      const SizedBox(width: 20),
                      ElevatedButton(
                        onPressed: _healPlayer,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                        ),
                        child: const Text('治疗生命 (+10)'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  
                  // 分数操作
                  ElevatedButton(
                    onPressed: _addScore,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.amber,
                    ),
                    child: const Text('增加分数 (+50)'),
                  ),
                  const SizedBox(height: 20),
                  
                  // 金币操作
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ElevatedButton(
                        onPressed: _spendCoins,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.deepPurple,
                        ),
                        child: const Text('消费金币 (-100)'),
                      ),
                      const SizedBox(width: 20),
                      ElevatedButton(
                        onPressed: _earnCoins,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                        ),
                        child: const Text('获得金币 (+200)'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          
          // 底部金币显示
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.black87,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                const Icon(Icons.monetization_on, color: Colors.amber),
                const SizedBox(width: 8),
                Text(
                  '$_coins',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
} 