// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a fr locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'fr';

  static String m0(nodeId) =>
      "Mise à jour de l’archive actuelle du nœud : ${nodeId}";

  static String m1(index) => "Branche ${index} :";

  static String m2(branchText) => "Branche : ${branchText}";

  static String m3(horizontalPercent, verticalPercent) =>
      "Position horizontale : ${horizontalPercent} %, Position verticale : ${verticalPercent} %";

  static String m4(count) =>
      "Nombre de nœuds avec des vidéos qui ne sont pas connectées : ${count}";

  static String m5(name) => "Modifier la valeur globale : ${name}";

  static String m6(error) =>
      "Erreur de chargement de l’organigramme : ${error}";

  static String m7(path) => "Chemin d’accès complet à la vidéo : ${path}";

  static String m8(error) => "Erreur de chargement de l’archive : ${error}";

  static String m9(branchText) =>
      "Les paramètres de modification de condition et de valeur pour la branche « ${branchText} » sont enregistrés";

  static String m10(nodeId) => "Nœud marqué comme consulté : ${nodeId}";

  static String m11(error) =>
      "Erreur lors de l’ouverture du projet de jeu : ${error}";

  static String m12(path) => "Projet chargé : ${path}";

  static String m13(path) => "Élément introuvable : ${path}";

  static String m14(duration) => "QTE\n{durée} secondes";

  static String m15(duration) =>
      "Temps d’affichage du bouton : ${duration} secondes";

  static String m16(horizontalPercent, verticalPercent) =>
      "Position du bouton QTE : ${horizontalPercent} %, ${verticalPercent} %";

  static String m17(time) => "Temps restant : ${time} secondes";

  static String m18(branchText) =>
      "Définissez la condition et le changement numérique de la branche « ${branchText} ».";

  static String m19(count) =>
      "Le nombre de nœuds vidéo après le point de départ doit être de 1, current : ${count}";

  static String m20(horizontalPercent, verticalPercent) =>
      "Emplacement : HorizontalPercent} %, Vertical${verticalPercent} %";

  static String m21(error) => "Erreur de mise à jour de l’atelier : ${error}";

  static String m22(path) => "Le fichier vidéo n’existe pas : ${path}";

  static String m23(error) => "Erreur de lecture vidéo : ${error}";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "AncientChinese": MessageLookupByLibrary.simpleMessage(
      "Style chinois ancien",
    ),
    "Anime": MessageLookupByLibrary.simpleMessage("Style anime"),
    "Everyone": MessageLookupByLibrary.simpleMessage("Tous"),
    "Landscape": MessageLookupByLibrary.simpleMessage("Rectangle transversal"),
    "NSFW": MessageLookupByLibrary.simpleMessage("Lieux non publics"),
    "Other": MessageLookupByLibrary.simpleMessage("Autres styles"),
    "Pixel": MessageLookupByLibrary.simpleMessage("Style de pixel"),
    "Portrait": MessageLookupByLibrary.simpleMessage("Rectangle longitudinal"),
    "Realistic": MessageLookupByLibrary.simpleMessage("Style réaliste"),
    "SearchWorkshop": MessageLookupByLibrary.simpleMessage(
      "Cherchez-le dans l’atelier",
    ),
    "Square": MessageLookupByLibrary.simpleMessage("carré"),
    "Subscribe": MessageLookupByLibrary.simpleMessage("s’inscrire"),
    "SubscribeAndDownload": MessageLookupByLibrary.simpleMessage(
      "Abonnez-vous et téléchargez",
    ),
    "Subscribed": MessageLookupByLibrary.simpleMessage("Souscrit"),
    "Unsubscribed": MessageLookupByLibrary.simpleMessage("Non abonné"),
    "about": MessageLookupByLibrary.simpleMessage("concernant"),
    "achievementDisplay": MessageLookupByLibrary.simpleMessage(
      "Vitrine des résultats",
    ),
    "achievements": MessageLookupByLibrary.simpleMessage("accomplissement"),
    "activity": MessageLookupByLibrary.simpleMessage("dynamique"),
    "add": MessageLookupByLibrary.simpleMessage("plus"),
    "addDiamond": MessageLookupByLibrary.simpleMessage("Ajouter un diamant"),
    "addGlobalValue": MessageLookupByLibrary.simpleMessage(
      "Ajouter une valeur globale",
    ),
    "addHexagon": MessageLookupByLibrary.simpleMessage("Ajouter un hexagone"),
    "addImage": MessageLookupByLibrary.simpleMessage("Ajouter une image"),
    "addOval": MessageLookupByLibrary.simpleMessage("Ajouter une ellipse"),
    "addParallelogram": MessageLookupByLibrary.simpleMessage(
      "Ajouter un parallélogramme",
    ),
    "addRectangle": MessageLookupByLibrary.simpleMessage(
      "Ajouter un rectangle",
    ),
    "addResizableRectangle": MessageLookupByLibrary.simpleMessage(
      "Ajouter un rectangle variable",
    ),
    "addStorage": MessageLookupByLibrary.simpleMessage(
      "Ajouter de l’espace de stockage",
    ),
    "addToFlowchart": MessageLookupByLibrary.simpleMessage(
      "Ajouter à l’organigramme",
    ),
    "addVariable": MessageLookupByLibrary.simpleMessage(
      "Ajouter des variables",
    ),
    "addVideo": MessageLookupByLibrary.simpleMessage("Ajouter une vidéo"),
    "adultAgreementContent": MessageLookupByLibrary.simpleMessage(
      "Le Steam Workshop contient du contenu co-créé par des joueurs du monde entier, qui peut impliquer du contenu qui n’est pas approprié pour être visionné dans des lieux publics.",
    ),
    "adultAgreementTitle": MessageLookupByLibrary.simpleMessage(
      "Veuillez vous assurer que vous avez au moins 18 ans",
    ),
    "ageRating": MessageLookupByLibrary.simpleMessage("Tranches d’âge"),
    "allSettings": MessageLookupByLibrary.simpleMessage("Tous les paramètres"),
    "alreadyFavorited": MessageLookupByLibrary.simpleMessage(
      "Déjà mis en signet",
    ),
    "alreadyLiked": MessageLookupByLibrary.simpleMessage("Il a déjà été aimé"),
    "alreadySubscribed": MessageLookupByLibrary.simpleMessage("Souscrit"),
    "apply": MessageLookupByLibrary.simpleMessage("appliquer"),
    "archive": MessageLookupByLibrary.simpleMessage("archiver"),
    "archiveFileNotFound": MessageLookupByLibrary.simpleMessage(
      "Le fichier d’archive n’existe pas",
    ),
    "archiveUpdatedForNode": m0,
    "audioSettings": MessageLookupByLibrary.simpleMessage("Paramètres audio"),
    "audioTrack": MessageLookupByLibrary.simpleMessage("Piste"),
    "autoFullScreenVideo": MessageLookupByLibrary.simpleMessage(
      "Plein écran automatique lors de la lecture d’une vidéo",
    ),
    "autoSaveDescription": MessageLookupByLibrary.simpleMessage(
      "Sauvegardez automatiquement votre progression dans le jeu",
    ),
    "autoSaveGame": MessageLookupByLibrary.simpleMessage(
      "Sauvegarde automatique des jeux",
    ),
    "autoSaveInterval": MessageLookupByLibrary.simpleMessage(
      "Intervalles d’enregistrement automatique",
    ),
    "autoSaveProgress": MessageLookupByLibrary.simpleMessage(
      "Enregistrez automatiquement votre progression",
    ),
    "autoSelect": MessageLookupByLibrary.simpleMessage("Sélection automatique"),
    "back": MessageLookupByLibrary.simpleMessage("rendre"),
    "backgroundColor": MessageLookupByLibrary.simpleMessage(
      "Couleur d’arrière-plan",
    ),
    "booleanType": MessageLookupByLibrary.simpleMessage("Booléen"),
    "borderColor": MessageLookupByLibrary.simpleMessage(
      "Couleur de la bordure",
    ),
    "branchIndexLabel": m1,
    "branchSettingsSaved": MessageLookupByLibrary.simpleMessage(
      "Les paramètres de branche et les organigrammes ont été enregistrés",
    ),
    "branchWithText": m2,
    "buttonDisplayTime": MessageLookupByLibrary.simpleMessage(
      "bouton pour afficher l’heure",
    ),
    "buttonDisplayTimeDescription": MessageLookupByLibrary.simpleMessage(
      "Combien de secondes avant la fin de la vidéo montre le bouton de branche",
    ),
    "buttonDisplayTimeNote": MessageLookupByLibrary.simpleMessage(
      "Lorsque ce paramètre est réglé sur 0, les branches s’affichent à la fin de la lecture vidéo",
    ),
    "buttonOpacity": MessageLookupByLibrary.simpleMessage(
      "Transparence des boutons",
    ),
    "buttonPositionInfo": m3,
    "buttonText": MessageLookupByLibrary.simpleMessage("Texte du bouton"),
    "buttonTextOnly": MessageLookupByLibrary.simpleMessage(
      "Afficher uniquement le texte (pas d’arrière-plan du bouton)",
    ),
    "cancel": MessageLookupByLibrary.simpleMessage("Annuler"),
    "captureCurrentFrame": MessageLookupByLibrary.simpleMessage(
      "Capture l’image actuelle",
    ),
    "challenge": MessageLookupByLibrary.simpleMessage("défi"),
    "challengeName": MessageLookupByLibrary.simpleMessage("Le nom du défi"),
    "clickToSubscribeAndDownload": MessageLookupByLibrary.simpleMessage(
      "Cliquez sur S’abonner pour lancer le téléchargement",
    ),
    "close": MessageLookupByLibrary.simpleMessage("Arrêter"),
    "closeSubtitle": MessageLookupByLibrary.simpleMessage(
      "Désactiver les sous-titres",
    ),
    "committingChanges": MessageLookupByLibrary.simpleMessage(
      "Validez vos modifications",
    ),
    "completed": MessageLookupByLibrary.simpleMessage(
      "Que ce soit fait ou pas",
    ),
    "confirm": MessageLookupByLibrary.simpleMessage("Confirmer"),
    "confirmExitApp": MessageLookupByLibrary.simpleMessage(
      "Êtes-vous sûr de vouloir quitter l’application ?",
    ),
    "confirmUnsubscribe": MessageLookupByLibrary.simpleMessage(
      "Êtes-vous sûr de vouloir résilier votre abonnement ?",
    ),
    "confirmUpdateWorkshopItem": MessageLookupByLibrary.simpleMessage(
      "Objets de l’atelier mis à jour",
    ),
    "confirmUpdateWorkshopItemDescription": MessageLookupByLibrary.simpleMessage(
      "Vous voudrez mettre à jour un objet de l’Atelier existant. Cela remplacera la version actuelle avec vos modifications.",
    ),
    "congratsEarnedCoins": MessageLookupByLibrary.simpleMessage(
      "Félicitations! Obtenez une récompense de 10 pièces d’or !",
    ),
    "coverLoadFailed": MessageLookupByLibrary.simpleMessage(
      "Echec du chargement de la page couverture",
    ),
    "createChallenge": MessageLookupByLibrary.simpleMessage("Créez un défi"),
    "createNewGame": MessageLookupByLibrary.simpleMessage("Créer un jeu"),
    "createTime": MessageLookupByLibrary.simpleMessage("Heure de création"),
    "credits": MessageLookupByLibrary.simpleMessage("Liste des crédits"),
    "creditsAnimationDesigner": MessageLookupByLibrary.simpleMessage(
      "Concepteur d’animation",
    ),
    "creditsArtDesigner": MessageLookupByLibrary.simpleMessage(
      "Designer artistique",
    ),
    "creditsCodeWriter": MessageLookupByLibrary.simpleMessage(
      "Rédaction de code",
    ),
    "creditsCopywriter": MessageLookupByLibrary.simpleMessage("Rédaction"),
    "creditsCreativeSource": MessageLookupByLibrary.simpleMessage(
      "Sources d’idées",
    ),
    "creditsCreativeTitle": MessageLookupByLibrary.simpleMessage(
      "Créativité et planification",
    ),
    "creditsGameProducer": MessageLookupByLibrary.simpleMessage(
      "Producteur de jeux",
    ),
    "creditsGameplayPlanner": MessageLookupByLibrary.simpleMessage(
      "Planification du jeu",
    ),
    "creditsMarketingTitle": MessageLookupByLibrary.simpleMessage(
      "Publicité du marché",
    ),
    "creditsProgrammingTitle": MessageLookupByLibrary.simpleMessage(
      "Élaboration de programmes",
    ),
    "creditsSoftwareArchitect": MessageLookupByLibrary.simpleMessage(
      "Architecture logicielle",
    ),
    "creditsSoftwarePlanner": MessageLookupByLibrary.simpleMessage(
      "Planification logicielle",
    ),
    "creditsSpecialThanks": MessageLookupByLibrary.simpleMessage(
      "Un grand merci à vous",
    ),
    "creditsSubtitle": MessageLookupByLibrary.simpleMessage(
      "Voir les équipes et les contributeurs impliqués dans le développement logiciel",
    ),
    "creditsThanksTitle": MessageLookupByLibrary.simpleMessage(
      "Merci d’avoir joué à mon jeu !",
    ),
    "creditsVideoProducer": MessageLookupByLibrary.simpleMessage(
      "Production vidéo",
    ),
    "creditsVisualTitle": MessageLookupByLibrary.simpleMessage(
      "Conception visuelle",
    ),
    "currentPosition": MessageLookupByLibrary.simpleMessage("Vous êtes ici"),
    "curved": MessageLookupByLibrary.simpleMessage("courbe"),
    "customButtonPosition": MessageLookupByLibrary.simpleMessage(
      "Personnaliser la position du bouton",
    ),
    "delete": MessageLookupByLibrary.simpleMessage("Supprimer"),
    "description": MessageLookupByLibrary.simpleMessage("description"),
    "developer": MessageLookupByLibrary.simpleMessage("Développeurs"),
    "diamondNode": MessageLookupByLibrary.simpleMessage("Nœuds en diamant"),
    "disconnectedVideoCount": m4,
    "diskSpaceInsufficient": MessageLookupByLibrary.simpleMessage(
      "Espace disque insuffisant, basé sur le mécanisme de téléchargement Steam, vous devez réserver suffisamment d’espace sur le disque où se trouve le logiciel. Libérez l’espace disque et réessayez de télécharger.",
    ),
    "divide": MessageLookupByLibrary.simpleMessage("Divisant"),
    "download": MessageLookupByLibrary.simpleMessage("Télécharger"),
    "downloadFailed": MessageLookupByLibrary.simpleMessage(
      "Le téléchargement a échoué",
    ),
    "downloadSuccess": MessageLookupByLibrary.simpleMessage(
      "Le téléchargement a été un succès",
    ),
    "downloadWorkshop": MessageLookupByLibrary.simpleMessage(
      "Télécharger l’atelier",
    ),
    "downloading": MessageLookupByLibrary.simpleMessage("Téléchargement"),
    "downloadingPleaseWait": MessageLookupByLibrary.simpleMessage(
      "Téléchargement, s’il vous plaît plus tard",
    ),
    "downloads": MessageLookupByLibrary.simpleMessage("Télécharger"),
    "duration": MessageLookupByLibrary.simpleMessage("Durée totale"),
    "editGlobalValue": MessageLookupByLibrary.simpleMessage(
      "Modifier les valeurs globales",
    ),
    "editGlobalValueTitle": m5,
    "elementId": MessageLookupByLibrary.simpleMessage("ID"),
    "elementParams": MessageLookupByLibrary.simpleMessage(
      "Paramètres d’élément",
    ),
    "elementText": MessageLookupByLibrary.simpleMessage("SMS"),
    "elementType": MessageLookupByLibrary.simpleMessage("type"),
    "elevation": MessageLookupByLibrary.simpleMessage("hauteur"),
    "enableFlowchartCheck": MessageLookupByLibrary.simpleMessage(
      "Activer la détection d’organigramme",
    ),
    "enableVideoClickPause": MessageLookupByLibrary.simpleMessage(
      "Appuyez pour mettre la vidéo en pause pendant sa lecture",
    ),
    "enableVideoClickPauseDesc": MessageLookupByLibrary.simpleMessage(
      "Une fois activé, appuyez sur la zone vidéo pour basculer l’état de lecture/pause",
    ),
    "endTime": MessageLookupByLibrary.simpleMessage("Prendre fin\nHeure"),
    "endTimeLabel": MessageLookupByLibrary.simpleMessage("Heure de fin"),
    "endTimeNotExceedTotal": MessageLookupByLibrary.simpleMessage(
      "L’heure de fin ne peut pas dépasser la durée totale de la vidéo",
    ),
    "enterProjectName": MessageLookupByLibrary.simpleMessage(
      "Entrez un nom pour le projet",
    ),
    "equalTo": MessageLookupByLibrary.simpleMessage("quantité"),
    "exit": MessageLookupByLibrary.simpleMessage("Quitter le jeu"),
    "exitFullscreen": MessageLookupByLibrary.simpleMessage(
      "Quitter le mode plein écran",
    ),
    "failedToGetWorkshopItems": MessageLookupByLibrary.simpleMessage(
      "Impossible d’obtenir des objets de l’atelier",
    ),
    "favoriteFailed": MessageLookupByLibrary.simpleMessage("Échec du signet"),
    "favoriteSuccess": MessageLookupByLibrary.simpleMessage("Collecte réussie"),
    "fileNotFound": MessageLookupByLibrary.simpleMessage(
      "Le fichier n’existe pas",
    ),
    "fileNotFoundTitle": MessageLookupByLibrary.simpleMessage(
      "Le fichier n’existe pas",
    ),
    "fileNotFoundWithPath": MessageLookupByLibrary.simpleMessage(
      "Le fichier n’existe pas : \$jsonFilePath",
    ),
    "filePreparingPleaseRetry": MessageLookupByLibrary.simpleMessage(
      "Le fichier est en cours de préparation, veuillez réessayer plus tard",
    ),
    "flowChart": MessageLookupByLibrary.simpleMessage("organigramme"),
    "flowchart": MessageLookupByLibrary.simpleMessage("Examiner le processus"),
    "flowchartCheckDescription": MessageLookupByLibrary.simpleMessage(
      "Détecte les nœuds non connectés et les connexions d’origine",
    ),
    "flowchartFileNotFound": MessageLookupByLibrary.simpleMessage(
      "FLOWCHART.json fichier n’a pas pu être retrouvé",
    ),
    "flowchartLoadError": m6,
    "flowchartMissingStart": MessageLookupByLibrary.simpleMessage(
      "Il manque un élément de point de départ à l’organigramme",
    ),
    "friends": MessageLookupByLibrary.simpleMessage("Amis"),
    "friendsonly": MessageLookupByLibrary.simpleMessage("Visible par les amis"),
    "fullVideoPath": m7,
    "fullscreen": MessageLookupByLibrary.simpleMessage("Mode plein écran"),
    "gallery": MessageLookupByLibrary.simpleMessage("Galerie"),
    "gameTitle": MessageLookupByLibrary.simpleMessage(
      "Moteur de jeu vidéo interactif",
    ),
    "gameWindow": MessageLookupByLibrary.simpleMessage("Fenêtre de jeu"),
    "games": MessageLookupByLibrary.simpleMessage("Jeu"),
    "generalBranchSettingsSaved": MessageLookupByLibrary.simpleMessage(
      "Les paramètres de branche et les organigrammes ont été enregistrés",
    ),
    "generalSettings": MessageLookupByLibrary.simpleMessage(
      "Paramètres généraux",
    ),
    "globalValueName": MessageLookupByLibrary.simpleMessage(
      "Nom de la valeur numérique",
    ),
    "globalValueNameHint": MessageLookupByLibrary.simpleMessage(
      "Par exemple : l’or, la santé, etc",
    ),
    "globalValues": MessageLookupByLibrary.simpleMessage("Valeurs mondiales"),
    "greaterThan": MessageLookupByLibrary.simpleMessage("Plus grand que"),
    "greaterThanOrEqual": MessageLookupByLibrary.simpleMessage(
      "Supérieur ou égal à",
    ),
    "hexagonNode": MessageLookupByLibrary.simpleMessage("Nœuds hexagonaux"),
    "hideController": MessageLookupByLibrary.simpleMessage(
      "Masquer la barre des commandes",
    ),
    "home": MessageLookupByLibrary.simpleMessage("page d’accueil"),
    "hour": MessageLookupByLibrary.simpleMessage("Heure"),
    "ilpDesc": MessageLookupByLibrary.simpleMessage("description"),
    "ilpEditor": MessageLookupByLibrary.simpleMessage("Éditeur de jeux"),
    "imageNode": MessageLookupByLibrary.simpleMessage("Nœuds d’image"),
    "importImage": MessageLookupByLibrary.simpleMessage("Importer des images"),
    "initialValue": MessageLookupByLibrary.simpleMessage("Valeur initiale"),
    "inputDescription": MessageLookupByLibrary.simpleMessage(
      "Entrez une description",
    ),
    "interfaceLanguage": MessageLookupByLibrary.simpleMessage(
      "Langue de l’interface",
    ),
    "invalidEndTime": MessageLookupByLibrary.simpleMessage(
      "L’heure de fin n’est pas valide",
    ),
    "invalidFileType": MessageLookupByLibrary.simpleMessage(
      "Type de fichier non valide",
    ),
    "invalidProjectPath": MessageLookupByLibrary.simpleMessage(
      "Le chemin d’accès au projet n’est pas valide",
    ),
    "invalidStartTime": MessageLookupByLibrary.simpleMessage(
      "L’heure de début n’est pas valide",
    ),
    "invalidTimeFormat": MessageLookupByLibrary.simpleMessage(
      "Le format de l’heure n’est pas valide",
    ),
    "itemNotSubscribed": MessageLookupByLibrary.simpleMessage(
      "Les articles qui ne sont pas abonnés seront automatiquement téléchargés après l’abonnement",
    ),
    "joinDiscord": MessageLookupByLibrary.simpleMessage(
      "Rejoignez le Discord pour des discussions sur le gameplay",
    ),
    "language": MessageLookupByLibrary.simpleMessage("Paramètres de langue"),
    "languageSettings": MessageLookupByLibrary.simpleMessage(
      "Paramètres de langue",
    ),
    "lastUpdate": MessageLookupByLibrary.simpleMessage("Dernière mise à jour"),
    "lessThan": MessageLookupByLibrary.simpleMessage("Moins de"),
    "lessThanOrEqual": MessageLookupByLibrary.simpleMessage(
      "Inférieur ou égal à",
    ),
    "likeFailed": MessageLookupByLibrary.simpleMessage("N’a pas aimé"),
    "likeSuccess": MessageLookupByLibrary.simpleMessage("Comme le succès"),
    "loadArchive": MessageLookupByLibrary.simpleMessage("Lire l’archive"),
    "loadArchiveError": m8,
    "loadFlowchart": MessageLookupByLibrary.simpleMessage(
      "Lire l’organigramme",
    ),
    "loadingFailed": MessageLookupByLibrary.simpleMessage(
      "Echec du chargement",
    ),
    "loadingFailedRetry": MessageLookupByLibrary.simpleMessage(
      "Si le chargement échoue, cliquez sur Réessayer",
    ),
    "mainWindowFullscreen": MessageLookupByLibrary.simpleMessage(
      "La fenêtre principale est en plein écran",
    ),
    "maxValue": MessageLookupByLibrary.simpleMessage("maximum"),
    "messages": MessageLookupByLibrary.simpleMessage("Message"),
    "minValue": MessageLookupByLibrary.simpleMessage("minimum"),
    "minute": MessageLookupByLibrary.simpleMessage("diviser"),
    "modifyChapterTitle": MessageLookupByLibrary.simpleMessage(
      "Modifier le titre de la section",
    ),
    "modifyTimeAndCover": MessageLookupByLibrary.simpleMessage(
      "Modifier l’heure avec la couverture",
    ),
    "multiply": MessageLookupByLibrary.simpleMessage("fois"),
    "myGames": MessageLookupByLibrary.simpleMessage("Mon jeu"),
    "nameBranchSettingsSaved": m9,
    "newProject": MessageLookupByLibrary.simpleMessage(
      "Créer un nouveau projet",
    ),
    "newWindowFullScreen": MessageLookupByLibrary.simpleMessage(
      "La nouvelle fenêtre s’affiche en plein écran",
    ),
    "newWindowFullScreenDescription": MessageLookupByLibrary.simpleMessage(
      "Réglage automatique en mode plein écran lorsqu’une nouvelle fenêtre s’ouvre",
    ),
    "news": MessageLookupByLibrary.simpleMessage("Nouvelles"),
    "no": MessageLookupByLibrary.simpleMessage("Non, vous ne le faites pas"),
    "noArchivesFound": MessageLookupByLibrary.simpleMessage(
      "Aucune archive trouvée",
    ),
    "noAudioTrack": MessageLookupByLibrary.simpleMessage("Pas de pistes audio"),
    "noBranchesToSet": MessageLookupByLibrary.simpleMessage(
      "Le nœud actuel n’a pas plusieurs branches et les paramètres de branche ne peuvent pas être définis",
    ),
    "noGlobalValuesFoundAddFirst": MessageLookupByLibrary.simpleMessage(
      "Si vous ne trouvez pas de valeur globale, ajoutez-en d’abord une à la gestion de la valeur globale",
    ),
    "noSubtitle": MessageLookupByLibrary.simpleMessage("Pas de sous-titres"),
    "noValidFilesFound": MessageLookupByLibrary.simpleMessage(
      "Aucun fichier de téléchargement valide n’a été trouvé, assurez-vous que votre projet n’inclut que les types de fichiers pris en charge",
    ),
    "noWorkshopItems": MessageLookupByLibrary.simpleMessage(
      "Vous n’avez pas d’objets de l’Atelier à mettre à jour",
    ),
    "nodeDetails": MessageLookupByLibrary.simpleMessage("Détails du nœud"),
    "nodeMarkedAsWatched": m10,
    "normalBranch": MessageLookupByLibrary.simpleMessage(
      "Ramification régulière",
    ),
    "notEqual": MessageLookupByLibrary.simpleMessage("Pas égal"),
    "numberType": MessageLookupByLibrary.simpleMessage("chiffre"),
    "opaque": MessageLookupByLibrary.simpleMessage("opaque"),
    "openFlowChartFailed": MessageLookupByLibrary.simpleMessage(
      "Echec de l’ouverture de l’organigramme : \$e",
    ),
    "openGame": MessageLookupByLibrary.simpleMessage("Ouvrez le jeu"),
    "openGameError": m11,
    "openGameInNewWindow": MessageLookupByLibrary.simpleMessage(
      "Ouvrir le projet de jeu dans une nouvelle fenêtre",
    ),
    "openGameInNewWindowDesc": MessageLookupByLibrary.simpleMessage(
      "Lorsqu’il est activé, le projet de jeu s’ouvre dans une nouvelle fenêtre, et lorsqu’il est désactivé, il s’ouvre dans la fenêtre actuelle",
    ),
    "openGameTitle": MessageLookupByLibrary.simpleMessage("Ouvrez le jeu"),
    "ovalNode": MessageLookupByLibrary.simpleMessage("Nœuds elliptiques"),
    "parallelogramNode": MessageLookupByLibrary.simpleMessage(
      "Nœuds de parallélogramme",
    ),
    "pause": MessageLookupByLibrary.simpleMessage("Temps d’arrêt"),
    "play": MessageLookupByLibrary.simpleMessage("Jouer"),
    "playPause": MessageLookupByLibrary.simpleMessage("Lecture/Pause"),
    "playTimeSettings": MessageLookupByLibrary.simpleMessage(
      "Paramètres de début et de fin de la lecture",
    ),
    "playbackControl": MessageLookupByLibrary.simpleMessage(
      "Commandes de lecture",
    ),
    "playbackProgress": MessageLookupByLibrary.simpleMessage(
      "Progression de la lecture",
    ),
    "playbackRate": MessageLookupByLibrary.simpleMessage("Taux de lecture"),
    "playbackSpeed": MessageLookupByLibrary.simpleMessage("Vitesse de lecture"),
    "playbackStatus": MessageLookupByLibrary.simpleMessage("État de lecture"),
    "player": MessageLookupByLibrary.simpleMessage("joueur"),
    "pleaseEnterTrueOrFalse": MessageLookupByLibrary.simpleMessage(
      "Entrez true ou false",
    ),
    "pleaseEnterValidNumber": MessageLookupByLibrary.simpleMessage(
      "Veuillez saisir un nombre significatif",
    ),
    "pleaseEnterValue": MessageLookupByLibrary.simpleMessage(
      "Entrez une valeur numérique",
    ),
    "pleaseEnterValueName": MessageLookupByLibrary.simpleMessage(
      "Entrez un nom numérique",
    ),
    "pleaseEnterVariableName": MessageLookupByLibrary.simpleMessage(
      "Entrez un nom de variable",
    ),
    "pleaseSelectProject": MessageLookupByLibrary.simpleMessage(
      "Veuillez d’abord sélectionner le projet",
    ),
    "popularGames": MessageLookupByLibrary.simpleMessage("Jeux populaires"),
    "preparingConfig": MessageLookupByLibrary.simpleMessage(
      "Préparer la configuration",
    ),
    "preparingProjectFiles": MessageLookupByLibrary.simpleMessage(
      "Préparation du dossier de projet...",
    ),
    "previewImage": MessageLookupByLibrary.simpleMessage(
      "Prévisualiser l’image",
    ),
    "previewImageDefault": MessageLookupByLibrary.simpleMessage("faire défaut"),
    "private": MessageLookupByLibrary.simpleMessage("Privé"),
    "profile": MessageLookupByLibrary.simpleMessage("Données personnelles"),
    "projectCreated": MessageLookupByLibrary.simpleMessage(
      "Le projet a été créé",
    ),
    "projectExistsContent": MessageLookupByLibrary.simpleMessage(
      "Le projet existe déjà, va-t-il être dans le projet ?",
    ),
    "projectExistsTitle": MessageLookupByLibrary.simpleMessage(
      "Le projet existe déjà",
    ),
    "projectLoaded": m12,
    "projectNameHint": MessageLookupByLibrary.simpleMessage(
      "Veuillez entrer un nom de projet",
    ),
    "projectNotFound": m13,
    "public": MessageLookupByLibrary.simpleMessage("Public"),
    "publicMode": MessageLookupByLibrary.simpleMessage(
      "Mode lieu public/Mode Live",
    ),
    "publicModeDesc": MessageLookupByLibrary.simpleMessage(
      "Lorsqu’elle est activée, l’interface de l’atelier impose l’utilisation des balises de tout le monde et n’affiche pas les balises qui ne sont pas liées à l’espace de travail",
    ),
    "publish": MessageLookupByLibrary.simpleMessage("publier"),
    "publishTime": MessageLookupByLibrary.simpleMessage("Libéré"),
    "puzzleHint": MessageLookupByLibrary.simpleMessage(
      "Une mauvaise réponse a été réduite",
    ),
    "qteBranch": MessageLookupByLibrary.simpleMessage("Branche QTE"),
    "qteButtonDisplayTime": m14,
    "qteButtonDurationSeconds": m15,
    "qteButtonPosition": MessageLookupByLibrary.simpleMessage(
      "Position du bouton QTE",
    ),
    "qteDuration": MessageLookupByLibrary.simpleMessage("Durée du bouton QTE"),
    "qteDurationDescription": MessageLookupByLibrary.simpleMessage(
      "Les joueurs doivent réagir dans ce délai",
    ),
    "qteFailBranch": MessageLookupByLibrary.simpleMessage(
      "Branche ayant échoué de QTE",
    ),
    "qteFailLabel": MessageLookupByLibrary.simpleMessage("échouer"),
    "qtePositionInfo": m16,
    "qteSuccessBranch": MessageLookupByLibrary.simpleMessage(
      "QTE Branche réussie",
    ),
    "qteSuccessLabel": MessageLookupByLibrary.simpleMessage("réussir"),
    "questionDescription": MessageLookupByLibrary.simpleMessage(
      "Description du problème",
    ),
    "range": MessageLookupByLibrary.simpleMessage("gamme"),
    "rate": MessageLookupByLibrary.simpleMessage("Taux de lecture"),
    "recentlyEdited": MessageLookupByLibrary.simpleMessage("Récemment modifié"),
    "recentlyPlayed": MessageLookupByLibrary.simpleMessage("Joué récemment"),
    "rectangleNode": MessageLookupByLibrary.simpleMessage(
      "Nœuds rectangulaires",
    ),
    "rectangular": MessageLookupByLibrary.simpleMessage("rectangle"),
    "remainingTimeLabel": m17,
    "remoteReservedWord": MessageLookupByLibrary.simpleMessage(
      "remote est réservé au programme, veuillez changer le nom du projet",
    ),
    "removeAll": MessageLookupByLibrary.simpleMessage("Supprimer tout"),
    "removeAllConnections": MessageLookupByLibrary.simpleMessage(
      "Retirez tous les raccords",
    ),
    "removeImage": MessageLookupByLibrary.simpleMessage("Supprimer l’image"),
    "required": MessageLookupByLibrary.simpleMessage("Obligatoire"),
    "resume": MessageLookupByLibrary.simpleMessage(
      "Revenez en arrière et continuez à jouer",
    ),
    "retry": MessageLookupByLibrary.simpleMessage("réessayer"),
    "retryLoading": MessageLookupByLibrary.simpleMessage(
      "Cliquez sur Réessayer",
    ),
    "save": MessageLookupByLibrary.simpleMessage("Sauvegarder"),
    "saveFailed": MessageLookupByLibrary.simpleMessage(
      "Échec de l’enregistrement, veuillez vérifier les journaux",
    ),
    "saveFlowchart": MessageLookupByLibrary.simpleMessage(
      "Enregistrer l’organigramme",
    ),
    "saveFlowchartFailed": MessageLookupByLibrary.simpleMessage(
      "Échec de l’enregistrement de l’organigramme",
    ),
    "saveSettings": MessageLookupByLibrary.simpleMessage(
      "Enregistrer les paramètres",
    ),
    "saving": MessageLookupByLibrary.simpleMessage("Sauvegarder..."),
    "savingBranchSettings": MessageLookupByLibrary.simpleMessage(
      "Enregistrement des paramètres de branche...",
    ),
    "screenPreview": MessageLookupByLibrary.simpleMessage("Aperçu de l’écran"),
    "screenshots": MessageLookupByLibrary.simpleMessage("Capture d’écran"),
    "search": MessageLookupByLibrary.simpleMessage("Rechercher"),
    "second": MessageLookupByLibrary.simpleMessage("deuxième"),
    "seconds": MessageLookupByLibrary.simpleMessage("deuxième"),
    "seed": MessageLookupByLibrary.simpleMessage("Graines"),
    "segmented": MessageLookupByLibrary.simpleMessage("Sous-niveau"),
    "selectBranchToSet": MessageLookupByLibrary.simpleMessage(
      "Sélectionnez la branche que vous souhaitez configurer",
    ),
    "selectContentRating": MessageLookupByLibrary.simpleMessage(
      "Veuillez sélectionner une classification de contenu",
    ),
    "selectWorkshopItemToUpdate": MessageLookupByLibrary.simpleMessage(
      "Sélectionnez l’élément de l’Atelier que vous souhaitez mettre à jour",
    ),
    "setAndEnableConditions": MessageLookupByLibrary.simpleMessage(
      "Définit et active les conditions d’affichage de l’option actuelle",
    ),
    "setAndEnableValueChanges": MessageLookupByLibrary.simpleMessage(
      "Définir et activer la modification de la valeur après avoir sélectionné une option",
    ),
    "setBranchConditionsAndChanges": m18,
    "setBranchParams": MessageLookupByLibrary.simpleMessage(
      "Définir le type de branche et les paramètres",
    ),
    "setCurrentTime": MessageLookupByLibrary.simpleMessage(
      "Régler l’heure actuelle",
    ),
    "setOptionsAndValueChanges": MessageLookupByLibrary.simpleMessage(
      "Définir les options et modifier les valeurs",
    ),
    "setTo": MessageLookupByLibrary.simpleMessage("Se mettre à"),
    "settings": MessageLookupByLibrary.simpleMessage("Installer"),
    "settingsSaved": MessageLookupByLibrary.simpleMessage(
      "Les paramètres sont enregistrés",
    ),
    "shape": MessageLookupByLibrary.simpleMessage("Formes d’images"),
    "showController": MessageLookupByLibrary.simpleMessage(
      "Afficher la barre de contrôle",
    ),
    "showControllerDuringPlayback": MessageLookupByLibrary.simpleMessage(
      "Affichage de la barre des commandes pendant la lecture de la vidéo",
    ),
    "showVideoController": MessageLookupByLibrary.simpleMessage(
      "Afficher la barre de contrôle vidéo",
    ),
    "showVideoControls": MessageLookupByLibrary.simpleMessage(
      "Afficher la barre de contrôle vidéo",
    ),
    "showVideoControlsDescription": MessageLookupByLibrary.simpleMessage(
      "Affichage de la barre des commandes pendant la lecture de la vidéo",
    ),
    "sort": MessageLookupByLibrary.simpleMessage("trier"),
    "sortBy": MessageLookupByLibrary.simpleMessage("Trier par"),
    "sortByFavorites": MessageLookupByLibrary.simpleMessage(
      "Par le nombre de favoris",
    ),
    "sortByPublishDate": MessageLookupByLibrary.simpleMessage("Libéré"),
    "sortBySubscribers": MessageLookupByLibrary.simpleMessage(
      "Par nombre d’abonnements",
    ),
    "sortByUpdateDate": MessageLookupByLibrary.simpleMessage("Actualisé"),
    "sortByVote": MessageLookupByLibrary.simpleMessage(
      "Par le nombre de likes",
    ),
    "startElementIdNotFound": MessageLookupByLibrary.simpleMessage(
      "Impossible d’obtenir l’ID de l’élément de départ du projet",
    ),
    "startGame": MessageLookupByLibrary.simpleMessage("Jouer au jeu"),
    "startNode": MessageLookupByLibrary.simpleMessage("Nœud de départ"),
    "startNodeVideoCount": m19,
    "startPoint": MessageLookupByLibrary.simpleMessage("point de départ"),
    "startTime": MessageLookupByLibrary.simpleMessage("Commencer\nHeure"),
    "startTimeBeforeEndTime": MessageLookupByLibrary.simpleMessage(
      "L’heure de début doit être antérieure à l’heure de fin",
    ),
    "startTimeLabel": MessageLookupByLibrary.simpleMessage("Heure de début"),
    "steamAuthorOtherFiles": MessageLookupByLibrary.simpleMessage(
      "Autres documents de l’auteur",
    ),
    "steamChallenge": MessageLookupByLibrary.simpleMessage(
      "Lancez-vous dans le défi",
    ),
    "steamGallery": MessageLookupByLibrary.simpleMessage(
      "Galerie du Steam Workshop",
    ),
    "steamLimitedAccount": MessageLookupByLibrary.simpleMessage(
      "Comptes restreints Steam",
    ),
    "steamWorkshop": MessageLookupByLibrary.simpleMessage("Atelier vapeur"),
    "stop": MessageLookupByLibrary.simpleMessage("Arrête"),
    "storageNode": MessageLookupByLibrary.simpleMessage("Nœuds de stockage"),
    "style": MessageLookupByLibrary.simpleMessage("Style d’image"),
    "subscribeFailed": MessageLookupByLibrary.simpleMessage(
      "Échec de l’abonnement : ",
    ),
    "subscribeSuccess": MessageLookupByLibrary.simpleMessage(
      "L’abonnement a réussi, commencez à télécharger...",
    ),
    "subtitle": MessageLookupByLibrary.simpleMessage("légende"),
    "subtract": MessageLookupByLibrary.simpleMessage("soustraire"),
    "switchHorizontal": MessageLookupByLibrary.simpleMessage(
      "Passer à une disposition horizontale",
    ),
    "switchVertical": MessageLookupByLibrary.simpleMessage(
      "Passer à une disposition verticale",
    ),
    "textType": MessageLookupByLibrary.simpleMessage("SMS"),
    "thickness": MessageLookupByLibrary.simpleMessage("épaisseur"),
    "timeEdit": MessageLookupByLibrary.simpleMessage("Édition de l’heure"),
    "timeLimit": MessageLookupByLibrary.simpleMessage(
      "Limite de temps (secondes)",
    ),
    "timedBranch": MessageLookupByLibrary.simpleMessage(
      "Succursales limitées dans le temps",
    ),
    "title": MessageLookupByLibrary.simpleMessage("titre"),
    "titleCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
      "Le titre ne peut pas être vide",
    ),
    "titlePositionInfo": m20,
    "to": MessageLookupByLibrary.simpleMessage("arriver"),
    "toggleConnectable": MessageLookupByLibrary.simpleMessage(
      "La bascule peut être connectée",
    ),
    "toggleResizable": MessageLookupByLibrary.simpleMessage(
      "Basculer pour redimensionner",
    ),
    "toggleSortDirection": MessageLookupByLibrary.simpleMessage(
      "Bascule le sens de tri",
    ),
    "transparent": MessageLookupByLibrary.simpleMessage("transparent"),
    "unknownNodeType": MessageLookupByLibrary.simpleMessage("Type inconnu"),
    "unsubscribe": MessageLookupByLibrary.simpleMessage(
      "Résilier votre abonnement",
    ),
    "unsubscribeFailed": MessageLookupByLibrary.simpleMessage(
      "Échec de l’annulation de l’abonnement",
    ),
    "unsubscribeSuccess": MessageLookupByLibrary.simpleMessage(
      "L’abonnement a été désabonné avec succès",
    ),
    "unsupportedFileFormat": MessageLookupByLibrary.simpleMessage(
      "Formats de fichiers non pris en charge, le Workshop n’autorise que les fichiers vidéo, jpg et json",
    ),
    "updateSuccess": MessageLookupByLibrary.simpleMessage(
      "Mise à jour réussie !",
    ),
    "updateTime": MessageLookupByLibrary.simpleMessage("Actualisé"),
    "updateWorkshop": MessageLookupByLibrary.simpleMessage(
      "Objets de l’atelier mis à jour",
    ),
    "updateWorkshopError": m21,
    "updating": MessageLookupByLibrary.simpleMessage("Actualisation..."),
    "upload": MessageLookupByLibrary.simpleMessage("télécharger"),
    "uploadFailed": MessageLookupByLibrary.simpleMessage(
      "Échec du téléchargement",
    ),
    "uploadFailedWithColon": MessageLookupByLibrary.simpleMessage(
      "Echec du téléchargement : ",
    ),
    "uploadNow": MessageLookupByLibrary.simpleMessage(
      "Téléchargez-le maintenant",
    ),
    "uploadSuccess": MessageLookupByLibrary.simpleMessage(
      "Le téléchargement est réussi",
    ),
    "uploadWorkshop": MessageLookupByLibrary.simpleMessage(
      "Télécharger l’atelier",
    ),
    "uploading": MessageLookupByLibrary.simpleMessage("Téléchargement"),
    "uploadingContent": MessageLookupByLibrary.simpleMessage(
      "Télécharger du contenu",
    ),
    "uploadingPleaseWait": MessageLookupByLibrary.simpleMessage(
      "En cours de téléchargement, veuillez patienter...",
    ),
    "uploadingPreviewImage": MessageLookupByLibrary.simpleMessage(
      "Télécharger un aperçu",
    ),
    "useNewWindowForEditing": MessageLookupByLibrary.simpleMessage(
      "Une nouvelle fenêtre ouvre l’interface Modifier le projet",
    ),
    "useNewWindowForEditingDescription": MessageLookupByLibrary.simpleMessage(
      "Lorsqu’il est activé, l’éditeur de projet s’ouvre dans une nouvelle fenêtre, et lorsqu’il est désactivé, il s’ouvre dans la fenêtre actuelle",
    ),
    "valueInputHint": MessageLookupByLibrary.simpleMessage(
      "Entrez une valeur initiale",
    ),
    "valueLabel": MessageLookupByLibrary.simpleMessage("valeur numérique"),
    "variableAlreadyExists": MessageLookupByLibrary.simpleMessage(
      "Le nom de la variable existe déjà",
    ),
    "variableName": MessageLookupByLibrary.simpleMessage("Nom de la variable"),
    "variableType": MessageLookupByLibrary.simpleMessage("type"),
    "version": MessageLookupByLibrary.simpleMessage("Version"),
    "verticalLayoutDescription": MessageLookupByLibrary.simpleMessage(
      "Interface de montage vidéo verticale (recommandée pour les écrans haute résolution)",
    ),
    "videoCover": MessageLookupByLibrary.simpleMessage("Couverture vidéo"),
    "videoFileNotExist": m22,
    "videoNode": MessageLookupByLibrary.simpleMessage("Nœuds vidéo"),
    "videoNodeLocked": MessageLookupByLibrary.simpleMessage(
      "Ce nœud vidéo n’a pas été déverrouillé, veuillez d’abord regarder la vidéo précédente",
    ),
    "videoPlayback": MessageLookupByLibrary.simpleMessage("Lecture vidéo"),
    "videoPlaybackError": m23,
    "videoTimeAndCover": MessageLookupByLibrary.simpleMessage(
      "Temps et couverture de la vidéo",
    ),
    "viewFlowChart": MessageLookupByLibrary.simpleMessage(
      "Voir l’organigramme",
    ),
    "viewFlowchart": MessageLookupByLibrary.simpleMessage(
      "Voir l’organigramme",
    ),
    "visibility": MessageLookupByLibrary.simpleMessage("Visibilité"),
    "volume": MessageLookupByLibrary.simpleMessage("Niveau de volume"),
    "volumeControl": MessageLookupByLibrary.simpleMessage("Contrôle du volume"),
    "watchedNodesCount": MessageLookupByLibrary.simpleMessage(
      "Le nombre de nœuds surveillés",
    ),
    "workshop": MessageLookupByLibrary.simpleMessage("Atelier"),
    "workshopItemUpdated": MessageLookupByLibrary.simpleMessage(
      "Le projet Workshop a été mis à jour",
    ),
    "workshopItemUploaded": MessageLookupByLibrary.simpleMessage(
      "Le projet Workshop a été téléchargé",
    ),
    "workshopItems": MessageLookupByLibrary.simpleMessage("Projet d’atelier"),
    "workshopRecommendedDescription": MessageLookupByLibrary.simpleMessage(
      "Écrit par le chef du village moteur lui-même, les villageois ont tous dit que ce manuel évoluera automatiquement avec la mise à jour de la version",
    ),
    "workshopRecommendedTitle": MessageLookupByLibrary.simpleMessage(
      "Guide pour sauver le monde",
    ),
    "workshopRuleNoAds": MessageLookupByLibrary.simpleMessage(
      "Pas de publicité",
    ),
    "workshopRuleNoAdult": MessageLookupByLibrary.simpleMessage(
      "Pas de pornographie ou de nudité photographique ou authentique",
    ),
    "workshopRuleNoCopyright": MessageLookupByLibrary.simpleMessage(
      "Pas de violation du droit d’auteur",
    ),
    "workshopRuleNoMisleading": MessageLookupByLibrary.simpleMessage(
      "Il n’y a pas de prévisualisations trompeuses",
    ),
    "workshopRuleNoOffensive": MessageLookupByLibrary.simpleMessage(
      "Pas d’offensant ou de violent, gore",
    ),
    "workshopRules": MessageLookupByLibrary.simpleMessage(
      "Règlement de l’atelier",
    ),
    "workshopRulesDescription": MessageLookupByLibrary.simpleMessage(
      "Avant de soumettre une vidéo interactive au Workshop, assurez-vous qu’elle n’enfreint pas les Conditions d’utilisation de Steam, sinon la vidéo interactive sera supprimée :",
    ),
    "workshopRulesSpecial": MessageLookupByLibrary.simpleMessage(
      "En particulier, les vidéos interactives, les aperçus et les descriptions doivent respecter les règles suivantes :",
    ),
  };
}
