{"@@locale": "az", "gameTitle": "İnteraktiv video oyun mühərriki", "settings": "Qur", "allSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "credits": "Kreditlə<PERSON>ı", "creditsCreativeTitle": "Yaradıcılıq &planlaşdırma", "creditsGameProducer": "Oyun Prodüseri", "creditsCreativeSource": "<PERSON><PERSON><PERSON> mən<PERSON>ə<PERSON>ə<PERSON>", "creditsSoftwarePlanner": "<PERSON><PERSON><PERSON>ı<PERSON>", "creditsGameplayPlanner": "Gameplay planlaşdırması", "creditsProgrammingTitle": "Proq<PERSON>ın <PERSON>", "creditsSoftwareArchitect": "Proqram arxitekturası", "creditsCodeWriter": "Kod yazılışı", "creditsVisualTitle": "Vizual di<PERSON><PERSON>", "creditsArtDesigner": "İnc<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "creditsAnimationDesigner": "Animasiya Konstruktor", "creditsMarketingTitle": "<PERSON><PERSON><PERSON><PERSON>", "creditsVideoProducer": "Video istehsalı", "creditsCopywriter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "creditsThanksTitle": "Oyun oynadığın üçün təşəkkür edirəm!", "creditsSpecialThanks": "<PERSON><PERSON><PERSON><PERSON> təşəkkür edirəm sizə", "creditsSubtitle": "<PERSON>qram təminatının hazırlanmasında iştirak edən komandalara və töhfə verənlərə bax", "openGameInNewWindow": "<PERSON><PERSON> layihəsini yeni pəncərədə aç", "openGameInNewWindowDesc": "Fəal olduqda oyun layihəsi yeni pəncərədə açılır, qeyri-fəal olduqda isə hazırkı pəncərədə açılır", "publicMode": "İctimai Məkan <PERSON>/Canlı Rejim", "publicModeDesc": "Fəal olduqda Workshop interfeysi hər kəsin etiketlərinin istifadəsini həyata keçirəcək və iş yeri olmayan etiketləri nümayiş etdirməyəcək", "verticalLayoutDescription": "Şaquli video redaktə interfeysi (yüksək çözlüşlü ekranlar üçün tövsiyə olunur)", "language": "<PERSON><PERSON>ı", "fullscreen": "<PERSON> ekran modu", "mainWindowFullscreen": "<PERSON><PERSON><PERSON> pəncərə tam ekranlı", "createNewGame": "<PERSON><PERSON>", "workshop": "İş", "openGame": "Oyunu aç", "home": "ana səhifə", "activity": "dinamik", "profile": "Şə<PERSON>si məlumatlar", "downloads": "<PERSON><PERSON>", "friends": "Do<PERSON><PERSON>", "messages": "İsmarış:", "elementParams": "element parametrləri", "backgroundColor": "<PERSON><PERSON><PERSON> fon rəngi", "thickness": "qalınlıq", "borderColor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rəngi", "elevation": "hündürlük", "videoPlayback": "Video oynatma", "playbackControl": "Playback n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "play": "Çal", "pause": "<PERSON><PERSON><PERSON>", "stop": "<PERSON><PERSON><PERSON>", "playbackRate": "<PERSON><PERSON><PERSON><PERSON>", "videoCover": "Video cover", "captureCurrentFrame": "Hazırkı çərçivəni tutur", "timeEdit": "<PERSON><PERSON><PERSON>", "startTime": "Başla\nVaxt", "endTime": "<PERSON><PERSON> gə<PERSON>", "setCurrentTime": "Hazırkı vaxtı təyin edin", "addVideo": "Video əlavə et", "addDiamond": "<PERSON><PERSON>rp<PERSON> əlavə et", "addRectangle": "Düzbucaqlı əlavə et", "addResizableRectangle": "Dəyişənli düzbucaqlı əlavə et", "addOval": "Ellips əlavə et", "addParallelogram": "Paraleloqram əlavə et", "addHexagon": "Altıbucaqlı əlavə et", "addStorage": "Ambar əlavə et", "addImage": "Şəkil əlavə et", "removeAll": "Hamısını sil", "saveFlowchart": "Axış çarxını qeyd et", "loadFlowchart": "Axış çarxını oxu", "workshopItems": "Workshop layihəsi", "loadingFailed": "Yük<PERSON><PERSON><PERSON>ə bilmədi", "retryLoading": "<PERSON><PERSON>dən <PERSON>", "openGameTitle": "Oyunu aç", "showController": "İdar<PERSON> ç<PERSON>uğunu gö<PERSON>ər", "hideController": "İdarə çubuğunu gizlət", "myGames": "<PERSON><PERSON><PERSON><PERSON>yun", "popularGames": "<PERSON><PERSON><PERSON> oyunlar", "news": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recentlyPlayed": "<PERSON>u yaxın<PERSON>a oynanıldı", "recentlyEdited": "Bu yaxınlarda redaktə olundu", "achievementDisplay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "achievements": "nail olmaq", "screenshots": "Ekran Görüntüsü", "player": "gamer", "games": "<PERSON><PERSON>", "search": "Axtar", "modifyTimeAndCover": "Örtüyü ilə vaxtı dəyişdirin", "delete": "Sil", "removeAllConnections": "Bütün bağlantıları sil", "toggleConnectable": "Bağlan", "toggleResizable": "<PERSON><PERSON><PERSON><PERSON><PERSON> boy<PERSON>la", "segmented": "alt-üst", "curved": "düzbucaqlı", "rectangular": "dördbucaq", "startPoint": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nö<PERSON>ə<PERSON>", "loadingFailedRetry": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> uğrayarsa, Retry", "flowchartMissingStart": "Axış çarxı başlanğıc nöqtəsi elementinin çatışmadığı üçün", "videoTimeAndCover": "Video vaxt və üz qabığı", "coverLoadFailed": "Qapayan səhifə yükləmə iflas etdi", "close": "Söndürmək", "confirm": "T<PERSON>sdiqlə", "playPause": "Çal/Arala", "invalidStartTime": "Başlama vaxtı səhvdir", "invalidEndTime": "Bitiş vaxtı səhvdir", "playbackStatus": "_<PERSON><PERSON>t", "currentPosition": "<PERSON><PERSON><PERSON> bur<PERSON>an", "volume": "<PERSON><PERSON><PERSON> sə<PERSON>", "completed": "Olub-olmaması", "duration": "<PERSON><PERSON><PERSON> müddət", "rate": "<PERSON><PERSON><PERSON><PERSON>", "playbackProgress": "Playback inkiş<PERSON><PERSON>", "volumeControl": "<PERSON><PERSON><PERSON> tənz<PERSON>", "audioTrack": "İzlə", "subtitle": "yazı", "noSubtitle": "Altyazı yoxdur", "closeSubtitle": "Alt yazıları söndür", "modifyChapterTitle": "<PERSON><PERSON><PERSON><PERSON> başlığını dəyişdir", "setBranchParams": "Budaq növünü və parametrlərini təyin edin", "noAudioTrack": "<PERSON><PERSON><PERSON> izləri yoxdur", "enterProjectName": "<PERSON><PERSON><PERSON> adı daxil edin", "projectNameHint": "Xahiş edirik layihə adını daxil edin", "cancel": "<PERSON>ə<PERSON>v etmək", "projectCreated": "<PERSON><PERSON><PERSON>", "fileNotFound": "<PERSON><PERSON> m<PERSON>d <PERSON>", "upload": "yukle", "uploadWorkshop": "Workshop yukle", "importImage": "Rə<PERSON><PERSON><PERSON><PERSON>l et", "removeImage": "<PERSON><PERSON><PERSON><PERSON> sil", "previewImageDefault": "ön qurğulu", "visibility": "G<PERSON>rünüş", "uploadNow": "İndi yüklə", "uploading": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uploadFailed": "Yükləmə bacarılmadı", "uploadSuccess": "Yükləmə uğurludur", "download": "<PERSON><PERSON>", "downloadWorkshop": "Workshop Yüklə", "downloading": "<PERSON><PERSON>", "downloadFailed": "Yükləmə bacarılmadı", "downloadSuccess": "<PERSON><PERSON><PERSON>", "invalidFileType": "<PERSON>əhv fayl növü", "unsupportedFileFormat": "Dəstəklənm<PERSON>yən fayl formatları, Workshop yalnız video, jpg və json faylları imkan verir", "noValidFilesFound": "<PERSON><PERSON> bir əsaslı yükləmə faylı tapılmadı, əmin olun ki, layihənizə yalnız dəstəklənən fayl növləri daxildir", "SearchWorkshop": "Emalatxanada axtar", "style": "<PERSON><PERSON><PERSON><PERSON> tərzi", "AncientChinese": "<PERSON><PERSON><PERSON><PERSON>", "shape": "Şəkil formaları", "ilpEditor": "<PERSON><PERSON>", "steamLimitedAccount": "Steam Məhdudlaşdırılmış Hesablar", "sort": "<PERSON><PERSON><PERSON><PERSON>", "joinDiscord": "Gameplay müzakir<PERSON><PERSON><PERSON><PERSON> üçün Discord-a qoşulun", "back": "qayıt", "no": "<PERSON><PERSON><PERSON>, sən yox,", "resume": "<PERSON><PERSON> qay<PERSON><PERSON><PERSON><PERSON> oynamağa davam edin", "retry": "yenidən etmək", "seed": "toxum", "about": "ilə bağlı", "exit": "Oyundan çıxın", "confirmExitApp": "Əminsinizmi ki, proqramı atmaq istəyirsiniz?", "challenge": "<PERSON><PERSON><PERSON> ox<PERSON>", "gallery": "Qalisiya", "steamChallenge": "Ç<PERSON><PERSON><PERSON><PERSON>ə başla", "steamGallery": "Buxar Emalatxanası Qalereyası", "Subscribe": "abunə ol", "SubscribeAndDownload": "Abunə ol və yüklə", "clickToSubscribeAndDownload": "Yükləməyə başlamaq üçün Abunə Ol düyməsinə basın", "createChallenge": "Çətinlik yarat", "challengeName": "<PERSON><PERSON><PERSON><PERSON><PERSON>n adı", "ilpDesc": "təsvir", "Everyone": "<PERSON><PERSON>ın hamısı", "NSFW": "<PERSON><PERSON>ri<PERSON><PERSON><PERSON><PERSON><PERSON> yer<PERSON>ər", "public": "İctimai", "friendsonly": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "private": "<PERSON><PERSON><PERSON><PERSON>", "Anime": "<PERSON><PERSON> janrı", "Realistic": "Realist üslub", "Pixel": "<PERSON><PERSON><PERSON>ə<PERSON>", "Ancient Chinese": "<PERSON><PERSON><PERSON><PERSON>", "Other": "<PERSON><PERSON><PERSON><PERSON>", "Landscape": "Transversiyalı Refraktor", "Portrait": "Uzunbucaqlı", "Square": "kvadrat", "puzzleHint": "<PERSON>ir səhv cavab a<PERSON>b", "steamWorkshop": "Buxar emalatxanası", "steamAuthorOtherFiles": "<PERSON><PERSON><PERSON><PERSON><PERSON> digər sənədləri", "publishTime": "<PERSON><PERSON>", "updateTime": "Yeniləndi", "ageRating": "<PERSON><PERSON> qiymə<PERSON>", "adultAgreementTitle": "<PERSON><PERSON><PERSON> edirəm, ən azı 18 yaşınız olduğuna əmin olun", "adultAgreementContent": "Steam Workshop bütün dünya oyunçularının birgə yaratdığı məzmunu özündə ehtiva edir. Bu, ictimai yerlərdə baxmaq üçün uyğun olmayan məzmunla bağlı ola bilər.", "Unsubscribed": "<PERSON><PERSON><PERSON>l", "itemNotSubscribed": "Abunə olmayan əşyalar abunə olunduqdan sonra avtomatik yüklənəcək", "subscribeSuccess": "<PERSON>nə<PERSON>ğ<PERSON>, yü<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> başlayın...", "subscribeFailed": "%s: %s ", "downloadingPleaseWait": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, zə<PERSON>ə<PERSON>, daha sonra", "Subscribed": "<PERSON><PERSON><PERSON>", "sortBySubscribers": "Abunə sayına görə", "sortByVote": "Bə<PERSON>ə<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sayına görə", "sortByFavorites": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sayına gö<PERSON>ə", "sortByPublishDate": "<PERSON><PERSON>", "sortByUpdateDate": "Yeniləndi", "sortBy": "S<PERSON>rala", "toggleSortDirection": "<PERSON><PERSON><PERSON><PERSON>", "autoFullScreenVideo": "Video oynatarkən avtomatik tam ekran", "newWindowFullScreen": "Yeni pəncərə tam ekrandır", "newWindowFullScreenDescription": "Yeni pəncərə açılanda avtomatik olaraq tam ekran rejiminə təyin et", "filePreparingPleaseRetry": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, daha sonra yenidən cəhd edin", "remoteReservedWord": "<PERSON><PERSON><PERSON> u<PERSON>, x<PERSON><PERSON> edirik layihə adını dəyişin", "workshopRules": "İş bölgüsü qaydaları", "workshopRulesDescription": "Workshop-a interaktiv video göndərməzdən əvvəl zəhmət olma<PERSON>, əmin olun ki, bu, Steam-in Xidmət Şərtlərini pozmur. Əks halda interaktiv video göt<PERSON>r<PERSON>ləcək:", "workshopRulesSpecial": "<PERSON><PERSON>susilə, interaktiv videolar, önbaxışlar və təsvirlər aşağıdakı qaydalara riayət etməlidir:", "workshopRuleNoAdult": "Heç bir foto və ya mötəbər pornoqrafiya və çılpaqlıq", "workshopRuleNoOffensive": "Nə təhqir, nə də qəddarlıq, gore", "workshopRuleNoCopyright": "<PERSON><PERSON><PERSON><PERSON><PERSON> hüququ pozuntusu yoxdur", "workshopRuleNoAds": "<PERSON><PERSON><PERSON> yoxdur", "workshopRuleNoMisleading": "Aldadıcı önbaxışlar yoxdur", "archive": "arxiv", "flowchart": "Prosesi nəzərdən keçirin", "startGame": "<PERSON>yun oyna<PERSON>", "viewFlowchart": "Axış çarxını bax", "nodeDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "elementId": "ID", "elementType": "yaz", "elementText": "mətn", "startNode": "Başlanğıc düyünü", "videoNode": "Video düyünləri", "rectangleNode": "Düzbucaqlı dü<PERSON>ünlər", "diamondNode": "<PERSON>am diamond d<PERSON><PERSON><PERSON><PERSON>", "storageNode": "<PERSON><PERSON>", "ovalNode": "<PERSON><PERSON><PERSON>", "parallelogramNode": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hexagonNode": "Altıbucaqlı dü<PERSON>ünlər", "imageNode": "<PERSON><PERSON><PERSON>", "unknownNodeType": "<PERSON><PERSON>'lum növ", "flowchartFileNotFound": "FLOWCHART.json faylı tapıla bilmədi", "flowchartLoadError": "Axış çarxı yüklənərkən xəta oldu: {error}", "createTime": "<PERSON><PERSON><PERSON><PERSON> vaxtı", "lastUpdate": "<PERSON>", "watchedNodesCount": "İzlən<PERSON>n d<PERSON>ü<PERSON><PERSON> sayı", "startElementIdNotFound": "Lay<PERSON><PERSON>nin başlanğıc elementi id-ləri alına bi<PERSON>", "archiveFileNotFound": "Arxiv faylı mövcud deyil", "loadArchiveError": "Arxiv yüklənərkən xəta oldu: {error}", "noArchivesFound": "Arxiv tapılmadı", "settingsSaved": "<PERSON><PERSON><PERSON> qeyd edilir", "saveSettings": "Qur<PERSON><PERSON><PERSON> qeyd et", "generalSettings": "<PERSON><PERSON><PERSON> qur<PERSON>", "audioSettings": "<PERSON><PERSON><PERSON>", "languageSettings": "<PERSON><PERSON>ı", "autoSaveProgress": "Gedişlərinizi avtomatik qeyd edin", "autoSaveDescription": "Oyun gedişatınızı avtomatik qeyd edin", "showVideoControls": "Video tənzimləmə çubuğunu göstər", "showVideoControlsDescription": "Video oynatarkən idarə çubuğunu göstər", "interfaceLanguage": "İnterface dili", "version": "versiyası", "developer": "Quraşdır", "openGameError": "<PERSON>yun açma xətası: {error}", "alreadySubscribed": "<PERSON><PERSON><PERSON>", "videoNodeLocked": "Bu video düyünü açılmayıb. <PERSON><PERSON><PERSON><PERSON><PERSON>, əvvəlki videoya ilk baxın", "likeSuccess": "<PERSON><PERSON><PERSON> kimi", "alreadyLiked": "<PERSON><PERSON><PERSON>ə<PERSON>", "likeFailed": "Xoşa gələ bilmədi", "favoriteSuccess": "Müvəffəqiyyətlə toplanıldı", "alreadyFavorited": "<PERSON><PERSON><PERSON>", "favoriteFailed": "<PERSON><PERSON><PERSON> b<PERSON>d<PERSON>", "unsubscribe": "<PERSON>n<PERSON><PERSON><PERSON> ləğv edin", "confirmUnsubscribe": "Əminsinizmi ki, abu<PERSON><PERSON><PERSON><PERSON> ləğv etmək istəyirsiniz?", "unsubscribeSuccess": "Abunəlik uğurla abunə<PERSON>z qaldı", "unsubscribeFailed": "Abunəlik ləğv edildi", "title": "ba<PERSON><PERSON><PERSON>q", "required": "<PERSON><PERSON><PERSON><PERSON><PERSON> olu<PERSON>r", "description": "təsvir", "inputDescription": "<PERSON><PERSON><PERSON> ed<PERSON>, təsvir daxil edin", "selectContentRating": "<PERSON><PERSON><PERSON>, məz<PERSON>n <PERSON> se<PERSON>in", "previewImage": "Şəkili önbaxış", "publish": "nəşr et", "titleCannotBeEmpty": "<PERSON><PERSON><PERSON><PERSON><PERSON> boş ola bi<PERSON>z", "flowChart": "axım cədvəli", "pleaseSelectProject": "<PERSON><PERSON><PERSON> ediri<PERSON>, lay<PERSON>əni əvvəlcə seçin", "fileNotFoundTitle": "<PERSON><PERSON> m<PERSON>d <PERSON>", "openFlowChartFailed": "Axış çarxı açıla bilmədi: $e", "invalidTimeFormat": "Vaxt formatı qüvvədə deyil", "startTimeBeforeEndTime": "Ba<PERSON><PERSON><PERSON><PERSON><PERSON> vaxtı son vaxtdan əvvəl olma<PERSON><PERSON>r", "endTimeNotExceedTotal": "Son vaxt videonun ümumi uzunluğunu aşa bilmir", "save": "<PERSON><PERSON><PERSON>", "playTimeSettings": "Playback ba<PERSON><PERSON>a və bitiş vaxtı qurğuları", "startTimeLabel": "Başlama vaxtı", "endTimeLabel": "Bitiş vaxtı", "hour": "vaxt", "minute": "b<PERSON><PERSON><PERSON><PERSON>", "second": "<PERSON><PERSON><PERSON>", "showVideoController": "Video tənzimləmə çubuğunu göstər", "showControllerDuringPlayback": "Video oynatarkən idarə çubuğunu göstər", "playbackSpeed": "<PERSON><PERSON><PERSON><PERSON>", "fileNotFoundWithPath": "<PERSON><PERSON> mövcud deyil: $jsonFilePath", "loadArchive": "Arxivi oxu", "viewFlowChart": "Axış çarxını bax", "switchHorizontal": "Üfüqi quruluşa keç", "switchVertical": "Şaquli quruluşa keç", "invalidProjectPath": "Lay<PERSON>ə cığırı səhvdir", "gameWindow": "<PERSON><PERSON> pəncərəsi", "projectLoaded": "Layihə yükləndi: {yol}", "projectNotFound": "<PERSON>em tapıla bi<PERSON>əyib: {yol}", "newProject": "<PERSON>ni layihə yarat", "projectExistsTitle": "<PERSON><PERSON><PERSON> onsuz da mövcuddur", "projectExistsContent": "Layihə artıq mö<PERSON>dd<PERSON>, layihədə olaca<PERSON>mı?", "confirmUpdateWorkshopItem": "Yenilənmiş Workshop items", "confirmUpdateWorkshopItemDescription": "Mövcud workshop item-ni yeniləmək istəyəcəksiniz. Bu, hazırkı versiyanı dəyişikliklərinizlə əvəz edəcək.", "updating": "Yenilənir...", "updateSuccess": "Uğuru Yeniləyin!", "updateWorkshop": "Yenilənmiş Workshop items", "noWorkshopItems": "Yeniləmək üçün Workshop əşyalarınız yoxdur", "selectWorkshopItemToUpdate": "Yeniləmək istədiyiniz Workshop əşyanı seçin", "updateWorkshopError": "İş emalatxanası xətası: {error}", "autoSaveGame": "Avtomatik saxla oyunlar", "autoSaveInterval": "Avtomatik qeyd a<PERSON>arı", "enableFlowchartCheck": "Axış çərçivəsi aşkarını fəallaşdır", "flowchartCheckDescription": "Bağlı olmayan dü<PERSON>ünlə<PERSON> və başlanğıc bağlantıları aşkar edir", "disconnectedVideoCount": "Bağlı olmayan videolarla d<PERSON>ü<PERSON> sayı: {count}", "startNodeVideoCount": "Başlanğıc nöqtəsindən sonrakı video düyünlərin sayı 1 olmalıdır, cari: {count}", "exitFullscreen": "Tam ekran çıx", "useNewWindowForEditing": "<PERSON><PERSON>ə düzəlt interfeysi yeni pəncərə açılır", "useNewWindowForEditingDescription": "Seçili halda layihə redaktoru yeni pəncərədə açılır, qeyri-fəal olduqda isə hazırkı pəncərədə açılır", "workshopItemUpdated": "\"Workshop\" lay<PERSON><PERSON><PERSON> ye<PERSON>b", "workshopItemUploaded": "\"Workshop\" lay<PERSON><PERSON><PERSON>", "uploadFailedWithColon": "Yükləmə bacarılmadı: %s ", "congratsEarnedCoins": "Təbrik edirik! 10 qızıldan mükafat al!", "preparingProjectFiles": "Lay<PERSON>ə dosyesi hazırlanır...", "preparingConfig": "Konfiqurasiyanı hazırla", "uploadingPleaseWait": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, zəhmət olma<PERSON> g<PERSON>ləyin...", "uploadingContent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "uploadingPreviewImage": "Önbaxış yukle", "committingChanges": "Dəyişik<PERSON><PERSON><PERSON><PERSON> göndər", "diskSpaceInsufficient": "Ki<PERSON>yət qədər disk yeri, Steam upload mexanizminə əsaslanaraq, proqramın yerləşdiyi diskdə kifayət qədər yer rezerv etməlisiniz. Disk yerini təmizlə və yenidən yükləməyə çalış.", "noBranchesToSet": "Hazırkı düyünün çoxlu budaqları yoxdur və budaq parametrlərini təyin etmək mümkün deyil", "normalBranch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> buda<PERSON>a", "timedBranch": "<PERSON><PERSON><PERSON> məhdud buda<PERSON>ı", "qteBranch": "QTE filialı", "timeLimit": "<PERSON><PERSON><PERSON> limiti (saniyələr)", "autoSelect": "Avtomatik seçim", "questionDescription": "İzahı", "buttonOpacity": "<PERSON><PERSON><PERSON><PERSON> şəffaflığı", "transparent": "<PERSON><PERSON><PERSON><PERSON>", "opaque": "opak", "buttonText": "<PERSON><PERSON><PERSON><PERSON><PERSON> mətn", "branchIndexLabel": "Budaq :index}", "customButtonPosition": "Düymə mövqeyini özelleştir", "screenPreview": "Ekran önbaxış", "remainingTimeLabel": "Qalan vaxt: {time} saniyə", "buttonPositionInfo": "<PERSON>f<PERSON>qi Mövqe: {horizontalPercent}%, <PERSON><PERSON><PERSON>: {verticalPercent}%", "titlePositionInfo": "Mövqe: HorizontalPercent}%, <PERSON><PERSON><PERSON>{verticalPercent}%", "saving": "<PERSON><PERSON>d <PERSON>t...", "branchSettingsSaved": "Budaq qurğuları və axın qurğuları qeyd edilib", "saveFlowchartFailed": "<PERSON><PERSON>rt qeyd edə bilmədi", "fullVideoPath": "Tam video cığırı: {yol}", "videoFileNotExist": "Video faylı mövcud deyil: {yol}", "archiveUpdatedForNode": "Hazırkı düyünün arxivi yenilənib: {nodeId}", "nodeMarkedAsWatched": "Baxılan işarəli dü<PERSON>ün: {nodeId}", "videoPlaybackError": "Video oynatma xətası: {error}", "addGlobalValue": "Ümumi dəyəri əlavə et", "editGlobalValue": "Ümumdünya dəyərlərini düzəlt", "globalValueName": "<PERSON><PERSON><PERSON><PERSON> qiymətin adı", "globalValueNameHint": "Məsələn: qızıl, sağlamlıq və s.", "initialValue": "Başlanğıc qi<PERSON>", "valueInputHint": "<PERSON><PERSON><PERSON><PERSON><PERSON>, b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> qiymətini daxil edin", "pleaseEnterValueName": "<PERSON><PERSON><PERSON> edirik, numeric name daxil edin", "editGlobalValueTitle": "Qlobal D<PERSON>: {name}", "valueLabel": "ədədi dəyər", "globalValues": "<PERSON><PERSON>bal də<PERSON>ərlər", "variableName": "<PERSON><PERSON><PERSON><PERSON><PERSON>n adı", "pleaseEnterVariableName": "<PERSON><PERSON><PERSON> ed<PERSON>, də<PERSON><PERSON><PERSON><PERSON> adı daxil edin", "variableAlreadyExists": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> adı onsuz da mövcuddur", "numberType": "rəqə<PERSON>li", "textType": "mətn", "booleanType": "Boolean", "variableType": "yaz", "addToFlowchart": "Axış çarxına əlavə et", "addVariable": "D<PERSON>yişən<PERSON><PERSON>ri əlavə et", "pleaseEnterValidNumber": "Xahiş edirik əhəmiyyətli nömrəni daxil edin", "pleaseEnterTrueOrFalse": "<PERSON>ahiş edirik həqiqi və ya yalnış daxil edin", "pleaseEnterValue": "<PERSON><PERSON><PERSON> ediri<PERSON>, numeric value daxil edin", "failedToGetWorkshopItems": "Emalatxana ə<PERSON>yaları alına bilmədi", "workshopRecommendedTitle": "Dünyanı Xilas Etmək Üçün Bələdçi", "workshopRecommendedDescription": "Mühərrik kəndinin başçısının özü tərəfindən yazılmış kəndlilərin hamısı deyirdilər ki, bu kitabça versiya yeniləməsi ilə avtomatik olaraq təkamül edəcək", "setOptionsAndValueChanges": "<PERSON>ç<PERSON><PERSON><PERSON>ri təyin edin və qiymətləri dəyişin", "selectBranchToSet": "Qur<PERSON>q istədiyiniz filialı seçin", "branchWithText": "Filial: {branchText}", "noGlobalValuesFoundAddFirst": "Əgər qlobal dəyər tapa bilmirsinizsə, əvvəlcə qlobal dəyərin idarə edilməsi üçün dəyər əlavə edin", "greaterThan": "<PERSON><PERSON>", "lessThan": "<PERSON><PERSON> az", "equalTo": "<PERSON><PERSON><PERSON>", "greaterThanOrEqual": "Böyük və ya bərabər", "lessThanOrEqual": "Az və ya bərabər", "notEqual": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "range": "aralığı", "add": "plus", "subtract": "alt-üst", "multiply": "vaxt", "divide": "Bölmək", "setTo": "Qur", "setBranchConditionsAndChanges": "\"{branchText}\" filialının vəziyyətini və numerik dəyişikliyini təyin edin.", "setAndEnableConditions": "Hazırkı variantın göründüyü şərtləri təyin edir və təmin edir", "minValue": "minimal", "to": "gəl", "maxValue": "<PERSON><PERSON><PERSON><PERSON>", "setAndEnableValueChanges": "<PERSON><PERSON><PERSON> etdi<PERSON>dən sonra qiymətin dəyişməsini təyin edin və fəallaşdırın", "savingBranchSettings": "Budaq qurğuları qeyd edilir...", "nameBranchSettingsSaved": "\"{branchText}\" filialının vəziyyəti və dəyər dəyişikliyi qurğuları qeyd edilib", "generalBranchSettingsSaved": "Budaq qurğuları və axın qurğuları qeyd edilib", "saveFailed": "<PERSON><PERSON>d edə <PERSON>, <PERSON><PERSON><PERSON>, qey<PERSON><PERSON><PERSON> yox<PERSON>n", "apply": "tətbiq edin", "buttonDisplayTime": "vaxtı göstərmək üçün düymə", "buttonDisplayTimeDescription": "Videonun sonuna neçə saniyə qalmış budaq dü<PERSON>ə<PERSON> g<PERSON>ə<PERSON>r", "buttonDisplayTimeNote": "Bu parametr 0-a təyin edildi<PERSON>, budaqlar video oynatma sonunda nümayiş etdirilir", "seconds": "<PERSON><PERSON><PERSON>", "buttonTextOnly": "<PERSON>ə<PERSON>ni yalnız gö<PERSON>ər (button arxa planı yoxdur)", "qteDuration": "QTE düsturu müddəti", "qteDurationDescription": "Oyunçular bu müddət ərzində reaksiya göstərməlidirlər", "qteSuccessBranch": "QTE Müvəffəqiyyətli Filial", "qteFailBranch": "QTE iflas etdi", "qteButtonPosition": "QTE düymə mövqeyi", "qteButtonDurationSeconds": "<PERSON><PERSON><PERSON>ə göstərmə vaxtı: {duration} saniyə", "qteButtonDisplayTime": "QTE\n{müddət} saniyə", "qteSuccessLabel": "<PERSON><PERSON><PERSON>an", "qteFailLabel": "bacarılmadı", "qtePositionInfo": "QTE düymə tutumu: {horizontalPercent}%, {verticalPercent}%", "enableVideoClickPause": "Videonu oynayarkən fasilə etmək üçün tap", "enableVideoClickPauseDesc": "Bir dəfə, play/pause vəzi<PERSON><PERSON>ə<PERSON>i keçmək üçün video sahəsinə toxunun"}