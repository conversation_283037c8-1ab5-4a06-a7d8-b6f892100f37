// ignore_for_file: public_member_api_docs, always_specify_types, avoid_positional_boolean_parameters, avoid_classes_with_only_static_members
import "dart:ffi";

import "../enums/eresult.dart";
import "../typedefs.dart";

@Packed(4)
final class CreateBeaconCallback extends Struct {
  static int get callbackId => 5302;

  @Int32()
  external EResultAliasDart result;

  @UnsignedLongLong()
  external PartyBeaconId beaconId;
}

extension CreateBeaconCallbackExtensions on Pointer<CreateBeaconCallback> {
  EResult get result => EResult.fromValue(ref.result);

  PartyBeaconId get beaconId => ref.beaconId;
}
