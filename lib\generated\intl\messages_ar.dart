// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ar locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ar';

  static String m0(nodeId) => "أرشيف العقدة الحالي المحدث: ${nodeId}";

  static String m1(index) => "فرع {فهرس}:";

  static String m2(branchText) => "الفرع: ${branchText}";

  static String m3(horizontalPercent, verticalPercent) =>
      "الوضع الأفقي: {أفقي في النسبة المئوية}٪ ، الوضع الرأسي: ${verticalPercent}٪";

  static String m4(count) =>
      "عدد العقد التي تحتوي على فيديوهات غير متصلة: ${count}";

  static String m5(name) => "تحرير القيمة العالمية: ${name}";

  static String m6(error) => "خطأ في تحميل المخطط الانسيابي: ${error}";

  static String m7(path) => "مسار الفيديو الكامل: ${path}";

  static String m8(error) => "خطأ في تحميل الأرشيف: ${error}";

  static String m9(branchText) =>
      "يتم حفظ إعدادات تغيير الشرط والقيمة للفرع \"${branchText}\"";

  static String m10(nodeId) =>
      "العقدة التي تم وضع علامة عليها كما تم عرضها: ${nodeId}";

  static String m11(error) => "خطأ في فتح مشروع اللعبة: ${error}";

  static String m12(path) => "تم تحميل المشروع: ${path}";

  static String m13(path) => "لم يتم العثور على العنصر: ${path}";

  static String m14(duration) => "كيو تي إي\n{المدة} ثواني";

  static String m15(duration) => "وقت عرض الزر: {المدة} ثواني";

  static String m16(horizontalPercent, verticalPercent) =>
      "موضع زر QTE: {أفقي في النسبة المئوية}٪ ، ${verticalPercent}٪";

  static String m17(time) => "الوقت المتبقي: {الوقت} ثواني";

  static String m18(branchText) =>
      "قم بتعيين الشرط والتغيير العددي للفرع \"${branchText}\".";

  static String m19(count) =>
      "يجب أن يكون عدد عقد الفيديو بعد نقطة البداية 1 ، الحالي: ${count}";

  static String m20(horizontalPercent, verticalPercent) =>
      "الموقع: HorizontalPercent}٪, عمودي${verticalPercent}٪";

  static String m21(error) => "خطأ ورشة عمل التحديث: ${error}";

  static String m22(path) => "ملف الفيديو غير موجود: ${path}";

  static String m23(error) => "خطأ في تشغيل الفيديو: ${error}";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "AncientChinese": MessageLookupByLibrary.simpleMessage(
      "النمط الصيني القديم",
    ),
    "Anime": MessageLookupByLibrary.simpleMessage("أسلوب أنيمي"),
    "Everyone": MessageLookupByLibrary.simpleMessage("كلهم"),
    "Landscape": MessageLookupByLibrary.simpleMessage("مستطيل عرضي"),
    "NSFW": MessageLookupByLibrary.simpleMessage("الأماكن غير العامة"),
    "Other": MessageLookupByLibrary.simpleMessage("أنماط أخرى"),
    "Pixel": MessageLookupByLibrary.simpleMessage("نمط البكسل"),
    "Portrait": MessageLookupByLibrary.simpleMessage("مستطيل طولي"),
    "Realistic": MessageLookupByLibrary.simpleMessage("أسلوب واقعي"),
    "SearchWorkshop": MessageLookupByLibrary.simpleMessage(
      "ابحث عنه في ورشة العمل",
    ),
    "Square": MessageLookupByLibrary.simpleMessage("مربع"),
    "Subscribe": MessageLookupByLibrary.simpleMessage("اشترك"),
    "SubscribeAndDownload": MessageLookupByLibrary.simpleMessage(
      "اشترك وقم بالتنزيل",
    ),
    "Subscribed": MessageLookupByLibrary.simpleMessage("مشترك"),
    "Unsubscribed": MessageLookupByLibrary.simpleMessage("غير مشترك"),
    "about": MessageLookupByLibrary.simpleMessage("بخصوص"),
    "achievementDisplay": MessageLookupByLibrary.simpleMessage("عرض النتائج"),
    "achievements": MessageLookupByLibrary.simpleMessage("در"),
    "activity": MessageLookupByLibrary.simpleMessage("دينامي"),
    "add": MessageLookupByLibrary.simpleMessage("زائد"),
    "addDiamond": MessageLookupByLibrary.simpleMessage("أضف ماسة"),
    "addGlobalValue": MessageLookupByLibrary.simpleMessage("إضافة قيمة عالمية"),
    "addHexagon": MessageLookupByLibrary.simpleMessage("أضف مسدس"),
    "addImage": MessageLookupByLibrary.simpleMessage("إضافة صورة"),
    "addOval": MessageLookupByLibrary.simpleMessage("أضف القطع الناقص"),
    "addParallelogram": MessageLookupByLibrary.simpleMessage(
      "إضافة متوازي الأضلاع",
    ),
    "addRectangle": MessageLookupByLibrary.simpleMessage("أضف مستطيلا"),
    "addResizableRectangle": MessageLookupByLibrary.simpleMessage(
      "إضافة مستطيل متغير",
    ),
    "addStorage": MessageLookupByLibrary.simpleMessage("إضافة مساحة تخزين"),
    "addToFlowchart": MessageLookupByLibrary.simpleMessage(
      "أضف إلى المخطط الانسيابي",
    ),
    "addVariable": MessageLookupByLibrary.simpleMessage("إضافة متغيرات"),
    "addVideo": MessageLookupByLibrary.simpleMessage("إضافة فيديو"),
    "adultAgreementContent": MessageLookupByLibrary.simpleMessage(
      "تحتوي ورشة Steam على محتوى تم إنشاؤه بشكل مشترك من لاعبين من جميع أنحاء العالم ، والذي قد يتضمن محتوى غير مناسب للعرض في الأماكن العامة.",
    ),
    "adultAgreementTitle": MessageLookupByLibrary.simpleMessage(
      "يرجى التأكد من أن عمرك لا يقل عن 18 عاما",
    ),
    "ageRating": MessageLookupByLibrary.simpleMessage("التصنيفات العمرية"),
    "allSettings": MessageLookupByLibrary.simpleMessage("جميع الإعدادات"),
    "alreadyFavorited": MessageLookupByLibrary.simpleMessage(
      "تم وضع إشارة مرجعية عليها بالفعل",
    ),
    "alreadyLiked": MessageLookupByLibrary.simpleMessage(
      "لقد تم الإعجاب بالفعل",
    ),
    "alreadySubscribed": MessageLookupByLibrary.simpleMessage("مشترك"),
    "apply": MessageLookupByLibrary.simpleMessage("طبق"),
    "archive": MessageLookupByLibrary.simpleMessage("أرشيف"),
    "archiveFileNotFound": MessageLookupByLibrary.simpleMessage(
      "ملف الأرشيف غير موجود",
    ),
    "archiveUpdatedForNode": m0,
    "audioSettings": MessageLookupByLibrary.simpleMessage("إعدادات الصوت"),
    "audioTrack": MessageLookupByLibrary.simpleMessage("المسار"),
    "autoFullScreenVideo": MessageLookupByLibrary.simpleMessage(
      "ملء الشاشة تلقائيا عند تشغيل مقطع فيديو",
    ),
    "autoSaveDescription": MessageLookupByLibrary.simpleMessage(
      "احفظ تقدم اللعبة تلقائيا",
    ),
    "autoSaveGame": MessageLookupByLibrary.simpleMessage(
      "الحفظ التلقائي للألعاب",
    ),
    "autoSaveInterval": MessageLookupByLibrary.simpleMessage(
      "فواصل الحفظ التلقائي",
    ),
    "autoSaveProgress": MessageLookupByLibrary.simpleMessage(
      "حفظ تقدمك تلقائيا",
    ),
    "autoSelect": MessageLookupByLibrary.simpleMessage("الاختيار التلقائي"),
    "back": MessageLookupByLibrary.simpleMessage("أعاد"),
    "backgroundColor": MessageLookupByLibrary.simpleMessage("لون الخلفية"),
    "booleanType": MessageLookupByLibrary.simpleMessage("منطقيه"),
    "borderColor": MessageLookupByLibrary.simpleMessage("لون الحدود"),
    "branchIndexLabel": m1,
    "branchSettingsSaved": MessageLookupByLibrary.simpleMessage(
      "تم حفظ إعدادات الفرع والمخططات الانسيابية",
    ),
    "branchWithText": m2,
    "buttonDisplayTime": MessageLookupByLibrary.simpleMessage("لعرض الوقت"),
    "buttonDisplayTimeDescription": MessageLookupByLibrary.simpleMessage(
      "كم ثانية قبل نهاية الفيديو يظهر زر الفرع",
    ),
    "buttonDisplayTimeNote": MessageLookupByLibrary.simpleMessage(
      "عند تعيين هذه المعلمة إلى 0 ، يتم عرض الفروع في نهاية تشغيل الفيديو",
    ),
    "buttonOpacity": MessageLookupByLibrary.simpleMessage("زر الشفافية"),
    "buttonPositionInfo": m3,
    "buttonText": MessageLookupByLibrary.simpleMessage("نص الزر"),
    "buttonTextOnly": MessageLookupByLibrary.simpleMessage(
      "إظهار النص فقط (بدون خلفية زر)",
    ),
    "cancel": MessageLookupByLibrary.simpleMessage("إلغاء الأمر"),
    "captureCurrentFrame": MessageLookupByLibrary.simpleMessage(
      "يلتقط الإطار الحالي",
    ),
    "challenge": MessageLookupByLibrary.simpleMessage("طعن"),
    "challengeName": MessageLookupByLibrary.simpleMessage("اسم التحدي"),
    "clickToSubscribeAndDownload": MessageLookupByLibrary.simpleMessage(
      "انقر فوق اشتراك لبدء التنزيل",
    ),
    "close": MessageLookupByLibrary.simpleMessage("إيقاف التشغيل"),
    "closeSubtitle": MessageLookupByLibrary.simpleMessage(
      "إيقاف تشغيل الترجمة",
    ),
    "committingChanges": MessageLookupByLibrary.simpleMessage(
      "تنفيذ التغييرات",
    ),
    "completed": MessageLookupByLibrary.simpleMessage("سواء تم ذلك أم لا"),
    "confirm": MessageLookupByLibrary.simpleMessage("أكد"),
    "confirmExitApp": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد من أنك تريد إنهاء التطبيق؟",
    ),
    "confirmUnsubscribe": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد من أنك تريد إلغاء اشتراكك؟",
    ),
    "confirmUpdateWorkshopItem": MessageLookupByLibrary.simpleMessage(
      "عناصر ورشة العمل المحدثة",
    ),
    "confirmUpdateWorkshopItemDescription": MessageLookupByLibrary.simpleMessage(
      "ستحتاج إلى تحديث عنصر ورشة عمل موجود. سيؤدي هذا إلى استبدال الإصدار الحالي بالتغييرات التي أجريتها.",
    ),
    "congratsEarnedCoins": MessageLookupByLibrary.simpleMessage(
      "مبروك! احصل على مكافأة قدرها 10 ذهبات!",
    ),
    "coverLoadFailed": MessageLookupByLibrary.simpleMessage(
      "فشل تحميل صفحة الغلاف",
    ),
    "createChallenge": MessageLookupByLibrary.simpleMessage("خلق تحد"),
    "createNewGame": MessageLookupByLibrary.simpleMessage("إنشاء لعبة"),
    "createTime": MessageLookupByLibrary.simpleMessage("وقت الإنشاء"),
    "credits": MessageLookupByLibrary.simpleMessage("قائمة الاعتمادات"),
    "creditsAnimationDesigner": MessageLookupByLibrary.simpleMessage(
      "مصمم الرسوم المتحركة",
    ),
    "creditsArtDesigner": MessageLookupByLibrary.simpleMessage("مصمم فني"),
    "creditsCodeWriter": MessageLookupByLibrary.simpleMessage(
      "كتابة التعليمات البرمجية",
    ),
    "creditsCopywriter": MessageLookupByLibrary.simpleMessage("كوبيوريتنغ"),
    "creditsCreativeSource": MessageLookupByLibrary.simpleMessage(
      "مصادر الأفكار",
    ),
    "creditsCreativeTitle": MessageLookupByLibrary.simpleMessage(
      "الإبداع والتخطيط",
    ),
    "creditsGameProducer": MessageLookupByLibrary.simpleMessage("منتج اللعبة"),
    "creditsGameplayPlanner": MessageLookupByLibrary.simpleMessage(
      "تخطيط اللعب",
    ),
    "creditsMarketingTitle": MessageLookupByLibrary.simpleMessage(
      "الدعاية للسوق",
    ),
    "creditsProgrammingTitle": MessageLookupByLibrary.simpleMessage(
      "تطوير البرنامج",
    ),
    "creditsSoftwareArchitect": MessageLookupByLibrary.simpleMessage(
      "بنية البرمجيات",
    ),
    "creditsSoftwarePlanner": MessageLookupByLibrary.simpleMessage(
      "تخطيط البرامج",
    ),
    "creditsSpecialThanks": MessageLookupByLibrary.simpleMessage("شكر خاص لك"),
    "creditsSubtitle": MessageLookupByLibrary.simpleMessage(
      "اطلع على الفرق والمساهمين المشاركين في تطوير البرمجيات",
    ),
    "creditsThanksTitle": MessageLookupByLibrary.simpleMessage(
      "شكرا لك على لعب لعبتي!",
    ),
    "creditsVideoProducer": MessageLookupByLibrary.simpleMessage(
      "إنتاج الفيديو",
    ),
    "creditsVisualTitle": MessageLookupByLibrary.simpleMessage(
      "التصميم المرئي",
    ),
    "currentPosition": MessageLookupByLibrary.simpleMessage("أنت هنا"),
    "curved": MessageLookupByLibrary.simpleMessage("حجن"),
    "customButtonPosition": MessageLookupByLibrary.simpleMessage(
      "تخصيص موضع الزر",
    ),
    "delete": MessageLookupByLibrary.simpleMessage("حذف"),
    "description": MessageLookupByLibrary.simpleMessage("وصف"),
    "developer": MessageLookupByLibrary.simpleMessage("المطورين"),
    "diamondNode": MessageLookupByLibrary.simpleMessage("العقد الماسية"),
    "disconnectedVideoCount": m4,
    "diskSpaceInsufficient": MessageLookupByLibrary.simpleMessage(
      "مساحة القرص غير كافية ، بناء على آلية تحميل Steam ، تحتاج إلى حجز مساحة كافية على القرص حيث يوجد البرنامج. قم بإخلاء مساحة القرص وحاول التحميل مرة أخرى.",
    ),
    "divide": MessageLookupByLibrary.simpleMessage("تقسيم"),
    "download": MessageLookupByLibrary.simpleMessage("تحميل"),
    "downloadFailed": MessageLookupByLibrary.simpleMessage("فشل التنزيل"),
    "downloadSuccess": MessageLookupByLibrary.simpleMessage(
      "كان التنزيل ناجحا",
    ),
    "downloadWorkshop": MessageLookupByLibrary.simpleMessage(
      "قم بتنزيل ورشة العمل",
    ),
    "downloading": MessageLookupByLibrary.simpleMessage("تحميل"),
    "downloadingPleaseWait": MessageLookupByLibrary.simpleMessage(
      "تنزيل ، يرجى لاحقا",
    ),
    "downloads": MessageLookupByLibrary.simpleMessage("تحميل"),
    "duration": MessageLookupByLibrary.simpleMessage("المدة الإجمالية"),
    "editGlobalValue": MessageLookupByLibrary.simpleMessage(
      "تحرير القيم العمومية",
    ),
    "editGlobalValueTitle": m5,
    "elementId": MessageLookupByLibrary.simpleMessage("معرف"),
    "elementParams": MessageLookupByLibrary.simpleMessage("معلمات العنصر"),
    "elementText": MessageLookupByLibrary.simpleMessage("نص"),
    "elementType": MessageLookupByLibrary.simpleMessage("نوع"),
    "elevation": MessageLookupByLibrary.simpleMessage("ارتفاع"),
    "enableFlowchartCheck": MessageLookupByLibrary.simpleMessage(
      "تمكين اكتشاف المخطط الانسيابي",
    ),
    "enableVideoClickPause": MessageLookupByLibrary.simpleMessage(
      "انقر لإيقاف الفيديو مؤقتا أثناء تشغيله",
    ),
    "enableVideoClickPauseDesc": MessageLookupByLibrary.simpleMessage(
      "بمجرد التشغيل ، انقر فوق منطقة الفيديو لتبديل حالة التشغيل / الإيقاف المؤقت",
    ),
    "endTime": MessageLookupByLibrary.simpleMessage(
      "وصل إلى نهاية الطريق\nالوقت",
    ),
    "endTimeLabel": MessageLookupByLibrary.simpleMessage("وقت الانتهاء"),
    "endTimeNotExceedTotal": MessageLookupByLibrary.simpleMessage(
      "لا يمكن أن يتجاوز وقت الانتهاء الطول الإجمالي للفيديو",
    ),
    "enterProjectName": MessageLookupByLibrary.simpleMessage(
      "أدخل اسما للمشروع",
    ),
    "equalTo": MessageLookupByLibrary.simpleMessage("مبلغ"),
    "exit": MessageLookupByLibrary.simpleMessage("الخروج من اللعبة"),
    "exitFullscreen": MessageLookupByLibrary.simpleMessage(
      "الخروج من وضع ملء الشاشة",
    ),
    "failedToGetWorkshopItems": MessageLookupByLibrary.simpleMessage(
      "فشل في الحصول على عناصر ورشة العمل",
    ),
    "favoriteFailed": MessageLookupByLibrary.simpleMessage(
      "فشلت الإشارة المرجعية",
    ),
    "favoriteSuccess": MessageLookupByLibrary.simpleMessage("تم جمعها بنجاح"),
    "fileNotFound": MessageLookupByLibrary.simpleMessage("الملف غير موجود"),
    "fileNotFoundTitle": MessageLookupByLibrary.simpleMessage(
      "الملف غير موجود",
    ),
    "fileNotFoundWithPath": MessageLookupByLibrary.simpleMessage(
      "الملف غير موجود: \$jsonFilePath",
    ),
    "filePreparingPleaseRetry": MessageLookupByLibrary.simpleMessage(
      "يتم إعداد الملف ، يرجى المحاولة مرة أخرى لاحقا",
    ),
    "flowChart": MessageLookupByLibrary.simpleMessage("مخطط التدفق"),
    "flowchart": MessageLookupByLibrary.simpleMessage("مراجعة العملية"),
    "flowchartCheckDescription": MessageLookupByLibrary.simpleMessage(
      "يكتشف العقد غير المتصلة والاتصالات الأصلية",
    ),
    "flowchartFileNotFound": MessageLookupByLibrary.simpleMessage(
      "تعذر العثور على FLOWCHART.json الملف",
    ),
    "flowchartLoadError": m6,
    "flowchartMissingStart": MessageLookupByLibrary.simpleMessage(
      "يفتقد المخطط الانسيابي إلى عنصر نقطة البداية",
    ),
    "friends": MessageLookupByLibrary.simpleMessage("اصدقاء"),
    "friendsonly": MessageLookupByLibrary.simpleMessage("مرئي للأصدقاء"),
    "fullVideoPath": m7,
    "fullscreen": MessageLookupByLibrary.simpleMessage("وضع ملء الشاشة"),
    "gallery": MessageLookupByLibrary.simpleMessage("معرض"),
    "gameTitle": MessageLookupByLibrary.simpleMessage(
      "محرك ألعاب الفيديو التفاعلي",
    ),
    "gameWindow": MessageLookupByLibrary.simpleMessage("نافذة اللعبة"),
    "games": MessageLookupByLibrary.simpleMessage("لعب"),
    "generalBranchSettingsSaved": MessageLookupByLibrary.simpleMessage(
      "تم حفظ إعدادات الفرع والمخططات الانسيابية",
    ),
    "generalSettings": MessageLookupByLibrary.simpleMessage("الإعدادات العامة"),
    "globalValueName": MessageLookupByLibrary.simpleMessage(
      "اسم القيمة الرقمية",
    ),
    "globalValueNameHint": MessageLookupByLibrary.simpleMessage(
      "على سبيل المثال: الذهب ، الصحة ، إلخ",
    ),
    "globalValues": MessageLookupByLibrary.simpleMessage("القيم العالمية"),
    "greaterThan": MessageLookupByLibrary.simpleMessage("اكبر"),
    "greaterThanOrEqual": MessageLookupByLibrary.simpleMessage(
      "أكبر من أو يساوي",
    ),
    "hexagonNode": MessageLookupByLibrary.simpleMessage("العقد السداسية"),
    "hideController": MessageLookupByLibrary.simpleMessage("إخفاء شريط التحكم"),
    "home": MessageLookupByLibrary.simpleMessage("صفحة رئيسية"),
    "hour": MessageLookupByLibrary.simpleMessage("الوقت"),
    "ilpDesc": MessageLookupByLibrary.simpleMessage("وصف"),
    "ilpEditor": MessageLookupByLibrary.simpleMessage("محرر الألعاب"),
    "imageNode": MessageLookupByLibrary.simpleMessage("عقد الصورة"),
    "importImage": MessageLookupByLibrary.simpleMessage("استيراد الصور"),
    "initialValue": MessageLookupByLibrary.simpleMessage("القيمة الأولية"),
    "inputDescription": MessageLookupByLibrary.simpleMessage(
      "الرجاء إدخال وصف",
    ),
    "interfaceLanguage": MessageLookupByLibrary.simpleMessage("لغة الواجهة"),
    "invalidEndTime": MessageLookupByLibrary.simpleMessage(
      "وقت الانتهاء غير صالح",
    ),
    "invalidFileType": MessageLookupByLibrary.simpleMessage(
      "نوع الملف غير صالح",
    ),
    "invalidProjectPath": MessageLookupByLibrary.simpleMessage(
      "مسار المشروع غير صالح",
    ),
    "invalidStartTime": MessageLookupByLibrary.simpleMessage(
      "وقت البدء غير صالح",
    ),
    "invalidTimeFormat": MessageLookupByLibrary.simpleMessage(
      "تنسيق الوقت غير صالح",
    ),
    "itemNotSubscribed": MessageLookupByLibrary.simpleMessage(
      "سيتم تنزيل العناصر غير المشتبة فيها تلقائيا بعد الاشتراك",
    ),
    "joinDiscord": MessageLookupByLibrary.simpleMessage(
      "انضم إلى Discord لمناقشات اللعب",
    ),
    "language": MessageLookupByLibrary.simpleMessage("إعدادات اللغة"),
    "languageSettings": MessageLookupByLibrary.simpleMessage("إعدادات اللغة"),
    "lastUpdate": MessageLookupByLibrary.simpleMessage("آخر تحديث"),
    "lessThan": MessageLookupByLibrary.simpleMessage("اقل"),
    "lessThanOrEqual": MessageLookupByLibrary.simpleMessage("أقل من أو يساوي"),
    "likeFailed": MessageLookupByLibrary.simpleMessage("فشل في الإعجاب"),
    "likeSuccess": MessageLookupByLibrary.simpleMessage("مثل النجاح"),
    "loadArchive": MessageLookupByLibrary.simpleMessage("قراءة الأرشيف"),
    "loadArchiveError": m8,
    "loadFlowchart": MessageLookupByLibrary.simpleMessage(
      "قراءة المخطط الانسيابي",
    ),
    "loadingFailed": MessageLookupByLibrary.simpleMessage("فشل التحميل"),
    "loadingFailedRetry": MessageLookupByLibrary.simpleMessage(
      "إذا تعذر التحميل، انقر على إعادة المحاولة",
    ),
    "mainWindowFullscreen": MessageLookupByLibrary.simpleMessage(
      "النافذة الرئيسية بملء الشاشة",
    ),
    "maxValue": MessageLookupByLibrary.simpleMessage("الحد الاقصي"),
    "messages": MessageLookupByLibrary.simpleMessage("رسالة"),
    "minValue": MessageLookupByLibrary.simpleMessage("الحد الادني"),
    "minute": MessageLookupByLibrary.simpleMessage("تقسيم"),
    "modifyChapterTitle": MessageLookupByLibrary.simpleMessage(
      "تعديل عنوان القسم",
    ),
    "modifyTimeAndCover": MessageLookupByLibrary.simpleMessage(
      "تعديل الوقت باستخدام الغطاء",
    ),
    "multiply": MessageLookupByLibrary.simpleMessage("تايمز"),
    "myGames": MessageLookupByLibrary.simpleMessage("لعبتي"),
    "nameBranchSettingsSaved": m9,
    "newProject": MessageLookupByLibrary.simpleMessage("إنشاء مشروع جديد"),
    "newWindowFullScreen": MessageLookupByLibrary.simpleMessage(
      "النافذة الجديدة ملء الشاشة",
    ),
    "newWindowFullScreenDescription": MessageLookupByLibrary.simpleMessage(
      "الضبط تلقائيا على وضع ملء الشاشة عند فتح نافذة جديدة",
    ),
    "news": MessageLookupByLibrary.simpleMessage("خبر"),
    "no": MessageLookupByLibrary.simpleMessage("لا ، أنت لا تفعل ذلك"),
    "noArchivesFound": MessageLookupByLibrary.simpleMessage(
      "لم يتم العثور على أرشيف",
    ),
    "noAudioTrack": MessageLookupByLibrary.simpleMessage(
      "لا توجد مسارات صوتية",
    ),
    "noBranchesToSet": MessageLookupByLibrary.simpleMessage(
      "لا تحتوي العقدة الحالية على فروع متعددة ، ولا يمكن تعيين معلمات الفرع",
    ),
    "noGlobalValuesFoundAddFirst": MessageLookupByLibrary.simpleMessage(
      "إذا لم تتمكن من العثور على قيمة عمومية، أضف قيمة إلى إدارة القيمة العمومية أولا",
    ),
    "noSubtitle": MessageLookupByLibrary.simpleMessage("لا توجد ترجمات"),
    "noValidFilesFound": MessageLookupByLibrary.simpleMessage(
      "لم يتم العثور على ملفات تحميل صالحة، تأكد من أن مشروعك يتضمن أنواع الملفات المتوافقة فقط",
    ),
    "noWorkshopItems": MessageLookupByLibrary.simpleMessage(
      "ليس لديك عناصر ورشة عمل لتحديثها",
    ),
    "nodeDetails": MessageLookupByLibrary.simpleMessage("تفاصيل العقدة"),
    "nodeMarkedAsWatched": m10,
    "normalBranch": MessageLookupByLibrary.simpleMessage("التفرع المنتظم"),
    "notEqual": MessageLookupByLibrary.simpleMessage("غير متساوية"),
    "numberType": MessageLookupByLibrary.simpleMessage("ارقام"),
    "opaque": MessageLookupByLibrary.simpleMessage("معتم"),
    "openFlowChartFailed": MessageLookupByLibrary.simpleMessage(
      "فشل فتح المخطط الانسيابي: \$e",
    ),
    "openGame": MessageLookupByLibrary.simpleMessage("افتح اللعبة"),
    "openGameError": m11,
    "openGameInNewWindow": MessageLookupByLibrary.simpleMessage(
      "افتح مشروع اللعبة في نافذة جديدة",
    ),
    "openGameInNewWindowDesc": MessageLookupByLibrary.simpleMessage(
      "عند التمكين، يفتح مشروع اللعبة في نافذة جديدة، وعند تعطيله، يتم فتحه في النافذة الحالية",
    ),
    "openGameTitle": MessageLookupByLibrary.simpleMessage("افتح اللعبة"),
    "ovalNode": MessageLookupByLibrary.simpleMessage("العقد القطع الناقصة"),
    "parallelogramNode": MessageLookupByLibrary.simpleMessage(
      "عقد متوازي الأضلاع",
    ),
    "pause": MessageLookupByLibrary.simpleMessage("المهلة"),
    "play": MessageLookupByLibrary.simpleMessage("لعب"),
    "playPause": MessageLookupByLibrary.simpleMessage("تشغيل / إيقاف مؤقت"),
    "playTimeSettings": MessageLookupByLibrary.simpleMessage(
      "إعدادات بدء التشغيل ووقت الانتهاء",
    ),
    "playbackControl": MessageLookupByLibrary.simpleMessage(
      "عناصر التحكم في التشغيل",
    ),
    "playbackProgress": MessageLookupByLibrary.simpleMessage("تقدم التشغيل"),
    "playbackRate": MessageLookupByLibrary.simpleMessage("معدل التشغيل"),
    "playbackSpeed": MessageLookupByLibrary.simpleMessage("سرعة التشغيل"),
    "playbackStatus": MessageLookupByLibrary.simpleMessage("حالة التشغيل"),
    "player": MessageLookupByLibrary.simpleMessage("بحر"),
    "pleaseEnterTrueOrFalse": MessageLookupByLibrary.simpleMessage(
      "الرجاء إدخال صواب أو خطأ",
    ),
    "pleaseEnterValidNumber": MessageLookupByLibrary.simpleMessage(
      "الرجاء إدخال رقم كبير",
    ),
    "pleaseEnterValue": MessageLookupByLibrary.simpleMessage(
      "الرجاء إدخال قيمة رقمية",
    ),
    "pleaseEnterValueName": MessageLookupByLibrary.simpleMessage(
      "الرجاء إدخال اسم رقمي",
    ),
    "pleaseEnterVariableName": MessageLookupByLibrary.simpleMessage(
      "الرجاء إدخال اسم متغير",
    ),
    "pleaseSelectProject": MessageLookupByLibrary.simpleMessage(
      "يرجى تحديد المشروع أولا",
    ),
    "popularGames": MessageLookupByLibrary.simpleMessage("الألعاب الشعبية"),
    "preparingConfig": MessageLookupByLibrary.simpleMessage("إعداد التكوين"),
    "preparingProjectFiles": MessageLookupByLibrary.simpleMessage(
      "إعداد ملف المشروع...",
    ),
    "previewImage": MessageLookupByLibrary.simpleMessage("معاينة الصورة"),
    "previewImageDefault": MessageLookupByLibrary.simpleMessage("افتراضي"),
    "private": MessageLookupByLibrary.simpleMessage("عسكري"),
    "profile": MessageLookupByLibrary.simpleMessage("البيانات الشخصية"),
    "projectCreated": MessageLookupByLibrary.simpleMessage("تم إنشاء المشروع"),
    "projectExistsContent": MessageLookupByLibrary.simpleMessage(
      "المشروع موجود بالفعل ، هل سيكون في المشروع؟",
    ),
    "projectExistsTitle": MessageLookupByLibrary.simpleMessage(
      "المشروع موجود بالفعل",
    ),
    "projectLoaded": m12,
    "projectNameHint": MessageLookupByLibrary.simpleMessage(
      "الرجاء إدخال اسم المشروع",
    ),
    "projectNotFound": m13,
    "public": MessageLookupByLibrary.simpleMessage("علني"),
    "publicMode": MessageLookupByLibrary.simpleMessage(
      "وضع المكان العام / الوضع المباشر",
    ),
    "publicModeDesc": MessageLookupByLibrary.simpleMessage(
      "عند التمكين، ستفرض واجهة ورشة العمل استخدام علامات الجميع ولن تعرض علامات غير مكان العمل",
    ),
    "publish": MessageLookupByLibrary.simpleMessage("نشر"),
    "publishTime": MessageLookupByLibrary.simpleMessage("تاريخ الإصدار"),
    "puzzleHint": MessageLookupByLibrary.simpleMessage(
      "تم تقليل إجابة واحدة خاطئة",
    ),
    "qteBranch": MessageLookupByLibrary.simpleMessage("فرع QTE"),
    "qteButtonDisplayTime": m14,
    "qteButtonDurationSeconds": m15,
    "qteButtonPosition": MessageLookupByLibrary.simpleMessage("موضع زر QTE"),
    "qteDuration": MessageLookupByLibrary.simpleMessage("مدة زر QTE"),
    "qteDurationDescription": MessageLookupByLibrary.simpleMessage(
      "يحتاج اللاعبون إلى الرد خلال هذا الوقت",
    ),
    "qteFailBranch": MessageLookupByLibrary.simpleMessage("فرع QTE الفاشل"),
    "qteFailLabel": MessageLookupByLibrary.simpleMessage("استعصى"),
    "qtePositionInfo": m16,
    "qteSuccessBranch": MessageLookupByLibrary.simpleMessage("فرع QTE الناجح"),
    "qteSuccessLabel": MessageLookupByLibrary.simpleMessage("نجح"),
    "questionDescription": MessageLookupByLibrary.simpleMessage("وصف المشكلة"),
    "range": MessageLookupByLibrary.simpleMessage("نطاق"),
    "rate": MessageLookupByLibrary.simpleMessage("معدل التشغيل"),
    "recentlyEdited": MessageLookupByLibrary.simpleMessage("تم التحرير مؤخرا"),
    "recentlyPlayed": MessageLookupByLibrary.simpleMessage("لعبت مؤخرا"),
    "rectangleNode": MessageLookupByLibrary.simpleMessage("العقد المستطيلة"),
    "rectangular": MessageLookupByLibrary.simpleMessage("مستطيل"),
    "remainingTimeLabel": m17,
    "remoteReservedWord": MessageLookupByLibrary.simpleMessage(
      "Remote محجوز للبرنامج ، يرجى تغيير اسم المشروع",
    ),
    "removeAll": MessageLookupByLibrary.simpleMessage("إزالة الكل"),
    "removeAllConnections": MessageLookupByLibrary.simpleMessage(
      "إزالة جميع الاتصالات",
    ),
    "removeImage": MessageLookupByLibrary.simpleMessage("إزالة الصورة"),
    "required": MessageLookupByLibrary.simpleMessage("مطلوب"),
    "resume": MessageLookupByLibrary.simpleMessage("ارجع واستمر في اللعب"),
    "retry": MessageLookupByLibrary.simpleMessage("اعاده المحاوله"),
    "retryLoading": MessageLookupByLibrary.simpleMessage(
      "انقر على محاولة مرة أخرى",
    ),
    "save": MessageLookupByLibrary.simpleMessage("أنقذ"),
    "saveFailed": MessageLookupByLibrary.simpleMessage(
      "فشل الحفظ ، يرجى التحقق من السجلات",
    ),
    "saveFlowchart": MessageLookupByLibrary.simpleMessage(
      "حفظ المخطط الانسيابي",
    ),
    "saveFlowchartFailed": MessageLookupByLibrary.simpleMessage(
      "فشل حفظ المخطط الانسيابي",
    ),
    "saveSettings": MessageLookupByLibrary.simpleMessage("احفظ الإعدادات"),
    "saving": MessageLookupByLibrary.simpleMessage("أنقذ..."),
    "savingBranchSettings": MessageLookupByLibrary.simpleMessage(
      "حفظ إعدادات الفرع ...",
    ),
    "screenPreview": MessageLookupByLibrary.simpleMessage("معاينة الشاشة"),
    "screenshots": MessageLookupByLibrary.simpleMessage("لقطة شاشة"),
    "search": MessageLookupByLibrary.simpleMessage("بحث"),
    "second": MessageLookupByLibrary.simpleMessage("ثان"),
    "seconds": MessageLookupByLibrary.simpleMessage("ثان"),
    "seed": MessageLookupByLibrary.simpleMessage("بذرة"),
    "segmented": MessageLookupByLibrary.simpleMessage("المستوى الفرعي"),
    "selectBranchToSet": MessageLookupByLibrary.simpleMessage(
      "حدد الفرع الذي تريد إعداده",
    ),
    "selectContentRating": MessageLookupByLibrary.simpleMessage(
      "الرجاء تحديد تصنيف المحتوى",
    ),
    "selectWorkshopItemToUpdate": MessageLookupByLibrary.simpleMessage(
      "حدد عنصر ورشة العمل الذي تريد تحديثه",
    ),
    "setAndEnableConditions": MessageLookupByLibrary.simpleMessage(
      "لتعيين الظروف التي يظهر فيها الخيار الحالي وتمكينها",
    ),
    "setAndEnableValueChanges": MessageLookupByLibrary.simpleMessage(
      "تعيين التغيير في القيمة وتمكينه بعد تحديد خيار",
    ),
    "setBranchConditionsAndChanges": m18,
    "setBranchParams": MessageLookupByLibrary.simpleMessage(
      "تعيين نوع الفرع والمعلمات",
    ),
    "setCurrentTime": MessageLookupByLibrary.simpleMessage("ضبط الوقت الحالي"),
    "setOptionsAndValueChanges": MessageLookupByLibrary.simpleMessage(
      "تعيين الخيارات وتغيير القيم",
    ),
    "setTo": MessageLookupByLibrary.simpleMessage("ضبط على"),
    "settings": MessageLookupByLibrary.simpleMessage("نصب"),
    "settingsSaved": MessageLookupByLibrary.simpleMessage("يتم حفظ الإعدادات"),
    "shape": MessageLookupByLibrary.simpleMessage("أشكال الصور"),
    "showController": MessageLookupByLibrary.simpleMessage("عرض شريط التحكم"),
    "showControllerDuringPlayback": MessageLookupByLibrary.simpleMessage(
      "عرض شريط التحكم أثناء تشغيل الفيديو",
    ),
    "showVideoController": MessageLookupByLibrary.simpleMessage(
      "عرض شريط التحكم في الفيديو",
    ),
    "showVideoControls": MessageLookupByLibrary.simpleMessage(
      "عرض شريط التحكم في الفيديو",
    ),
    "showVideoControlsDescription": MessageLookupByLibrary.simpleMessage(
      "عرض شريط التحكم أثناء تشغيل الفيديو",
    ),
    "sort": MessageLookupByLibrary.simpleMessage("صنف"),
    "sortBy": MessageLookupByLibrary.simpleMessage("الترتيب حسب"),
    "sortByFavorites": MessageLookupByLibrary.simpleMessage("حسب عدد المفضلات"),
    "sortByPublishDate": MessageLookupByLibrary.simpleMessage("تاريخ الإصدار"),
    "sortBySubscribers": MessageLookupByLibrary.simpleMessage(
      "حسب عدد الاشتراكات",
    ),
    "sortByUpdateDate": MessageLookupByLibrary.simpleMessage("تحديث"),
    "sortByVote": MessageLookupByLibrary.simpleMessage("حسب عدد الإعجابات"),
    "startElementIdNotFound": MessageLookupByLibrary.simpleMessage(
      "تعذر الحصول على معرف عنصر البداية للمشروع",
    ),
    "startGame": MessageLookupByLibrary.simpleMessage("العب اللعبة"),
    "startNode": MessageLookupByLibrary.simpleMessage("عقدة البدء"),
    "startNodeVideoCount": m19,
    "startPoint": MessageLookupByLibrary.simpleMessage("نقطة البداية"),
    "startTime": MessageLookupByLibrary.simpleMessage("بدأ\nالوقت"),
    "startTimeBeforeEndTime": MessageLookupByLibrary.simpleMessage(
      "يجب أن يكون وقت البدء أقدم من وقت الانتهاء",
    ),
    "startTimeLabel": MessageLookupByLibrary.simpleMessage("وقت البدء"),
    "steamAuthorOtherFiles": MessageLookupByLibrary.simpleMessage(
      "وثائق أخرى للمؤلف",
    ),
    "steamChallenge": MessageLookupByLibrary.simpleMessage("ابدأ التحدي"),
    "steamGallery": MessageLookupByLibrary.simpleMessage("معرض ورشة البخار"),
    "steamLimitedAccount": MessageLookupByLibrary.simpleMessage(
      "حسابات Steam المقيدة",
    ),
    "steamWorkshop": MessageLookupByLibrary.simpleMessage("ورشة البخار"),
    "stop": MessageLookupByLibrary.simpleMessage("كف عن هذا"),
    "storageNode": MessageLookupByLibrary.simpleMessage("عقد التخزين"),
    "style": MessageLookupByLibrary.simpleMessage("نمط الصورة"),
    "subscribeFailed": MessageLookupByLibrary.simpleMessage("فشل الاشتراك: "),
    "subscribeSuccess": MessageLookupByLibrary.simpleMessage(
      "تم الاشتراك بنجاح ، ابدأ التنزيل ...",
    ),
    "subtitle": MessageLookupByLibrary.simpleMessage("التسميه التوضيحيه"),
    "subtract": MessageLookupByLibrary.simpleMessage("طرح"),
    "switchHorizontal": MessageLookupByLibrary.simpleMessage(
      "التبديل إلى تخطيط أفقي",
    ),
    "switchVertical": MessageLookupByLibrary.simpleMessage(
      "التبديل إلى تخطيط رأسي",
    ),
    "textType": MessageLookupByLibrary.simpleMessage("نص"),
    "thickness": MessageLookupByLibrary.simpleMessage("سمك"),
    "timeEdit": MessageLookupByLibrary.simpleMessage("تحرير الوقت"),
    "timeLimit": MessageLookupByLibrary.simpleMessage("الحد الزمني (بالثواني)"),
    "timedBranch": MessageLookupByLibrary.simpleMessage("الفروع محدودة الوقت"),
    "title": MessageLookupByLibrary.simpleMessage("عنوان"),
    "titleCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
      "لا يمكن أن يكون العنوان فارغا",
    ),
    "titlePositionInfo": m20,
    "to": MessageLookupByLibrary.simpleMessage("وصل"),
    "toggleConnectable": MessageLookupByLibrary.simpleMessage(
      "يمكن توصيل التبديل",
    ),
    "toggleResizable": MessageLookupByLibrary.simpleMessage(
      "التبديل إلى تغيير الحجم",
    ),
    "toggleSortDirection": MessageLookupByLibrary.simpleMessage(
      "تبديل اتجاه الفرز",
    ),
    "transparent": MessageLookupByLibrary.simpleMessage("شفاف"),
    "unknownNodeType": MessageLookupByLibrary.simpleMessage("نوع غير معروف"),
    "unsubscribe": MessageLookupByLibrary.simpleMessage("إلغاء اشتراكك"),
    "unsubscribeFailed": MessageLookupByLibrary.simpleMessage(
      "فشل إلغاء الاشتراك",
    ),
    "unsubscribeSuccess": MessageLookupByLibrary.simpleMessage(
      "تم إلغاء الاشتراك بنجاح",
    ),
    "unsupportedFileFormat": MessageLookupByLibrary.simpleMessage(
      "تنسيقات الملفات غير المدعومة ، تسمح ورشة العمل فقط بملفات الفيديو و jpg و json",
    ),
    "updateSuccess": MessageLookupByLibrary.simpleMessage("نجاح التحديث!"),
    "updateTime": MessageLookupByLibrary.simpleMessage("تحديث"),
    "updateWorkshop": MessageLookupByLibrary.simpleMessage(
      "عناصر ورشة العمل المحدثة",
    ),
    "updateWorkshopError": m21,
    "updating": MessageLookupByLibrary.simpleMessage("تحديث..."),
    "upload": MessageLookupByLibrary.simpleMessage("رفع"),
    "uploadFailed": MessageLookupByLibrary.simpleMessage("فشل التحميل"),
    "uploadFailedWithColon": MessageLookupByLibrary.simpleMessage(
      "فشل التحميل: ",
    ),
    "uploadNow": MessageLookupByLibrary.simpleMessage("قم بتحميله الآن"),
    "uploadSuccess": MessageLookupByLibrary.simpleMessage("تم التحميل بنجاح"),
    "uploadWorkshop": MessageLookupByLibrary.simpleMessage("تحميل ورشة العمل"),
    "uploading": MessageLookupByLibrary.simpleMessage("تحميل"),
    "uploadingContent": MessageLookupByLibrary.simpleMessage("تحميل المحتوى"),
    "uploadingPleaseWait": MessageLookupByLibrary.simpleMessage(
      "التحميل ، يرجى الانتظار ...",
    ),
    "uploadingPreviewImage": MessageLookupByLibrary.simpleMessage(
      "تحميل معاينة",
    ),
    "useNewWindowForEditing": MessageLookupByLibrary.simpleMessage(
      "نافذة جديدة تفتح واجهة تحرير المشروع",
    ),
    "useNewWindowForEditingDescription": MessageLookupByLibrary.simpleMessage(
      "عند التمكين، يفتح محرر المشروع في نافذة جديدة، وعند تعطيله، يتم فتحه في النافذة الحالية",
    ),
    "valueInputHint": MessageLookupByLibrary.simpleMessage(
      "الرجاء إدخال قيمة أولية",
    ),
    "valueLabel": MessageLookupByLibrary.simpleMessage("القيمة الرقمية"),
    "variableAlreadyExists": MessageLookupByLibrary.simpleMessage(
      "اسم المتغير موجود بالفعل",
    ),
    "variableName": MessageLookupByLibrary.simpleMessage("اسم المتغير"),
    "variableType": MessageLookupByLibrary.simpleMessage("نوع"),
    "version": MessageLookupByLibrary.simpleMessage("الإصدار"),
    "verticalLayoutDescription": MessageLookupByLibrary.simpleMessage(
      "واجهة تحرير الفيديو العمودية (موصى بها للشاشات عالية الدقة)",
    ),
    "videoCover": MessageLookupByLibrary.simpleMessage("غلاف الفيديو"),
    "videoFileNotExist": m22,
    "videoNode": MessageLookupByLibrary.simpleMessage("عقد الفيديو"),
    "videoNodeLocked": MessageLookupByLibrary.simpleMessage(
      "لم يتم فتح عقدة الفيديو هذه ، يرجى مشاهدة الفيديو السابق أولا",
    ),
    "videoPlayback": MessageLookupByLibrary.simpleMessage("تشغيل الفيديو"),
    "videoPlaybackError": m23,
    "videoTimeAndCover": MessageLookupByLibrary.simpleMessage(
      "وقت الفيديو والغطاء",
    ),
    "viewFlowChart": MessageLookupByLibrary.simpleMessage(
      "عرض المخطط الانسيابي",
    ),
    "viewFlowchart": MessageLookupByLibrary.simpleMessage(
      "عرض المخطط الانسيابي",
    ),
    "visibility": MessageLookupByLibrary.simpleMessage("رؤية"),
    "volume": MessageLookupByLibrary.simpleMessage("مستوى الصوت"),
    "volumeControl": MessageLookupByLibrary.simpleMessage(
      "التحكم في مستوى الصوت",
    ),
    "watchedNodesCount": MessageLookupByLibrary.simpleMessage(
      "عدد العقد التي تمت مراقبتها",
    ),
    "workshop": MessageLookupByLibrary.simpleMessage("حلقه العمل"),
    "workshopItemUpdated": MessageLookupByLibrary.simpleMessage(
      "تم تحديث مشروع ورشة العمل",
    ),
    "workshopItemUploaded": MessageLookupByLibrary.simpleMessage(
      "تم تحميل مشروع ورشة العمل",
    ),
    "workshopItems": MessageLookupByLibrary.simpleMessage("مشروع ورشة العمل"),
    "workshopRecommendedDescription": MessageLookupByLibrary.simpleMessage(
      "قال القرويون ، الذي كتبه رئيس قرية المحرك نفسه ، إن هذا الدليل سيتطور تلقائيا مع تحديث الإصدار",
    ),
    "workshopRecommendedTitle": MessageLookupByLibrary.simpleMessage(
      "دليل إنقاذ العالم",
    ),
    "workshopRuleNoAds": MessageLookupByLibrary.simpleMessage("لا إعلانات"),
    "workshopRuleNoAdult": MessageLookupByLibrary.simpleMessage(
      "لا توجد صور فوتوغرافية أو إباحية أصلية أو عري",
    ),
    "workshopRuleNoCopyright": MessageLookupByLibrary.simpleMessage(
      "لا انتهاك لحقوق الطبع والنشر",
    ),
    "workshopRuleNoMisleading": MessageLookupByLibrary.simpleMessage(
      "لا توجد معاينات مضللة",
    ),
    "workshopRuleNoOffensive": MessageLookupByLibrary.simpleMessage(
      "لا هجوم أو عنيف ، دموي",
    ),
    "workshopRules": MessageLookupByLibrary.simpleMessage("قواعد ورشة العمل"),
    "workshopRulesDescription": MessageLookupByLibrary.simpleMessage(
      "قبل إرسال مقطع فيديو تفاعلي إلى ورشة العمل ، يرجى التأكد من أنه لا ينتهك شروط خدمة Steam ، وإلا ستتم إزالة الفيديو التفاعلي:",
    ),
    "workshopRulesSpecial": MessageLookupByLibrary.simpleMessage(
      "على وجه الخصوص ، يجب أن تلتزم مقاطع الفيديو التفاعلية والمعاينات والأوصاف بالقواعد التالية:",
    ),
  };
}
