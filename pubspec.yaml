name: ve
description: "A new Flutter project."
publish_to: 'none'
version: 0.1.0

environment:
  sdk: ^3.5.3

dependencies:
  flutter:
    sdk: flutter
  media_kit: ^1.2.0
  media_kit_video: 
  media_kit_libs_video: # Native video dependencies.
  fullscreen_window: ^1.1.0

  #UI
  flutter_icons_null_safety: 
  flutter_screenutil: 
  percent_indicator: 
  stacked: 
  steamworks_gen: ^0.4.4
  shared_preferences: ^2.5.3

  #swk
  steamworks: ^0.4.7
  # steamworks:
    # path: mypackage/steamwk

  #本地化
  flutter_localizations:
    sdk: flutter
  intl: 
  ffi: ^2.1.3

  #多窗口
  extra_alignments: ^1.0.0+1
  flextras: ^1.0.0
  flutter_animate: ^4.5.0
  focusable_control_builder: ^1.0.2+1
  gap: ^3.0.1
  particle_field: ^1.0.0
  rnd: ^0.2.0
  vector_math: ^2.1.4
  cupertino_icons: ^1.0.8
  provider: ^6.1.2
  window_size: ^0.1.0
  desktop_window: ^0.4.2
  window_manager: ^0.4.3

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0
  url_launcher: ^6.2.6
  intl: any
  path_provider: ^2.1.3
  font_awesome_flutter: ^10.7.0
  get: ^4.6.6
  file_picker:
  flutter_colorpicker: ^1.0.3
  star_menu: ^4.0.1
  web: ^1.0.0
  flutter_flow_chart:
    path: mypackage/flutter_flow_chart

  # 国际化
  intl_generator: 

flutter:
  uses-material-design: true
  generate: true

  assets:
    - assets/images/
    - assets/fonts/

  fonts:
    - family: Lato
      fonts:
        - asset: assets/fonts/Lato-Regular.ttf
        - asset: assets/fonts/Lato-Italic.ttf
          style: italic
        - asset: assets/fonts/Lato-Bold.ttf
          weight: 700
    - family: WorkSans
      fonts:
        - asset: assets/fonts/WorkSans-Regular.ttf
        - asset: assets/fonts/WorkSans-Italic.ttf
          style: italic
        - asset: assets/fonts/WorkSans-Bold.ttf
          weight: 700
        - asset: assets/fonts/WorkSans-Medium.ttf
          weight: 500
        - asset: assets/fonts/WorkSans-SemiBold.ttf
          weight: 600
    - family: Exo
      fonts:
        - asset: assets/fonts/Exo-Bold.ttf
        - asset: assets/fonts/Exo-Medium.ttf

  shaders:
    - assets/shaders/orb_shader.frag
    - assets/shaders/ui_glitch.frag

flutter_intl:
  enabled: true
