import 'package:flutter/material.dart';
import 'package:ve/main.dart';
import 'package:media_kit/media_kit.dart';
import '../../../../utils/video.dart';
import 'package:flutter_flow_chart/flutter_flow_chart.dart';
import 'dart:io';
import 'create_game_view.dart';
import 'package:ve/generated/l10n.dart';
import 'package:shared_preferences/shared_preferences.dart';

class FloatingVideoWindow extends StatefulWidget {
  final FlowElement element;
  
  const FloatingVideoWindow({
    required this.element,
    Key? key,
  }) : super(key: key);

  @override
  FloatingVideoWindowState createState() => FloatingVideoWindowState();
}

class FloatingVideoWindowState extends State<FloatingVideoWindow> {
  late VideoPlayerManager videoPlayerManager;
  bool _isFullScreen = false;
  Offset position = const Offset(20, 20);
  bool _isVerticalLayout = false;
  bool _showControls = true; // 添加控制器显示/隐藏状态变量

  late SharedPreferences _prefs;
  bool _enableVideoClickPause = false;

  late TextEditingController _startHourController;
  late TextEditingController _startMinuteController;
  late TextEditingController _startSecondController;
  late TextEditingController _endHourController;
  late TextEditingController _endMinuteController;
  late TextEditingController _endSecondController;

  late FocusNode _startHourFocusNode;
  late FocusNode _startMinuteFocusNode;
  late FocusNode _startSecondFocusNode;
  late FocusNode _endHourFocusNode;
  late FocusNode _endMinuteFocusNode;
  late FocusNode _endSecondFocusNode;

  late int allTime;
  Future<void> _generateThumbnail({bool force = false}) async {
    final String thumbnailPath = '${widget.element.projectPath}/${widget.element.id}.jpg';
    
    // 如果文件存在且不是强制生成，则直接返回
    if (!force && await File(thumbnailPath).exists()) {
      return;
    }

    final directory = Directory(widget.element.projectPath);
    if (!await directory.exists()) {
      await directory.create(recursive: true);
    }

    // 计算缩略图位置：如果开始时间为0，则使用视频10%位置，否则使用开始时间
    final startTimeSeconds = _timeStringToSeconds(widget.element.startTime);
    final thumbnailPosition = startTimeSeconds == 0 
        ? Duration(seconds: (allTime * 0.1).round())
        : Duration(seconds: startTimeSeconds);

    try {
      // 确保视频已经加载
      await videoPlayerManager.player.seek(thumbnailPosition);
      // 添加延迟确保视频帧已经渲染
      await Future.delayed(const Duration(milliseconds: 500));
      // 强制刷新一帧
      await videoPlayerManager.player.play();
      await Future.delayed(const Duration(milliseconds: 100));
      await videoPlayerManager.player.pause();
      
      // 然后再截图
      final bytes = await videoPlayerManager.player.screenshot(format: 'image/jpeg');
      if (bytes != null) {
        await File(thumbnailPath).writeAsBytes(bytes);
      }

    } catch (e) {
      debugPrint('生成缩略图失败: $e');
    }
  }

  @override
  void initState() {
    super.initState();
    videoPlayerManager = VideoPlayerManager();
    
    // 解析开始时间，支持毫秒精度
    final startTimeParts = widget.element.startTime.split(':');
    int startHours = int.parse(startTimeParts[0]);
    int startMinutes = int.parse(startTimeParts[1]);
    int startSeconds = 0;
    int startMilliseconds = 0;
    
    // 处理秒和毫秒部分
    String secondsPart = startTimeParts[2];
    if (secondsPart.contains('.')) {
      List<String> secondsAndMillis = secondsPart.split('.');
      startSeconds = int.parse(secondsAndMillis[0]);
      if (secondsAndMillis.length > 1) {
        // 尝试解析毫秒，如果无法解析则默认为0
        startMilliseconds = int.tryParse(secondsAndMillis[1]) ?? 0;
      }
    } else {
      startSeconds = int.parse(secondsPart);
    }
    
    final startTimeDuration = Duration(
      hours: startHours,
      minutes: startMinutes,
      seconds: startSeconds,
      milliseconds: startMilliseconds
    );
    
    // 使用Media的start参数从开始时间播放
    videoPlayerManager.player.open(Media(widget.element.path, start: startTimeDuration));
    
    allTime = _timeStringToSeconds(widget.element.allTime);
    _initializeControllers();
    _initializeFocusNodes();
    
    // 从AppStateContainer获取全局布局设置
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        final container = AppStateContainer.of(context, throwOnError: false);
        setState(() {
          _isVerticalLayout = container.isVerticalLayout;
        });
      }
    });
  
    // 自动生成封面
    _generateThumbnail();
    
    // 加载视频点击暂停设置
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    _prefs = await SharedPreferences.getInstance();
    if (mounted) {
      setState(() {
        _enableVideoClickPause = _prefs.getBool('enableVideoClickPause') ?? false;
      });
    }
  }

  void _initializeControllers() {
    final startParts = widget.element.startTime.split(':');
    final endParts = widget.element.endTime.split(':');
  
    _startHourController = TextEditingController(text: startParts[0]);
    _startMinuteController = TextEditingController(text: startParts[1]);
    _startSecondController = TextEditingController(text: startParts[2]);
    _endHourController = TextEditingController(text: endParts[0]);
    _endMinuteController = TextEditingController(text: endParts[1]);
    _endSecondController = TextEditingController(text: endParts[2]);
  }

  void _initializeFocusNodes() {
    _startHourFocusNode = FocusNode()..addListener(() {
      if (!_startHourFocusNode.hasFocus) _updateTime(1);
    });
    _startMinuteFocusNode = FocusNode()..addListener(() {
      if (!_startMinuteFocusNode.hasFocus) _updateTime(1);
    });
    _startSecondFocusNode = FocusNode()..addListener(() {
      if (!_startSecondFocusNode.hasFocus) _updateTime(1);
    });
    _endHourFocusNode = FocusNode()..addListener(() {
      if (!_endHourFocusNode.hasFocus) _updateTime(2);
    });
    _endMinuteFocusNode = FocusNode()..addListener(() {
      if (!_endMinuteFocusNode.hasFocus) _updateTime(2);
    });
    _endSecondFocusNode = FocusNode()..addListener(() {
      if (!_endSecondFocusNode.hasFocus) _updateTime(2);
    });
  }

  // 添加静态缓存
  static final Map<String, Future<File>> _thumbnailCache = {};
  Widget _buildThumbnailPanel() {
    // 使用缓存的Future
    _thumbnailCache[widget.element.id] ??= _getThumbnailFile();
      
    return Card(
      margin: EdgeInsets.all(8.0),
      child: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(S.of(context).videoCover, style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
            Divider(),
            Center(
              child: FutureBuilder<File>(
                future: _thumbnailCache[widget.element.id],
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return CircularProgressIndicator();
                  }
                  if (snapshot.hasError || !snapshot.hasData) {
                    return Text(S.of(context).coverLoadFailed);
                  }
                  return Container(
                    width: 320,
                    height: 180,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Image.file(
                      snapshot.data!,
                      fit: BoxFit.cover,
                      key: ValueKey(DateTime.now()),
                    ),
                  );
                },
              ),
            ),
            SizedBox(height: 16),
            Center(
              child: ElevatedButton.icon(
                icon: Icon(Icons.camera_alt),
                label: Text(S.of(context).captureCurrentFrame),
                onPressed: _captureCurrentFrame,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<File> _getThumbnailFile() async {
      final String thumbnailPath = '${widget.element.projectPath}/${widget.element.id}.jpg';
      final file = File(thumbnailPath);
      if (!await file.exists()) {
        await _generateThumbnail();
      }
      return file;
  }
  void _refreshThumbnailDisplay() {
    // 清除旧的缓存
    _thumbnailCache.remove(widget.element.id);
    // 强制重新获取文件
    _thumbnailCache[widget.element.id] = Future.value(File('${widget.element.projectPath}/${widget.element.id}.jpg'));
    setState(() {});
  }
  // 添加一个状态变量
  bool _isCapturing = false;

  Future<void> _captureCurrentFrame() async {
    setState(() => _isCapturing = true);
  
    try {
      final bytes = await videoPlayerManager.player.screenshot(format: 'image/jpeg');
      if (bytes != null) {
        final file = File('${widget.element.projectPath}/${widget.element.id}.jpg');
        await file.writeAsBytes(bytes);
      
        // 清除图片缓存
        imageCache.clear();
        imageCache.clearLiveImages();
      
        setState(() {});
      }
    } finally {
      setState(() => _isCapturing = false);
    }
  }
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 在依赖变化时检查全局布局设置
    final container = AppStateContainer.of(context, throwOnError: false);
    if (_isVerticalLayout != container.isVerticalLayout) {
      setState(() {
        _isVerticalLayout = container.isVerticalLayout;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: position.dx,
      top: position.dy,
      child: GestureDetector(
        onPanUpdate: (details) {
          setState(() {
            position += details.delta;
          });
        },
        child: Material(
          elevation: 2.0,
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: BorderRadius.circular(8.0),
          child: _isVerticalLayout ? Container(
            width: _isFullScreen ? MediaQuery.of(context).size.width : 640, // 设置为与视频播放器相同的宽度
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildHeader(),
                _buildVideoPlayer(),
                if (!_isFullScreen) ...[
                  _buildControlPanel(),
                  _buildCombinedPanel(),
                ],
              ],
            ),
          ):
          Container(
            width: _isFullScreen ? MediaQuery.of(context).size.width : 1280, // 设置为与视频播放器相同的宽度
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  flex: 1,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      _buildHeader(),
                      _buildVideoPlayer(),
                    ],
                  ),
                ),
                if (!_isFullScreen) ...[
                  Expanded(
                    flex: 1,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        _buildControlPanel(),
                        _buildCombinedPanel(),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Card(
      margin: EdgeInsets.all(8.0),
      child: Padding(
        padding: EdgeInsets.all(8.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(S.of(context).videoPlayback, style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
            Row(
              children: [
                IconButton(
                  icon: Icon(_showControls ? Icons.visibility : Icons.visibility_off),
                  onPressed: () => setState(() => _showControls = !_showControls),
                  tooltip: _showControls ? S.of(context).hideController : S.of(context).showController,
                ),
                IconButton(
                  icon: Icon(_isFullScreen ? Icons.fullscreen_exit : Icons.fullscreen),
                  onPressed: () => setState(() => _isFullScreen = !_isFullScreen),
                ),
                IconButton(
                  icon: Icon(Icons.close),
                  onPressed: () => Navigator.of(context).pop(),
                ),
                  IconButton(
                    icon: Icon(_isVerticalLayout ? Icons.view_stream : Icons.view_column),
                    onPressed: () {
                      final container = AppStateContainer.of(context, throwOnError: false);
                      container.toggleVerticalLayout();
                      setState(() {
                        _isVerticalLayout = container.isVerticalLayout;
                      });
                    },
                    tooltip: _isVerticalLayout ? S.of(context).switchHorizontal : S.of(context).switchVertical,
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVideoPlayer() {
    return Card(
      margin: EdgeInsets.all(8.0),
      elevation: 4.0,
      child: SizedBox(
        width: _isFullScreen ? MediaQuery.of(context).size.width - 32 : _isVerticalLayout ? 640 : 1280,
        height: _isFullScreen ? MediaQuery.of(context).size.height - 200 : 360,
        child: videoPlayerManager.buildVideoWidget(
          context, 
          showControls: _showControls,
          enableClickPause: _enableVideoClickPause,
        ),
      ),
    );
  }

  Widget _buildControlPanel() {
    return Card(
      margin: EdgeInsets.all(8.0),
      child: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(S.of(context).playbackControl, style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
            Divider(),
            _buildPlaybackRateControl(),
            SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton(
                  onPressed: () => videoPlayerManager.player.play(),
                  child: Text(S.of(context).play),
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.blue),
                ),
                ElevatedButton(
                  onPressed: () => videoPlayerManager.player.pause(),
                  child: Text(S.of(context).pause),
                ),
                ElevatedButton(
                  onPressed: () => videoPlayerManager.player.playOrPause(),
                  child: Text(S.of(context).playPause),
                ),
                ElevatedButton(
                  onPressed: () => videoPlayerManager.player.stop(),
                  child: Text(S.of(context).stop),
                ),
              ],
            ),
            // _buildProgressControl(),  // 播放进度控制
            // _buildVolumeControl(),    // 音量控制
            SizedBox(height: 16),
            Center(
              child: Table(
                defaultColumnWidth: FixedColumnWidth(180),
                children: [
                  TableRow(
                    children: [
                      StreamBuilder<bool>(
                        stream: videoPlayerManager.player.stream.playing,
                        builder: (context, snapshot) => Padding(
                          padding: EdgeInsets.symmetric(vertical: 4),
                          child: Text('${S.of(context).playbackStatus}: ${snapshot.data ?? false}'),
                        ),
                      ),
                      StreamBuilder<Duration?>(
                        stream: videoPlayerManager.player.stream.position,
                        builder: (context, snapshot) => Padding(
                          padding: EdgeInsets.symmetric(vertical: 4),
                          child: Text('${S.of(context).currentPosition}: ${snapshot.data?.toString().substring(0, snapshot.data.toString().length-4)}'),
                        ),
                      ),
                      StreamBuilder<double>(
                        stream: videoPlayerManager.player.stream.volume,
                        builder: (context, snapshot) => Padding(
                          padding: EdgeInsets.symmetric(vertical: 4),
                          child: Text('${S.of(context).volume}: ${(snapshot.data ?? 1.0).toStringAsFixed(1)}'),
                        ),
                      ),
                    ],
                  ),
                  TableRow(
                    children: [
                      StreamBuilder<bool>(
                        stream: videoPlayerManager.player.stream.completed,
                        builder: (context, snapshot) => Padding(
                          padding: EdgeInsets.symmetric(vertical: 4),
                          child: Text('${S.of(context).completed}: ${snapshot.data ?? false}'),
                        ),
                      ),
                      StreamBuilder<Duration?>(
                        stream: videoPlayerManager.player.stream.duration,
                        builder: (context, snapshot) => Padding(
                          padding: EdgeInsets.symmetric(vertical: 4),
                          child: Text('${S.of(context).duration}: ${snapshot.data?.toString().substring(0, snapshot.data.toString().length-4)}'),
                        ),
                      ),
                      StreamBuilder<double>(
                        stream: videoPlayerManager.player.stream.rate,
                        builder: (context, snapshot) => Padding(
                          padding: EdgeInsets.symmetric(vertical: 4),
                          child: Text('${S.of(context).rate}: ${(snapshot.data ?? 1.0).toStringAsFixed(1)}'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            )
          ],
        ),
      ),    
    );
  }
  
  Widget _buildProgressControl() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(S.of(context).playbackProgress, style: TextStyle(fontSize: 14)),
        StreamBuilder(
          stream: videoPlayerManager.player.stream.position,
          builder: (context, positionSnapshot) {
            return StreamBuilder(
              stream: videoPlayerManager.player.stream.duration,
              builder: (context, durationSnapshot) {
                final duration = durationSnapshot.data?.inMilliseconds.toDouble() ?? 1.0;
                final position = positionSnapshot.data?.inMilliseconds.toDouble() ?? 0.0;
                return Slider(
                  min: 0.0,
                  max: duration,
                  value: position,
                  onChanged: (value) {
                    videoPlayerManager.player.seek(Duration(milliseconds: value.toInt()));
                  },
                );
              }
            );
          }
        ),
      ],
    );
  }

  Widget _buildVolumeControl() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(S.of(context).volumeControl, style: TextStyle(fontSize: 14)),
        StreamBuilder(
          stream: videoPlayerManager.player.stream.volume,
          builder: (context, snapshot) {
            return Row(
              children: [
                Icon(Icons.volume_up),
                Expanded(
                  child: Slider(
                    min: 0.0,
                    max: 1.0,
                    value: snapshot.data?.toDouble() ?? 1.0,
                    onChanged: (volume) {
                      videoPlayerManager.player.setVolume(volume);
                    },
                  ),
                ),
              ],
            );
          }
        ),
      ],
    );
  }

  Widget _buildPlaybackRateControl() {
    return Center(
      child: Container(
        width: 600,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(S.of(context).playbackRate, style: TextStyle(fontSize: 14)),
            StreamBuilder(
              stream: videoPlayerManager.player.stream.rate,
              builder: (context, snapshot) {
                return Slider(
                  min: 0.1,
                  max: 10.0,
                  value: snapshot.data?.toDouble() ?? 1.0,
                  onChanged: (rate) {
                    videoPlayerManager.player.setRate(rate);
                  },
                );
              }
            ),
          ],
        ),
      ),
    );
  }  


  Widget _buildCombinedPanel() {
    // 使用缓存的Future
    _thumbnailCache[widget.element.id] ??= _getThumbnailFile();
    
    return Card(
      margin: EdgeInsets.all(8.0),
      child: Padding(
        padding: EdgeInsets.all(16.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 左侧视频封面
            Expanded(
              flex: 3,  // 视频封面占2份宽度
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(S.of(context).videoCover, style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                  Divider(),
                  Center(
                    child: FutureBuilder<File>(
                      future: _thumbnailCache[widget.element.id],
                      builder: (context, snapshot) {
                        if (snapshot.connectionState == ConnectionState.waiting) {
                          return CircularProgressIndicator();
                        }
                        if (snapshot.hasError || !snapshot.hasData) {
                          return Text(S.of(context).coverLoadFailed);
                        }
                        return Container(
                          width: 320,
                          height: 180,
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Image.file(
                            snapshot.data!,
                            fit: BoxFit.cover,
                            key: ValueKey(DateTime.now()),
                          ),
                        );
                      },
                    ),
                  ),
                  SizedBox(height: 16),
                  Center(
                    child: ElevatedButton.icon(
                      icon: Icon(Icons.camera_alt),
                      label: Text(S.of(context).captureCurrentFrame),
                      onPressed: _captureCurrentFrame,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(width: 16),
            Expanded(
              flex: 2,  // 时间编辑占1份宽度
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(S.of(context).timeEdit, style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                  Divider(),
                  Column(
                    children: [
                        Center(
                          child: Column(
                            children: [
                              Center(
                                child: _buildTimeInputField(
                                  S.of(context).startTime,
                                  _startHourController,
                                  _startMinuteController,
                                  _startSecondController,
                                  _startHourFocusNode,
                                  _startMinuteFocusNode,
                                  _startSecondFocusNode
                                ),
                              ),
                              SizedBox(height: 8),
                            ],
                          ),
                        ),
                      StreamBuilder<Duration?>(
                        stream: videoPlayerManager.player.stream.position,
                        builder: (context, snapshot) {
                          return ElevatedButton.icon(
                            icon: Icon(Icons.timer),
                            label: Text(S.of(context).setCurrentTime),
                            onPressed: () {
                              final time = snapshot.data ?? Duration.zero;
                              final hours = time.inHours;
                              final minutes = (time.inMinutes % 60);
                              final seconds = (time.inSeconds % 60);
                            
                              _startHourController.text = hours.toString();
                              _startMinuteController.text = minutes.toString().padLeft(2, '0');
                              _startSecondController.text = seconds.toString().padLeft(2, '0');
                              _updateTime(1);
                            },
                          );
                        }
                      ),
                    ],
                  ),
                  SizedBox(height: 16),
                  Column(
                    children: [
                      _buildTimeInputField(S.of(context).endTime, _endHourController, _endMinuteController,
                          _endSecondController, _endHourFocusNode, _endMinuteFocusNode, _endSecondFocusNode),
                      SizedBox(height: 8),
                      StreamBuilder<Duration?>(
                        stream: videoPlayerManager.player.stream.position,
                        builder: (context, snapshot) {
                          return ElevatedButton.icon(
                            icon: Icon(Icons.timer),
                            label: Text(S.of(context).setCurrentTime),
                            onPressed: () {
                              final time = snapshot.data ?? Duration.zero;
                              final hours = time.inHours;
                              final minutes = (time.inMinutes % 60);
                              final seconds = (time.inSeconds % 60);
                            
                              _endHourController.text = hours.toString();
                              _endMinuteController.text = minutes.toString().padLeft(2, '0');
                              _endSecondController.text = seconds.toString().padLeft(2, '0');
                              _updateTime(2);
                            },
                          );
                        }
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildTimeTextField(TextEditingController controller, FocusNode focusNode, {double width = 40}) {
    return SizedBox(
      width: width,
      child: TextField(
        controller: controller,
        focusNode: focusNode,
        style: TextStyle(color: Colors.black87),
        decoration: InputDecoration(
          contentPadding: EdgeInsets.symmetric(horizontal: 4),
          border: OutlineInputBorder(),
        ),
        keyboardType: TextInputType.number,
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildTimeInputField(String label, TextEditingController hourController, 
    TextEditingController minuteController, TextEditingController secondController,
    FocusNode hourFocusNode, FocusNode minuteFocusNode, FocusNode secondFocusNode) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,  // 添加这行
      children: [
        Text(label),
        SizedBox(width: 8),
        _buildTimeTextField(hourController, hourFocusNode),
        Text(':'),
        _buildTimeTextField(minuteController, minuteFocusNode),
        Text(':'),
        _buildTimeTextField(secondController, secondFocusNode, width: 50),  // 秒输入框宽度加倍
      ],
    );
  }

  int _timeStringToSeconds(String time) {
    List<String> parts = time.split(':');
    int hours = int.parse(parts[0]);
    int minutes = int.parse(parts[1]);
    
    // 处理秒和毫秒部分
    String secondsPart = parts[2];
    int seconds = 0;
    
    if (secondsPart.contains('.')) {
      List<String> secondsAndMillis = secondsPart.split('.');
      // 确保秒部分是有效的数字
      seconds = int.parse(secondsAndMillis[0]);
      // 毫秒部分在这里不需要处理，因为我们只需要返回秒级精度的结果
    } else {
      seconds = int.parse(secondsPart);
    }
    
    // 计算总秒数
    return hours * 3600 + minutes * 60 + seconds;
  }

  void _updateTime(int index) {
    String hour, minute, second;
    if (index == 1) {
      hour = _startHourController.text;
      minute = _startMinuteController.text;
      second = _startSecondController.text;
      if (!_validateTime(hour, minute, second)) {
        _showErrorDialog(S.of(context).invalidStartTime);
        return;
      }
    } else {
      hour = _endHourController.text;
      minute = _endMinuteController.text;
      second = _endSecondController.text;
      if (!_validateTime(hour, minute, second)) {
        _showErrorDialog(S.of(context).invalidEndTime);
        return;
      }
    }

    // 确保小时部分是有效的，如果为0，则格式化为'0'
    hour = (int.tryParse(hour) ?? 0).toString();
    // 确保分钟部分至少有两位数字
    minute = minute.padLeft(2, '0');
    
    // 处理秒，保留小数点和毫秒部分
    String formattedSecond;
    if (second.contains('.')) {
      List<String> secondParts = second.split('.');
      // 确保秒部分至少有两位数字
      formattedSecond = secondParts[0].padLeft(2, '0');
      // 如果有毫秒部分，则添加回来
      if (secondParts.length > 1) {
        formattedSecond += '.${secondParts[1]}';
      } else {
        formattedSecond += '.0'; // 添加默认的毫秒部分
      }
    } else {
      // 如果没有小数点，则添加默认的毫秒部分
      formattedSecond = second.padLeft(2, '0') + '.0';
    }

    // 确保分钟部分不超过两位数字
    if (minute.length > 2) minute = minute.substring(minute.length - 2);
    
    // 确保秒部分的整数部分不超过两位数字
    if (formattedSecond.contains('.')) {
      List<String> parts = formattedSecond.split('.');
      if (parts[0].length > 2) parts[0] = parts[0].substring(parts[0].length - 2);
      formattedSecond = parts[0] + '.' + parts[1];
    } else {
      if (formattedSecond.length > 2) formattedSecond = formattedSecond.substring(formattedSecond.length - 2);
      formattedSecond += '.0';
    }

    // 更新元素的时间
    if (index == 1) {
      widget.element.startTime = '$hour:$minute:$formattedSecond';
    } else {
      widget.element.endTime = '$hour:$minute:$formattedSecond';
    }
  }

  bool _validateTime(String hour, String minute, String second) {
    int? h = int.tryParse(hour);
    int? m = int.tryParse(minute);
    
    // 分离秒和毫秒部分
    int? s;
    if (second.contains('.')) {
      // 只取小数点前的部分
      String secondsOnly = second.split('.')[0];
      s = int.tryParse(secondsOnly);
    } else {
      s = int.tryParse(second);
    }

    if (h == null || m == null || s == null) return false;
    if (h < 0 || h > 999 || m < 0 || m >= 60 || s < 0 || s >= 60) return false;

    // 使用我们的_timeStringToSeconds方法来计算总秒数
    int startTotalSeconds = _timeStringToSeconds('${_startHourController.text}:${_startMinuteController.text}:${_startSecondController.text}');
    int endTotalSeconds = _timeStringToSeconds('${_endHourController.text}:${_endMinuteController.text}:${_endSecondController.text}');
    
    // 确保开始时间小于结束时间
    if (startTotalSeconds >= endTotalSeconds) return false;
    
    // 确保结束时间不超过视频总时长
    if (endTotalSeconds > allTime) return false;

    return true;
  }

  void _showErrorDialog(String message) {
    // Reset text fields to original values
    _startHourController.text = widget.element.startTime.split(':')[0];
    _startMinuteController.text = widget.element.startTime.split(':')[1];
    _startSecondController.text = widget.element.startTime.split(':')[2];
    _endHourController.text = widget.element.endTime.split(':')[0];
    _endMinuteController.text = widget.element.endTime.split(':')[1];
    _endSecondController.text = widget.element.endTime.split(':')[2];

    final overlay = Overlay.of(context);
    final overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        bottom: 100,
        left: MediaQuery.of(context).size.width / 2 - 120,
        child: Material(
          elevation: 8,
          borderRadius: BorderRadius.circular(4),
          child: Container(
            width: 240,
            padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: BoxDecoration(
              color: Theme.of(context).snackBarTheme.backgroundColor ?? Colors.grey[800],
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              message,
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.white),
            ),
          ),
        ),
      ),
    );

    overlay.insert(overlayEntry);
    Future.delayed(Duration(seconds: 2), () => overlayEntry.remove());
  }

  @override
  void dispose() {
    _startHourController.dispose();
    _startMinuteController.dispose();
    _startSecondController.dispose();
    _endHourController.dispose();
    _endMinuteController.dispose();
    _endSecondController.dispose();

    _startHourFocusNode.dispose();
    _startMinuteFocusNode.dispose();
    _startSecondFocusNode.dispose();
    _endHourFocusNode.dispose();
    _endMinuteFocusNode.dispose();
    _endSecondFocusNode.dispose();

    videoPlayerManager.dispose();
    super.dispose();
  }
}
