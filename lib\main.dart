import 'package:ve/UI/Views/home/<USER>';
import 'package:ve/UI/config/size_config.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:ve/generated/l10n.dart'; // 新的导入路径
import 'utils/steam_looper.dart';
import 'utils/steam_ex.dart';
import 'utils/gom_localization_delegates.dart'; // 导入gom本地化代理
import 'utils/steam_globals.dart'; // 导入Steam全局变量管理类
import 'package:steamworks/steamworks.dart';
import 'dart:io';
import 'package:media_kit/media_kit.dart';
import 'package:media_kit_video/media_kit_video.dart'; // 导入 media_kit_video
import 'package:media_kit_libs_video/media_kit_libs_video.dart'; // 导入 media_kit_libs_video
import 'utils/fallback_localization_delegates.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'utils/data.dart'; // 导入 Data 类
import 'dart:convert';
import 'package:ve/UI/Views/open/open_game_view.dart'; // 导入 OpenGameView
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';
import 'next-gen-ui/assets.dart';
import 'next-gen-ui/title_screen/title_screen.dart';
import 'package:ve/UI/Views/create/create_game_view.dart' show CreateGameView;
import 'package:fullscreen_window/fullscreen_window.dart';
import 'package:window_manager/window_manager.dart'; // 恢复导入window_manager

void main(List<String> args) async {
  // 确保 Flutter 绑定初始化
  WidgetsFlutterBinding.ensureInitialized();
  
  // 初始化窗口管理器
  if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
    // 必须先调用windowManager.ensureInitialized()
    await windowManager.ensureInitialized();
    
    // 设置窗口选项
    WindowOptions windowOptions = WindowOptions(
      size: Size(1280, 720),
      center: true,
      backgroundColor: Colors.transparent,
      title: "Interactive Movie Engine", // 使用临时标题，稍后会更新为本地化字符串
      titleBarStyle: TitleBarStyle.normal,
      windowButtonVisibility: true,
    );
    
    await windowManager.waitUntilReadyToShow(windowOptions, () async {
      await windowManager.show();
      await windowManager.focus();
    });
  }
  
  // 初始化 MediaKit
  MediaKit.ensureInitialized();
  
  // 处理多实例启动
  if (args.isNotEmpty) {
    if (args.first == '--new-instance-base64' && args.length > 1) {
      // 从Base64编码中解析参数
      try {
        Map<String, dynamic> arguments = <String, dynamic>{};

        // 解析Base64编码的参数
        try {
          final base64Str = args[1];
          final jsonStr = utf8.decode(base64Decode(base64Str));
          arguments = jsonDecode(jsonStr) as Map<String, dynamic>;
          print('新实例参数解析成功 (Base64): $arguments');
        } catch (e) {
          print('新实例参数解析失败 (Base64): $e');
          print('原始Base64参数: ${args[1]}');
        }

        // 初始化必要的组件用于独立实例
        // await Data.init();

        // 处理Steam初始化 - 在多实例模式下需要特别小心
        // 在新实例中，我们不进行Steam客户端初始化，避免重复初始化造成冲突
        // 多实例模式下仅使用主实例的Steam功能

        // 启动新实例窗口
        runApp(GameInstanceApp(
          arguments: arguments,
        ));
        return;
      } catch (e) {
        print('新实例启动失败 (Base64): $e');
      }
    } else if (args.first == '--new-instance' && args.length > 1) {
      // 向后兼容支持直接JSON参数(不推荐）
      try {
        Map<String, dynamic> arguments = <String, dynamic>{};

        // 解析参数
        try {
          arguments = jsonDecode(args[1]) as Map<String, dynamic>;
          print('新实例参数解析成功: $arguments');
        } catch (e) {
          print('新实例参数解析失败: $e');
          print('原始参数: ${args[1]}');
        }

        // 初始化必要的组件用于独立实例
        // await Data.init();

        // 处理Steam初始化 - 在多实例模式下需要特别小心
        // 在新实例中，我们不进行Steam客户端初始化，避免重复初始化造成冲突
        // 多实例模式下仅使用主实例的Steam功能

        // 启动新实例窗口
        runApp(GameInstanceApp(
          arguments: arguments,
        ));
        return;
      } catch (e) {
        print('新实例启动失败: $e');
      }
    }
  }

  // 主窗口初始化
  // 初始化 Data 类
  await Data.init();

  SteamLooper.init().start();
  SteamDownloadListener.init();

  print("Initializing Steam client...");
  SteamClient.init();
  print("Steam client initialized successfully.");

  // 初始化Steam全局变量
  SteamGlobals.init();
  print("Steam globals initialized successfully.");

  SteamClient.instance.steamUserStats.requestCurrentStats();

  runApp(const MyApp());
}

// 新实例应用窗口 - 用于在新进程中启动游戏
class GameInstanceApp extends StatefulWidget {
  final Map<String, dynamic> arguments;

  const GameInstanceApp({
    Key? key,
    required this.arguments,
  }) : super(key: key);

  @override
  State<GameInstanceApp> createState() => GameInstanceAppState();
}

class GameInstanceAppState extends State<GameInstanceApp> {
  Locale _currentLocale = const Locale('en', 'US');
  late SharedPreferences _prefs;
  bool _shouldOpenFullscreen = false; // 是否应该全屏打开窗口
  bool isFullscreen = false; // 追踪当前是否处于全屏状态 - 公开变量
  bool _initialized = false; // 追踪初始化状态
  bool _useNewWindowForEditing = true; // 新增：是否使用新窗口编辑项目
  bool _useNewWindowForPlaying = true; // 新增：是否使用新窗口打开游戏项目

  // 获取全屏状态
  bool get fullscreen => isFullscreen;
  
  // 设置全屏状态
  set fullscreen(bool value) {
    if (isFullscreen != value) {
      setState(() {
        isFullscreen = value;
      });
    }
  }

  @override
  void initState() {
    super.initState();
    _shouldOpenFullscreen = widget.arguments['shouldOpenFullscreen'] == true;
    isFullscreen = _shouldOpenFullscreen; // 初始化全屏状态

    // 获取项目名称并设置窗口标题
    final projectName = widget.arguments['projectName'] ?? '新游戏窗口';
    if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      windowManager.setTitle(projectName);
    }
    
    // 立即执行初始化，不要等待异步操作
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    try {
      // 初始化设置
      await _loadSettings();

      // 初始化Steam客户端 - 新增代码
      try {
        print("初始化新实例的Steam客户端...");
        // 初始化Data类 - 可能包含一些Steam相关的设置
        await Data.init();
        
        // 使用try-catch来处理可能的重复初始化问题
        try {
          SteamLooper.init().start();
          SteamDownloadListener.init();
          SteamClient.init();
          SteamGlobals.init();
          print("新实例Steam客户端初始化成功");
        } catch (e) {
          // 如果出现"已经初始化"的错误，这是可以接受的
          print("Steam客户端可能已经初始化: $e");
        }
      } catch (e) {
        print("初始化Steam客户端失败: $e");
        // 即使Steam初始化失败，我们也继续应用的其他初始化
      }

      // 标记初始化完成
      if (mounted) {
        setState(() {
          _initialized = true;
        });
      }

      // 添加延迟，确保窗口完全加载后再设置全屏
      await Future.delayed(const Duration(milliseconds: 300));
      
      // 如果需要，则设置全屏模式
      if (_shouldOpenFullscreen) {
        try {
          // 设置为全屏模式
          await _setFullScreen(true);
          print('窗口已设置为全屏模式');
        } catch (e) {
          print('设置全屏模式失败: $e');
        }
      }

      print('GameInstanceApp初始化完成');
    } catch (e) {
      print('GameInstanceApp初始化失败: $e');
    }
  }

  // 设置窗口全屏状态
  Future<void> _setFullScreen(bool isFullScreen) async {
    try {
      if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
        // 使用window_manager设置全屏
        await windowManager.setFullScreen(isFullScreen);
        
        // 确保退出全屏时恢复标题栏
        if (!isFullScreen) {
          await windowManager.setTitleBarStyle(TitleBarStyle.normal);
        }
        
        print('使用window_manager设置全屏模式: $isFullScreen');
      } else {
        // 其他平台使用fullscreen_window
        await FullScreenWindow.setFullScreen(isFullScreen);
        print('使用fullscreen_window设置全屏模式: $isFullScreen');
      }
      
      // 更新状态
      if (mounted) {
        setState(() {
          isFullscreen = isFullScreen;
        });
      }
    } catch (e) {
      print('设置全屏模式出错: $e');
      // 添加短暂延迟后重试一次
      try {
        await Future.delayed(const Duration(milliseconds: 200));
        print('尝试再次设置全屏...');
        
        if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
          await windowManager.setFullScreen(isFullScreen);
          if (!isFullScreen) {
            await windowManager.setTitleBarStyle(TitleBarStyle.normal);
          }
        } else {
          await FullScreenWindow.setFullScreen(isFullScreen);
        }
        
        if (mounted) {
          setState(() {
            isFullscreen = isFullScreen;
          });
        }
      } catch (retryError) {
        print('重试设置全屏模式仍然失败: $retryError');
        rethrow;
      }
    }
  }

  Future<void> _loadSettings() async {
    _prefs = await SharedPreferences.getInstance();
    if (mounted) {
      setState(() {
        final languageCode = _prefs.getString('language') ?? 'en';
        final countryCode = _prefs.getString('countryCode');
        final scriptCode = _prefs.getString('scriptCode');

        if (scriptCode != null) {
          _currentLocale = Locale.fromSubtags(
              languageCode: languageCode,
              scriptCode: scriptCode,
              countryCode: countryCode);
        } else if (countryCode != null) {
          _currentLocale = Locale(languageCode, countryCode);
        } else {
          _currentLocale = Locale(languageCode);
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // 获取项目基本信息以供显示
    final projectName = widget.arguments['projectName'] ?? '新游戏窗口';

    // 确保在界面构建完成后再次检查全屏设置
    if (_initialized && _shouldOpenFullscreen && !isFullscreen) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        // 立即设置全屏，不使用延迟
        _setFullScreen(true);
      });
    }

    return MaterialApp(
      title: projectName,
      debugShowCheckedModeBanner: false,
      locale: _currentLocale,
      theme: ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      localizationsDelegates: const [
        S.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        MaterialLocalizationGomDelegate(),
        CupertinoLocalizationGomDelegate(),
        FallbackMaterialLocalizationDelegate(),
        FallbackCupertinoLocalizationDelegate(),
      ],
      supportedLocales: S.delegate.supportedLocales,
      home: Builder(
        builder: (context) {
          // 确保 ScreenUtil 初始化
          ScreenUtil.init(context);

          if (!_initialized) {
            // 显示加载指示器，等待初始化完成
            return Scaffold(
              body: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 20),
                    Text(
                      '正在加载 $projectName...',
                      style: TextStyle(fontSize: 18),
                    ),
                  ],
                ),
              ),
            );
          }

          // 从参数中获取项目路径和创意工坊ID，提供默认值防止空值异常
          final projectPath = widget.arguments['projectPath']?.toString() ?? '';
          final workshopItemId =
              widget.arguments['workshopItemId']?.toString() ?? '0';

          // 检查参数是否有效
          if (projectPath.isEmpty) {
            return Center(
              child: Text(
                S.of(context).invalidProjectPath,
                style: const TextStyle(color: Colors.red),
              ),
            );
          }

          // 检查是编辑模式还是播放模式
          final mode = widget.arguments['mode']?.toString() ?? 'play';

          // 如果是编辑模式，打开CreateGameView
          if (mode == 'edit') {
            final initialProjectName =
                widget.arguments['initialProjectName']?.toString();
            print('打开编辑模式，项目名称: $initialProjectName, 项目路径: $projectPath');

            // 直接使用CreateGameView，而不是EditorView
            // 传递projectPath确保项目正确加载
            return CreateGameView(
              initialProjectName: initialProjectName,
              projectPath: projectPath,  // 传递项目路径
              key: ValueKey('editor-${initialProjectName ?? "new"}'),
              enableSteamFeatures: true, // 启用Steam功能，包括创意工坊
            );
          }

          // 播放模式：使用 TitleScreen 作为中间界面
          return FutureProvider<FragmentPrograms?>(
            create: (context) => loadFragmentPrograms(),
            initialData: null,
            child: TitleScreen(
              projectPath: projectPath,
              projectName: widget.arguments['projectName'] ?? '',
              workshopItemId: workshopItemId,
              onStartPressed: () {
                // 当用户点击"开始"按钮时，打开游戏项目
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => OpenGameView(
                      projectPath: projectPath,
                      projectName: widget.arguments['projectName'] ?? '',
                      workshopItemId: workshopItemId,
                    ),
                  ),
                );
              },
            ),
          );
        },
      ),
    );
  }
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => MyAppState();
}

// Make this public so it can be accessed
class MyAppState extends State<MyApp> {
  Locale _currentLocale = const Locale('en', 'US');
  bool _isFullScreen = false;
  bool _isVerticalLayout = false;
  bool _newWindowFullScreen = false;
  bool _useNewWindowForEditing = true; // 新增：是否使用新窗口编辑项目
  bool _useNewWindowForPlaying = true; // 新增：是否使用新窗口打开游戏项目
  bool _isPublicMode = false; // 新增：是否启用公共场所模式/直播模式
  late SharedPreferences _prefs;

  bool get isVerticalLayout => _isVerticalLayout;
  bool get isFullScreen => _isFullScreen;
  bool get newWindowFullScreen => _newWindowFullScreen;
  bool get useNewWindowForEditing => _useNewWindowForEditing; // 新增：获取是否使用新窗口编辑项目
  bool get useNewWindowForPlaying => _useNewWindowForPlaying; // 新增：获取是否使用新窗口打开游戏项目
  bool get isPublicMode => _isPublicMode; // 新增：获取是否启用公共场所模式

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  // 设置本地化窗口标题的方法
  void _updateWindowTitle(BuildContext context) {
    if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      try {
        final localizedTitle = S.of(context).gameTitle;
        windowManager.setTitle(localizedTitle);
        print('窗口标题已更新为: $localizedTitle');
      } catch (e) {
        print('设置窗口标题时出错: $e');
      }
    }
  }

  Future<void> _loadSettings() async {
    _prefs = await SharedPreferences.getInstance();
    setState(() {
      _isFullScreen = _prefs.getBool('isFullScreen') ?? false;
      _isVerticalLayout = _prefs.getBool('isVerticalLayout') ?? false;
      _newWindowFullScreen = _prefs.getBool('newWindowFullScreen') ?? false;
      _useNewWindowForEditing = _prefs.getBool('useNewWindowForEditing') ?? true;
      _useNewWindowForPlaying = _prefs.getBool('useNewWindowForPlaying') ?? true;
      _isPublicMode = _prefs.getBool('isPublicMode') ?? false; // 新增：加载公共场所模式设置
      final languageCode = _prefs.getString('language') ?? 'en';
      final countryCode = _prefs.getString('countryCode');
      final scriptCode = _prefs.getString('scriptCode');
      
      // 根据保存的设置应用全屏模式
      if (_isFullScreen && (Platform.isWindows || Platform.isLinux || Platform.isMacOS)) {
        // 使用window_manager插件实现真全屏
        windowManager.setFullScreen(_isFullScreen);
      } else if (_isFullScreen && Platform.isAndroid) {
        // 对于移动设备
        SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
      } else if (!_isFullScreen && Platform.isAndroid) {
        SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
      }

      if (scriptCode != null) {
        _currentLocale = Locale.fromSubtags(
            languageCode: languageCode,
            scriptCode: scriptCode,
            countryCode: countryCode);
      } else if (countryCode != null) {
        _currentLocale = Locale(languageCode, countryCode);
      } else {
        _currentLocale = Locale(languageCode);
      }
    });
  }

  void toggleVerticalLayout() {
    setState(() {
      _isVerticalLayout = !_isVerticalLayout;
      print(
          'Layout changed to: ${_isVerticalLayout ? 'Vertical' : 'Horizontal'}');
      _prefs.setBool('isVerticalLayout', _isVerticalLayout);
    });
  }

  void toggleFullScreen() {
    setState(() {
      _isFullScreen = !_isFullScreen;
      print('Fullscreen mode changed to: ${_isFullScreen ? 'On' : 'Off'}');
      _prefs.setBool('isFullScreen', _isFullScreen);
      
      // 立即应用全屏设置
      if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
        windowManager.setFullScreen(_isFullScreen);
        
        // 确保退出全屏时恢复正常标题栏
        if (!_isFullScreen) {
          windowManager.setTitleBarStyle(TitleBarStyle.normal);
        }
      } else if (Platform.isAndroid) {
        if (_isFullScreen) {
          SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
        } else {
          SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
        }
      }
    });
  }
  
  void toggleAutoFullScreenVideo() {
    setState(() {
      _newWindowFullScreen = !_newWindowFullScreen;
      print(
          'New window fullscreen mode changed to: ${_newWindowFullScreen ? 'On' : 'Off'}');
      _prefs.setBool('newWindowFullScreen', _newWindowFullScreen);
    });
  }

  void updateLocale(Locale locale) {
    setState(() {
      _currentLocale = locale;
    });
    // 保存语言设置到SharedPreferences
    _prefs.setString('language', locale.languageCode);
    if (locale.countryCode != null) {
      _prefs.setString('countryCode', locale.countryCode!);
    }
    if (locale.scriptCode != null) {
      _prefs.setString('scriptCode', locale.scriptCode!);
    }
  }

  void toggleNewWindowFullScreen() {
    setState(() {
      _newWindowFullScreen = !_newWindowFullScreen;
      print('新窗口全屏模式已切换为: ${_newWindowFullScreen ? '开启' : '关闭'}');
      // 立即保存设置
      _prefs.setBool('newWindowFullScreen', _newWindowFullScreen);
    });
  }

  void toggleUseNewWindowForEditing() {
    setState(() {
      _useNewWindowForEditing = !_useNewWindowForEditing;
      print('使用新窗口编辑模式已切换为: ${_useNewWindowForEditing ? '开启' : '关闭'}');
      // 立即保存设置
      _prefs.setBool('useNewWindowForEditing', _useNewWindowForEditing);
    });
  }

  void toggleUseNewWindowForPlaying() {
    setState(() {
      _useNewWindowForPlaying = !_useNewWindowForPlaying;
      print('使用新窗口打开游戏项目已切换为: ${_useNewWindowForPlaying ? '开启' : '关闭'}');
      // 立即保存设置
      _prefs.setBool('useNewWindowForPlaying', _useNewWindowForPlaying);
    });
  }

  void togglePublicMode(bool value) {
    setState(() {
      _isPublicMode = value;
      print('公共场所模式/直播模式已切换为: ${_isPublicMode ? '开启' : '关闭'}');
      // 立即保存设置
      _prefs.setBool('isPublicMode', _isPublicMode);
    });
  }

  @override
  Widget build(BuildContext context) {
    return AppStateContainer(
      updateLocale: updateLocale,
      currentLocale: _currentLocale,
      isVerticalLayout: _isVerticalLayout,
      toggleVerticalLayout: toggleVerticalLayout,
      isFullScreen: _isFullScreen,
      toggleFullScreen: toggleFullScreen,
      newWindowFullScreen: _newWindowFullScreen,
      toggleNewWindowFullScreen: toggleNewWindowFullScreen,
      useNewWindowForEditing: _useNewWindowForEditing,
      toggleUseNewWindowForEditing: toggleUseNewWindowForEditing,
      useNewWindowForPlaying: _useNewWindowForPlaying,
      toggleUseNewWindowForPlaying: toggleUseNewWindowForPlaying,
      isPublicMode: _isPublicMode, // 新增：传递公共场所模式状态
      togglePublicMode: togglePublicMode, // 新增：传递公共场所模式切换方法
      child: MaterialApp(
        title: "互动视频游戏引擎", // 使用固定字符串替代S.of(context).gameTitle
        debugShowCheckedModeBanner: false,
        locale: _currentLocale,
        theme: ThemeData(
          primarySwatch: Colors.blue,
          visualDensity: VisualDensity.adaptivePlatformDensity,
        ),
        localizationsDelegates: const [
          S.delegate,
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
          // 添加额外的本地化代理支持
          MaterialLocalizationGomDelegate(),
          CupertinoLocalizationGomDelegate(),
          // 添加通用本地化代理
          FallbackMaterialLocalizationDelegate(),
          FallbackCupertinoLocalizationDelegate(),
        ],
        supportedLocales: S.delegate.supportedLocales,
        // 添加builder属性以便在MaterialApp构建后更新窗口标题
        builder: (context, child) {
          // 当本地化资源加载完成后更新窗口标题
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _updateWindowTitle(context);
          });
          return child ?? const SizedBox.shrink();
        },
        home: const MyHomePage(),
      ),
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key});

  @override
  _MyHomePageState createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  @override
  Widget build(BuildContext context) {
    SizeConfig().init(context);
    ScreenUtil.init(context);

    return const Scaffold(
      body: HomeView(),
    );
  }
}

// Add this class to main.dart
class AppStateContainer extends InheritedWidget {
  final Function(Locale) updateLocale;
  final Locale currentLocale;
  final bool isVerticalLayout;
  final Function() toggleVerticalLayout;
  final bool isFullScreen;
  final Function() toggleFullScreen;
  final bool newWindowFullScreen;
  final Function() toggleNewWindowFullScreen;
  final bool useNewWindowForEditing; // 新增：是否使用新窗口编辑项目
  final Function() toggleUseNewWindowForEditing;
  final bool useNewWindowForPlaying; // 新增：是否使用新窗口打开游戏项目
  final Function() toggleUseNewWindowForPlaying;
  final bool isPublicMode; // 新增：是否启用公共场所模式
  final Function(bool) togglePublicMode; // 新增：切换公共场所模式的方法

  const AppStateContainer({
    super.key,
    required super.child,
    required this.updateLocale,
    required this.currentLocale,
    required this.isVerticalLayout,
    required this.toggleVerticalLayout,
    required this.isFullScreen,
    required this.toggleFullScreen,
    required this.newWindowFullScreen,
    required this.toggleNewWindowFullScreen,
    required this.useNewWindowForEditing,
    required this.toggleUseNewWindowForEditing,
    required this.useNewWindowForPlaying,
    required this.toggleUseNewWindowForPlaying,
    required this.isPublicMode, // 新增：是否启用公共场所模式
    required this.togglePublicMode, // 新增：切换公共场所模式的方法
  });

  static AppStateContainer of(BuildContext context, {bool throwOnError = true}) {
    final container = context.dependOnInheritedWidgetOfExactType<AppStateContainer>();
    if (container == null) {
      if (throwOnError) {
        throw FlutterError('AppStateContainer.of() called with a context that does not contain an AppStateContainer.\n'
            'No AppStateContainer ancestor could be found starting from the context that was passed to AppStateContainer.of().');
      } else {
        // 返回一个带有默认值的AppStateContainer
        // 注意：这将创建一个不与UI树连接的独立实例，仅用于避免空值异常
        return AppStateContainer(
          child: const SizedBox.shrink(),
          updateLocale: (_) {},
          currentLocale: const Locale('en', 'US'),
          isVerticalLayout: false,
          toggleVerticalLayout: () {},
          isFullScreen: false,
          toggleFullScreen: () {},
          newWindowFullScreen: false,
          toggleNewWindowFullScreen: () {},
          useNewWindowForEditing: true,
          toggleUseNewWindowForEditing: () {},
          useNewWindowForPlaying: true,
          toggleUseNewWindowForPlaying: () {},
          isPublicMode: false, // 新增：默认不启用公共场所模式
          togglePublicMode: (_) {}, // 新增：空方法作为默认值
        );
      }
    }
    return container;
  }

  @override
  bool updateShouldNotify(AppStateContainer oldWidget) {
    return currentLocale != oldWidget.currentLocale ||
        isVerticalLayout != oldWidget.isVerticalLayout ||
        isFullScreen != oldWidget.isFullScreen ||
        newWindowFullScreen != oldWidget.newWindowFullScreen ||
        useNewWindowForEditing != oldWidget.useNewWindowForEditing ||
        useNewWindowForPlaying != oldWidget.useNewWindowForPlaying ||
        isPublicMode != oldWidget.isPublicMode; // 新增：检查公共场所模式是否变更
  }
}
