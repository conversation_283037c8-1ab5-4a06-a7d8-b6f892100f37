import 'dart:io';
import 'dart:typed_data';

import 'package:file_picker/file_picker.dart';
import 'package:flutter_flow_chart/flutter_flow_chart.dart';
import 'package:path_provider/path_provider.dart' as path;
import '../../../utils/steam_ex.dart';

Future<void> saveDashboard(Dashboard dashboard, [Directory? appDocDir]) async {
  final Directory directory = appDocDir ?? await path.getApplicationDocumentsDirectory();
  dashboard.saveDashboard('${directory.path}/FLOWCHART.json');
}

/// Load dashboard from file
Future<void> loadDashboard(Dashboard dashboard, [Directory? appDocDir]) async {
  final Directory directory = appDocDir ?? await path.getApplicationDocumentsDirectory();
  dashboard.loadDashboard('${directory.path}/FLOWCHART.json');
}

/// Pick image
Future<Uint8List?> pickImageBytes() async {
  final pickResult = await FilePicker.platform.pickFiles(
    type: FileType.image,
  );
  if (pickResult == null) return null;
  return File(pickResult.files.single.path!).readAsBytesSync();
}

/// Pick Video
Future<String?> pickVideoBytes() async {
  final pickResult = await FilePicker.platform.pickFiles(
    type: FileType.custom,
    allowedExtensions: allowedVideoFormats,
  );
  if (pickResult == null) return null;
  //返回视频文件绝对路径 TODO 返回视频预览图
  return pickResult.files.single.path!;
}