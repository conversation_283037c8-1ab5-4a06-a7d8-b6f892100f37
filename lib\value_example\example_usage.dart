import 'package:flutter/material.dart';
import '../UI/shared/value_change_indicator.dart';

/// 示例：如何使用ValueChangeIndicator组件
class ValueChangeExample extends StatefulWidget {
  const ValueChangeExample({Key? key}) : super(key: key);

  @override
  State<ValueChangeExample> createState() => _ValueChangeExampleState();
}

class _ValueChangeExampleState extends State<ValueChangeExample> {
  int _value = 50;
  ValueChangeType? _lastChangeType;
  String? _variableName;
  bool _showIndicator = false;

  void _incrementValue() {
    setState(() {
      _value += 10;
      _lastChangeType = ValueChangeType.increase;
      _variableName = '生命值';
      _showIndicator = true;
      
      // 设置定时器自动隐藏提示框
      Future.delayed(const Duration(milliseconds: 800), () {
        if (mounted) {
          setState(() {
            _showIndicator = false;
          });
        }
      });
    });
  }

  void _decrementValue() {
    setState(() {
      _value -= 10;
      _lastChangeType = ValueChangeType.decrease;
      _variableName = '能量值';
      _showIndicator = true;
      
      // 设置定时器自动隐藏提示框
      Future.delayed(const Duration(milliseconds: 800), () {
        if (mounted) {
          setState(() {
            _showIndicator = false;
          });
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('数值变化提示示例'),
      ),
      body: Stack(
        children: [
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  '当前数值: $_value',
                  style: const TextStyle(fontSize: 24),
                ),
                const SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ElevatedButton(
                      onPressed: _decrementValue,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                      ),
                      child: const Text('减少能量值'),
                    ),
                    const SizedBox(width: 20),
                    ElevatedButton(
                      onPressed: _incrementValue,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                      ),
                      child: const Text('增加生命值'),
                    ),
                  ],
                ),
                const SizedBox(height: 40),
                const Text(
                  '点击上面的按钮来测试数值变化提示框',
                  style: TextStyle(fontSize: 16, color: Colors.grey),
                ),
              ],
            ),
          ),
          
          // 显示数值变化提示框
          if (_showIndicator && _lastChangeType != null)
            Positioned(
              top: 100,
              right: 20,
              child: ValueChangeIndicator(
                changeType: _lastChangeType!,
                valueName: _variableName,
              ),
            ),
        ],
      ),
    );
  }
}

/// 示例：如何在实际场景中使用ValueChangeIndicator
/// 
/// ```dart
/// // 在需要显示数值变化提示的地方
/// void _updateValue(int newValue, String variableName) {
///   final ValueChangeType? changeType;
///   
///   if (newValue > _currentValue) {
///     changeType = ValueChangeType.increase;
///   } else if (newValue < _currentValue) {
///     changeType = ValueChangeType.decrease;
///   } else {
///     changeType = null; // 数值未变化，不显示提示
///   }
///   
///   setState(() {
///     _currentValue = newValue;
///     
///     if (changeType != null) {
///       _showValueChangeIndicator(changeType, variableName);
///     }
///   });
/// }
/// 
/// void _showValueChangeIndicator(ValueChangeType type, String variableName) {
///   // 将提示框添加到Overlay
///   final overlayState = Overlay.of(context);
///   final overlayEntry = OverlayEntry(
///     builder: (context) => Positioned(
///       top: 100,
///       right: 20,
///       child: ValueChangeIndicator(
///         changeType: type,
///         valueName: variableName,
///       ),
///     ),
///   );
///   
///   overlayState.insert(overlayEntry);
///   
///   // 动画完成后自动移除
///   Future.delayed(const Duration(milliseconds: 1000), () {
///     overlayEntry.remove();
///   });
/// }
/// ``` 