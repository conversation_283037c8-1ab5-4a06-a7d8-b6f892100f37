import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ve/extensions/size_extension.dart';
import 'package:ve/generated/l10n.dart';
import 'package:stacked/stacked.dart';
import 'package:steamworks/steamworks.dart';
import 'package:ve/utils/steam_ex.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:ve/utils/workshop_path.dart';
import 'package:ve/utils/steam_file.dart';
import 'package:ve/utils/window_manager.dart';
import 'workshop_viewmodel.dart';
import 'dart:io';
import 'dart:async';
import '../open/open_game_view.dart';
import '../../../utils/steam_tags.dart';
import 'package:ve/main.dart'; // 导入main.dart以使用AppStateContainer
import 'package:provider/provider.dart'; // 导入 provider
import 'package:ve/next-gen-ui/assets.dart'; // 导入 assets
import 'package:ve/next-gen-ui/title_screen/title_screen.dart'; // 导入 TitleScreen

class WorkshopListView extends StatefulWidget {
  final WorkshopViewModel model;

  WorkshopListView({required this.model});

  @override
  _WorkshopListViewState createState() => _WorkshopListViewState();
}

class _WorkshopListViewState extends State<WorkshopListView> {
  // 添加Map来存储每个物品的订阅状态和点赞收藏状态
  Map<String, bool> subscriptionStatus = {};
  Map<String, bool> likeStatus = {};
  Map<String, bool> favoriteStatus = {};
  final TextEditingController _filterController = TextEditingController();
  final Map<String, bool> _hoverStates = {};
  // 添加一个Map来跟踪哪些项目需要强制显示下载按钮，不依赖于鼠标悬停
  final Map<String, bool> _forceShowDownloadButton = {};
  
  // 保存ScaffoldMessengerState的引用，以便在异步操作后安全地使用
  late ScaffoldMessengerState? _scaffoldMessenger;
  late String _unsubscribeSuccessMsg;
  late String _unsubscribeFailedMsg;
  late String _unsubscribeTitle;
  late String _confirmUnsubscribeMsg;
  late String _cancelMsg;
  late String _unsubscribeButtonText;
  late String _subscribedText;
  late String _subscribeText;
  late String _subscribeFailedMsg;

  // 添加一个Map来跟踪正在下载的项目
  final Map<String, bool> _downloadingItems = {};
  
  // 添加一个Map用于跟踪每个项目的下载进度
  final Map<String, StreamController<double>> _downloadProgressControllers = {};
  
  // 初始化已有的下载中项目
  void _initDownloadingItems() {
    for (var item in widget.model.filteredWorkshops) {
      final itemId = int.parse(item.id);
      final steamFile = SteamSimpleFile(id: itemId);
      steamFile.load().then((_) {
        if (steamFile.isDownLoading && mounted) {
          setState(() {
            _downloadingItems[item.id] = true;
            _forceShowDownloadButton[item.id] = true;
          });
          _startDownloadProgressUpdate(itemId);
        }
      });
    }
  }

  @override
  void initState() {
    super.initState();
    _filterController.addListener(_filterWorkshops);
    _initSubscriptionStatus();
    _initLikeAndFavoriteStatus();
    // 初始化Steam下载监听器
    SteamDownloadListener.init();
    // 初始化下载中项目状态
    _initDownloadingItems();
  }
  
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 保存ScaffoldMessengerState的引用
    _scaffoldMessenger = ScaffoldMessenger.of(context);
    _unsubscribeSuccessMsg = S.of(context).unsubscribeSuccess;
    _unsubscribeFailedMsg = S.of(context).unsubscribeFailed;
    _unsubscribeTitle = S.of(context).unsubscribe;
    _confirmUnsubscribeMsg = S.of(context).confirmUnsubscribe;
    _cancelMsg = S.of(context).cancel;
    _unsubscribeButtonText = S.of(context).unsubscribe;
    _subscribedText = S.of(context).Subscribed;
    _subscribeText = S.of(context).Subscribe;
    _subscribeFailedMsg = S.of(context).subscribeFailed;
  }
  
  @override
  void dispose() {
    // 关闭所有进度流控制器
    for (var controller in _downloadProgressControllers.values) {
      if (!controller.isClosed) {
        controller.close();
      }
    }
    _downloadProgressControllers.clear();
    _filterController.dispose();
    super.dispose();
  }

  // 初始化订阅状态
  Future<void> _initSubscriptionStatus() async {
    for (var item in widget.model.filteredWorkshops) {
      final isSubscribed = await SteamClient.instance.isSubscribed(int.parse(item.id));
      setState(() {
        subscriptionStatus[item.id] = isSubscribed;
      });
    }
  }

  // 初始化点赞和收藏状态
  Future<void> _initLikeAndFavoriteStatus() async {
    for (var item in widget.model.filteredWorkshops) {
      final itemId = int.parse(item.id);
      final isLiked = await SteamClient.instance.hasVotedUp(itemId);
      final isFavorited = await SteamClient.instance.isFavorited(itemId);
      setState(() {
        likeStatus[item.id] = isLiked;
        favoriteStatus[item.id] = isFavorited;
      });
    }
  }

  void _filterWorkshops() {
    widget.model.filterWorkshops(_filterController.text);
  }

  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  Widget _buildStatItem({required WorkshopItem item, required IconData icon, required int value, required Color color, bool isActive = false}) {
    return Flexible(
      child: InkWell(
        onTap: () async {
          if (icon == Icons.thumb_up_outlined) {
            final itemId = int.parse(item.id);
            final isLiked = likeStatus[item.id] ?? false;
            try {
              if (!isLiked) {
                await SteamClient.instance.voteUp(itemId);
                setState(() {
                  likeStatus[item.id] = true;
                  item.likes++;
                });
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text(S.of(context).likeSuccess)),
                );
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text(S.of(context).alreadyLiked)),
                );
              }
            } catch (e) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('${S.of(context).likeFailed}: $e')),
              );
            }
          } else if (icon == Icons.star_outline) {
            final itemId = int.parse(item.id);
            final isFavorited = favoriteStatus[item.id] ?? false;
            try {
              if (!isFavorited) {
                await SteamClient.instance.addToFavorites(itemId);
                setState(() {
                  favoriteStatus[item.id] = true;
                  item.favorites++;
                });
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text(S.of(context).favoriteSuccess)),
                );
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text(S.of(context).alreadyFavorited)),
                );
              }
            } catch (e) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('${S.of(context).favoriteFailed}: $e')),
              );
            }
          }
        },
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
          margin: const EdgeInsets.symmetric(horizontal: 4),
          constraints: BoxConstraints(maxWidth: 80),
          decoration: BoxDecoration(
            color: isActive ? color.withOpacity(0.2) : color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isActive ? color.withOpacity(0.4) : color.withOpacity(0.2),
              width: 1,
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, size: 14, color: color),
              const SizedBox(width: 4),
              Flexible(
                child: Text(
                  value.toString(),
                  style: TextStyle(
                    fontSize: 12,
                    color: color,
                    fontWeight: FontWeight.bold,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final steamClient = SteamClient.instance;
    return ViewModelBuilder<WorkshopViewModel>.reactive(
      viewModelBuilder: () => widget.model,
      builder: (context, model, child) => Material(
        color: Colors.transparent,
        child: Container(
          width: ScreenUtil().screenWidth - 21.width,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 搜索栏
                TextField(
                  controller: _filterController,
                  decoration: InputDecoration(
                    labelText: S.of(context).SearchWorkshop,
                    border: OutlineInputBorder(),
                    isDense: true,
                  ),
                ),
                const SizedBox(height: 16),
                
                // 排序选项
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      S.of(context).sortBy,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Colors.grey[700],
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: [
                          _buildSortChip(
                            context: context,
                            icon: Icons.people_outline,
                            label: S.of(context).sortBySubscribers,
                            sortBy: SortBy.subscribers,
                            model: model,
                          ),
                          _buildSortChip(
                            context: context,
                            icon: Icons.thumb_up_outlined,
                            label: S.of(context).sortByVote,
                            sortBy: SortBy.likes,
                            model: model,
                          ),
                          _buildSortChip(
                            context: context,
                            icon: Icons.star_outline,
                            label: S.of(context).sortByFavorites,
                            sortBy: SortBy.favorites,
                            model: model,
                          ),
                          _buildSortChip(
                            context: context,
                            icon: Icons.calendar_today_outlined,
                            label: S.of(context).sortByPublishDate,
                            sortBy: SortBy.publishDate,
                            model: model,
                          ),
                          _buildSortChip(
                            context: context,
                            icon: Icons.update_outlined,
                            label: S.of(context).sortByUpdateDate,
                            sortBy: SortBy.updateDate,
                            model: model,
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 8),
                    IconButton(
                      icon: Icon(
                        model.sortAscending ? Icons.arrow_upward : Icons.arrow_downward,
                        size: 20,
                        color: Theme.of(context).primaryColor,
                      ),
                      onPressed: () {
                        model.toggleSortDirection();
                      },
                      tooltip: S.of(context).toggleSortDirection,
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                
                // 分类过滤
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      // 订阅状态
                      ChoiceChip(
                        label: Text(S.of(context).Subscribed),
                        selected: model.subscriptionStatus == TagSubscriptionStatus.subscribed,
                        onSelected: (selected) {
                          model.setSubscriptionStatus(selected ? TagSubscriptionStatus.subscribed : null);
                        },
                      ),
                      const SizedBox(width: 8),
                      ChoiceChip(
                        label: Text(S.of(context).Unsubscribed),
                        selected: model.subscriptionStatus == TagSubscriptionStatus.unsubscribed,
                        onSelected: (selected) {
                          model.setSubscriptionStatus(selected ? TagSubscriptionStatus.unsubscribed : null);
                        },
                      ),
                      const SizedBox(width: 8),
                      // 年龄评级
                      ChoiceChip(
                        label: Text(S.of(context).Everyone),
                        selected: model.ageRating == TagAgeRating.everyone,
                        onSelected: (selected) {
                        model.setAgeRating(selected ? TagAgeRating.everyone : null);
                        },
                      ),
                      const SizedBox(width: 8),
                      // 只在非公共场所模式下显示NSFW标签
                      if (!model.isPublicMode) 
                        Row(
                          children: [
                            ChoiceChip(
                              label: Text(S.of(context).NSFW),
                              selected: model.ageRating == TagAgeRating.nsfw,
                              onSelected: (selected) {
                                model.setAgeRating(selected ? TagAgeRating.nsfw : null);
                              },
                            ),
                            const SizedBox(width: 8),
                          ],
                        ),
                      
                      // 风格
                      ChoiceChip(
                        label: Text(S.of(context).Anime),
                        selected: model.style == TagStyle.anime,
                        onSelected: (selected) {
                          model.setStyle(selected ? TagStyle.anime : null);
                        },
                      ),
                      const SizedBox(width: 8),
                      ChoiceChip(
                        label: Text(S.of(context).Realistic),
                        selected: model.style == TagStyle.realistic,
                        onSelected: (selected) {
                          model.setStyle(selected ? TagStyle.realistic : null);
                        },
                      ),
                      const SizedBox(width: 8),
                      ChoiceChip(
                        label: Text(S.of(context).Pixel),
                        selected: model.style == TagStyle.pixel,
                        onSelected: (selected) {
                          model.setStyle(selected ? TagStyle.pixel : null);
                        },
                      ),
                      const SizedBox(width: 8),
                      
                      // 形状
                      ChoiceChip(
                        label: Text(S.of(context).Landscape),
                        selected: model.shape == TagShape.landscape,
                        onSelected: (selected) {
                        model.setShape(selected ? TagShape.landscape : null);
                        },
                      ),
                      const SizedBox(width: 8),
                      ChoiceChip(
                        label: Text(S.of(context).Portrait),
                        selected: model.shape == TagShape.portrait,
                        onSelected: (selected) {
                          model.setShape(selected ? TagShape.portrait : null);
                        },
                      ),
                      const SizedBox(width: 8),
                      ChoiceChip(
                        label: Text(S.of(context).Square),
                        selected: model.shape == TagShape.square,
                        onSelected: (selected) {
                          model.setShape(selected ? TagShape.square : null);
                        },
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  S.of(context).workshop,
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontFamily: 'WorkSans',
                  ),
                ),
                const SizedBox(height: 16),
                Expanded(
                  child: SingleChildScrollView(
                    child: Wrap(
                      spacing: 12,
                      runSpacing: 12,
                      children: model.filteredWorkshops.map((item) => _buildWorkshopCard(item)).toList(),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSortChip({
    required BuildContext context,
    required IconData icon,
    required String label,
    required SortBy sortBy,
    required WorkshopViewModel model,
  }) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      child: ChoiceChip(
        label: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 18,
              color: model.sortBy == sortBy 
                ? Theme.of(context).primaryColor 
                : Colors.grey[700],
            ),
            const SizedBox(width: 6),
            Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: model.sortBy == sortBy
                  ? Theme.of(context).primaryColor
                  : Colors.grey[700],
              ),
            ),
          ],
        ),
        selected: model.sortBy == sortBy,
        onSelected: (selected) {
          model.setSortBy(selected ? sortBy : null);
        },
        selectedColor: Theme.of(context).primaryColor.withOpacity(0.2),
        backgroundColor: Colors.grey[100],
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
                        elevation: model.sortBy == sortBy ? 2 : 0,
      ),
    );
  }

  Widget _buildWorkshopCard(WorkshopItem item) {
    return InkWell(
      onTap: () async {
        // 获取最新的订阅状态 - 使用item.isSubscribed而不是调用API
        final isSubscribed = subscriptionStatus[item.id] ?? item.isSubscribed;
        // 更新状态
        setState(() {
          subscriptionStatus[item.id] = isSubscribed;
          item.isSubscribed = isSubscribed;
        });
        
        if (!isSubscribed) {
          // 显示未订阅提示弹窗
          showDialog(
            context: context, 
            builder: (context) => AlertDialog(
              title: Text(S.of(context).Unsubscribed),
              content: Text(S.of(context).itemNotSubscribed),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text(S.of(context).cancel),
                ),
                ElevatedButton(
                  onPressed: () async {
                    Navigator.pop(context);
                    try {
                      await SteamClient.instance.subscribe(int.parse(item.id));
                      setState(() {
                        subscriptionStatus[item.id] = true;
                        item.isSubscribed = true;
                      });
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text(S.of(context).subscribeSuccess)),
                      );
                    } catch (e) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('${S.of(context).subscribeFailed}$e')),
                      );
                    }
                  },
                  child: Text(S.of(context).Subscribe),
                ),
              ],
            ),
          );
          return;
        }
        
        // 检查物品是否正在下载中
        final itemId = int.parse(item.id);
        final steamFile = SteamSimpleFile(id: itemId);
        await steamFile.load();
        
        if (steamFile.isDownLoading) {
          // 如果正在下载中，显示提示信息
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(S.of(context).downloadingPleaseWait)),
          );
          return;
        }
        
        // 获取工作坊项目路径
        final path = WorkshopPath.getShopPath(item.id);
        final directory = Directory(path);
        
        // 检查目录是否存在且不为空
        if (!await directory.exists() || (await directory.list().isEmpty)) {
          // 如果目录不存在或为空，说明下载可能刚完成但文件尚未解压完成
          // 使用安全的方式显示提示，避免使用可能已失效的context
          if (mounted && context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(S.of(context).filePreparingPleaseRetry)),
            );
          } else {
            // 使用保存的ScaffoldMessenger引用
            _scaffoldMessenger?.showSnackBar(
              SnackBar(content: Text(_getLocalizedMessage('filePreparingPleaseRetry'))),
            );
          }
          return;
        }
        
        // 已订阅且下载完成，打开项目
        final workshopPath = WorkshopPath.getShopPath(item.id);
        try {
          // 获取是否使用新窗口打开游戏项目的设置，使用安全的方式
          bool useNewWindow = false;
          if (mounted && context.mounted) {
            final appState = AppStateContainer.of(context, throwOnError: false);
            if (appState != null) {
              useNewWindow = appState.useNewWindowForPlaying;
            }
          }
          print('打开创意工坊项目路径: $path, 使用新窗口: $useNewWindow');
          
          if (useNewWindow) {
            // 使用新窗口打开
            final size = mounted && context.mounted ? MediaQuery.of(context).size : const Size(1280, 720);
            await WindowManager.openGameInNewWindow(
              projectPath: path,
              projectName: item.title,
              workshopItemId: item.id,
              windowSize: size,
            );
          } else {
            // 使用当前窗口打开，先显示TitleScreen
            // 确保context有效
            if (mounted && context.mounted) {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => FutureProvider<FragmentPrograms?>(
                    create: (context) => loadFragmentPrograms(),
                    initialData: null,
                    child: TitleScreen(
                      projectPath: path,
                      projectName: item.title,
                      workshopItemId: item.id,
                      showBackButton: true,
                      onStartPressed: () {
                        // 点击开始按钮后导航到OpenGameView
                        Navigator.pushReplacement(
                          context,
                          MaterialPageRoute(
                            builder: (context) => OpenGameView(
                              projectPath: path,
                              projectName: item.title,
                              workshopItemId: item.id,
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ).then((_) {
                // 当从游戏界面返回时，可以做一些刷新操作
                if (mounted) {
                  setState(() {
                    // 刷新工作坊列表状态
                  });
                }
              });
            }
          }
        } catch (e) {
          print('打开游戏项目失败: $e');
          // 显示错误提示（使用安全的方式）
          if (mounted && context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('${S.of(context).openGameError}: $e')),
            );
          } else {
            // 使用保存的ScaffoldMessenger引用
            _scaffoldMessenger?.showSnackBar(
              SnackBar(content: Text('${_getLocalizedMessage('openGameError')}: $e')),
            );
          }
        }
      },
      child: SizedBox(
        width: 200,
        child: Card(
        semanticContainer: false,
        clipBehavior: Clip.antiAlias,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        elevation: 2,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            MouseRegion(
              child: Stack(
                children: [
                  Container(
                    height: 120,
                    width: double.infinity,
                    child: Image.network(
                      item.cover,
                      fit: BoxFit.cover,
                      loadingBuilder: (context, child, loadingProgress) {
                        if (loadingProgress == null) return child;
                        return Center(
                          child: CircularProgressIndicator(
                            value: loadingProgress.expectedTotalBytes != null
                                ? loadingProgress.cumulativeBytesLoaded / loadingProgress.expectedTotalBytes!
                                : null,
                          ),
                        );
                      },
                      errorBuilder: (context, error, stackTrace) {
                        return const Center(
                          child: Icon(Icons.error_outline, size: 30, color: Colors.grey),
                        );
                      },
                    ),
                  ),
                  Positioned(
                    top: 8,
                    right: 8,
                    child: AnimatedOpacity(
                      opacity: _hoverStates[item.id] ?? false ? 1 : 0,
                      duration: Duration(milliseconds: 200),
                      child: _buildDownloadButton(item),
                    ),
                  ),
                ],
              ),
              onEnter: (_) {
                setState(() {
                  _hoverStates[item.id] = true;
                });
              },
              onExit: (_) {
                setState(() {
                  _hoverStates[item.id] = false;
                });
              },
            ),
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item.title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    item.description,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),
                  // 统计信息
                  SizedBox(
                    height: 40,
                    child: Row(
                      children: [
                        _buildStatItem(
                          item: item,
                          icon: Icons.thumb_up_outlined,
                          value: item.likes,
                          color: Colors.blue,
                          isActive: likeStatus[item.id] ?? false,
                        ),
                        _buildStatItem(
                          item: item,
                          icon: Icons.star_outline,
                          value: item.favorites,
                          color: Colors.pink,
                          isActive: favoriteStatus[item.id] ?? false,
                        ),
                        _buildStatItem(
                          item: item,
                          icon: Icons.people_outline,
                          value: item.subscribers,
                          color: Colors.green,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 8),
                  
                  // 时间信息
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.calendar_today, size: 12, color: Colors.grey),
                          const SizedBox(width: 4),
                          Text(
                            _formatDate(item.publishDate),
                            style: TextStyle(
                              fontSize: 10,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                      Row(
                        children: [
                          Icon(Icons.update, size: 12, color: Colors.grey),
                          const SizedBox(width: 4),
                          Text(
                            _formatDate(item.updateDate),
                            style: TextStyle(
                              fontSize: 10,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      ),
    );
  }

  Widget _buildSubscribeButton(WorkshopItem item) {
    final isSubscribed = subscriptionStatus[item.id] ?? false;
    final isDownloading = _downloadingItems[item.id] ?? false;
    final itemId = int.parse(item.id);
    
    // 检查是否正在下载中
    return StatefulBuilder(
      builder: (context, setState) {
        // 如果正在下载中，显示下载进度
        if (isDownloading || SteamDownloadListener.isDownloading(itemId)) {
          return StreamBuilder<double>(
            // 使用Stream而不是FutureBuilder以实现实时更新
            stream: _getDownloadProgressStream(itemId),
            initialData: SteamDownloadListener.getProgress(itemId),
            builder: (context, snapshot) {
              final progress = snapshot.data ?? 0.0;
              
              return AnimatedContainer(
                duration: Duration(milliseconds: 300),
                width: 120,
                height: 36,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(18),
                  gradient: LinearGradient(
                    colors: [Colors.blue[700]!, Colors.blue[500]!],
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.blue.withOpacity(0.3),
                      blurRadius: 8,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: Stack(
                  children: [
                    // 进度条背景
                    ClipRRect(
                      borderRadius: BorderRadius.circular(18),
                      child: LinearProgressIndicator(
                        value: progress,
                        backgroundColor: Colors.transparent,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.blue[300]!),
                        minHeight: 36,
                      ),
                    ),
                    // 文字和图标
                    Positioned.fill(
                      child: Center(
                        child: Text(
                          '${S.of(context).downloading} ${(progress * 100).toStringAsFixed(0)}%',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                    // 进度指示器
                    Center(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(width: 8),
                          SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          ),
                          SizedBox(width: 8),
                          Text(
                            '${(progress * 100).toStringAsFixed(0)}%',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            },
          );
        } 
        // 如果已订阅，显示已订阅按钮
        else if (isSubscribed) {
          return AnimatedContainer(
            duration: Duration(milliseconds: 300),
            width: 120,
            height: 36,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(18),
              gradient: LinearGradient(
                colors: [Colors.green[600]!, Colors.green[400]!],
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.green.withOpacity(0.3),
                  blurRadius: 8,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(18),
                onTap: () async {
                  // 显示确认取消订阅的对话框
                  showDialog(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: Text(_unsubscribeTitle),
                      content: Text(_confirmUnsubscribeMsg),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.pop(context),
                          child: Text(_cancelMsg),
                        ),
                        ElevatedButton(
                          onPressed: () async {
                            Navigator.pop(context);
                            try {
                              await SteamClient.instance.unsubscribe(itemId);
                              if (mounted) {
                                setState(() {
                                  subscriptionStatus[item.id] = false;
                                  item.isSubscribed = false;
                                });
                                // 使用缓存的本地化字符串
                                _scaffoldMessenger?.showSnackBar(
                                  SnackBar(content: Text(_unsubscribeSuccessMsg)),
                                );
                              }
                            } catch (e) {
                              if (mounted) {
                                // 使用缓存的本地化字符串
                                _scaffoldMessenger?.showSnackBar(
                                  SnackBar(content: Text('$_unsubscribeFailedMsg: $e')),
                                );
                              }
                            }
                          },
                          child: Text(_unsubscribeButtonText),
                        ),
                      ],
                    ),
                  );
                },
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.check, size: 16, color: Colors.white),
                    SizedBox(width: 6),
                    Text(
                      _subscribedText,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        } 
        // 如果未订阅，显示订阅按钮
        else {
          return AnimatedContainer(
            duration: Duration(milliseconds: 300),
            width: 120,
            height: 36,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(18),
              gradient: LinearGradient(
                colors: [Theme.of(context).primaryColor, Colors.blue[400]!],
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
              ),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).primaryColor.withOpacity(0.3),
                  blurRadius: 8,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(18),
                onTap: () async {
                  try {
                    await SteamClient.instance.subscribe(itemId);
                    setState(() {
                      subscriptionStatus[item.id] = true;
                      item.isSubscribed = true;
                      // 标记为正在下载中
                      _downloadingItems[item.id] = true;
                    });
                    // 开始下载
                    SteamClient.instance.steamUgc.downloadItem(itemId, true);
                    // 强制刷新UI以显示下载进度
                    _startDownloadProgressUpdate(itemId);
                    // 自动显示下载进度条
                    _showDownloadProgressAfterDelay(item.id);
                  } catch (e) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('$_subscribeFailedMsg: $e')),
                    );
                  }
                },
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.add, size: 16, color: Colors.white),
                    SizedBox(width: 6),
                    Text(
                      _subscribeText,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        }
      },
    );
  }

  // 构建下载按钮或进度条
  Widget _buildDownloadButton(WorkshopItem item) {
    final itemId = int.parse(item.id);
    // 使用item.isSubscribed作为初始值，然后检查subscriptionStatus映射
    final isSubscribed = subscriptionStatus[item.id] ?? item.isSubscribed;
    final isDownloading = _downloadingItems[item.id] ?? false;
    final forceShow = _forceShowDownloadButton[item.id] ?? false;
    
    // 创建一个SteamSimpleFile实例来获取下载状态
    final steamFile = SteamSimpleFile(id: itemId);
    
    return FutureBuilder<void>(
      future: steamFile.load(),
      builder: (context, snapshot) {
        // 如果正在下载中或被强制显示，显示进度条
        if (isDownloading || steamFile.isDownLoading || forceShow || SteamDownloadListener.isDownloading(itemId)) {
          return StreamBuilder<double>(
            // 使用Stream而不是FutureBuilder以实现实时更新
            stream: _getDownloadProgressStream(itemId),
            initialData: SteamDownloadListener.getProgress(itemId),
            builder: (context, snapshot) {
              final progress = snapshot.data ?? 0.0;
              
              // 检查是否正在播放下载完成动画
              final isPlayingCompleteAnimation = _downloadCompleteAnimations[item.id] ?? false;
              
              return AnimatedSwitcher(
                duration: Duration(milliseconds: 500),
                transitionBuilder: (Widget child, Animation<double> animation) {
                  return ScaleTransition(
                    scale: animation,
                    child: FadeTransition(opacity: animation, child: child),
                  );
                },
                child: isPlayingCompleteAnimation
                  ? _buildDownloadCompleteAnimation(context)
                  : AnimatedContainer(
                      key: ValueKey('download-progress-${item.id}'),
                      duration: Duration(milliseconds: 300),
                      width: 120,
                      height: 36,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(18),
                        gradient: LinearGradient(
                          colors: [Theme.of(context).primaryColor, Colors.blue[300]!],
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.blue.withOpacity(0.3),
                            blurRadius: 8,
                            offset: Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Stack(
                        children: [
                          // 进度条填充效果
                          ClipRRect(
                            borderRadius: BorderRadius.circular(18),
                            child: Align(
                              alignment: Alignment.centerLeft,
                              child: AnimatedContainer(
                                duration: Duration(milliseconds: 300),
                                width: 120 * progress,
                                height: 36,
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    colors: [Colors.blue[400]!, Colors.blue[300]!],
                                    begin: Alignment.centerLeft,
                                    end: Alignment.centerRight,
                                  ),
                                ),
                              ),
                            ),
                          ),
                          // 波浪动画效果
                          ClipRRect(
                            borderRadius: BorderRadius.circular(18),
                            child: ShaderMask(
                              shaderCallback: (rect) {
                                return LinearGradient(
                                  begin: Alignment.centerLeft,
                                  end: Alignment.centerRight,
                                  colors: [Colors.white.withOpacity(0.15), Colors.white.withOpacity(0.05)],
                                  stops: [progress, progress],
                                ).createShader(rect);
                              },
                              blendMode: BlendMode.srcIn,
                              child: Container(
                                width: double.infinity,
                                height: double.infinity,
                                color: Colors.white,
                              ),
                            ),
                          ),
                          // 进度指示器
                          Center(
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SizedBox(width: 8),
                                SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                  ),
                                ),
                                SizedBox(width: 8),
                                Text(
                                  '${(progress * 100).toStringAsFixed(0)}%',
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
              );
            },
          );
        } 
        // 如果已订阅，显示已订阅按钮
        else if (isSubscribed) {
          return AnimatedContainer(
            duration: Duration(milliseconds: 300),
            width: 120,
            height: 36,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(18),
              gradient: LinearGradient(
                colors: [Colors.green[600]!, Colors.green[400]!],
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.green.withOpacity(0.3),
                  blurRadius: 8,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(18),
                onTap: () async {
                  // 显示确认取消订阅的对话框
                  showDialog(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: Text(_unsubscribeTitle),
                      content: Text(_confirmUnsubscribeMsg),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.pop(context),
                          child: Text(_cancelMsg),
                        ),
                        ElevatedButton(
                          onPressed: () async {
                            Navigator.pop(context);
                            try {
                              await SteamClient.instance.unsubscribe(itemId);
                              if (mounted) {
                                setState(() {
                                  subscriptionStatus[item.id] = false;
                                  item.isSubscribed = false;
                                });
                                // 使用缓存的本地化字符串
                                _scaffoldMessenger?.showSnackBar(
                                  SnackBar(content: Text(_unsubscribeSuccessMsg)),
                                );
                              }
                            } catch (e) {
                              if (mounted) {
                                // 使用缓存的本地化字符串
                                _scaffoldMessenger?.showSnackBar(
                                  SnackBar(content: Text('$_unsubscribeFailedMsg: $e')),
                                );
                              }
                            }
                          },
                          child: Text(_unsubscribeButtonText),
                        ),
                      ],
                    ),
                  );
                },
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.check, size: 16, color: Colors.white),
                    SizedBox(width: 6),
                    Text(
                      _subscribedText,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        } 
        // 如果未订阅，显示订阅按钮
        else {
          return AnimatedContainer(
            duration: Duration(milliseconds: 300),
            width: 120,
            height: 36,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(18),
              gradient: LinearGradient(
                colors: [Theme.of(context).primaryColor, Colors.blue[400]!],
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
              ),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).primaryColor.withOpacity(0.3),
                  blurRadius: 8,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(18),
                onTap: () async {
                  try {
                    await SteamClient.instance.subscribe(itemId);
                    setState(() {
                      subscriptionStatus[item.id] = true;
                      item.isSubscribed = true;
                      // 标记为正在下载中
                      _downloadingItems[item.id] = true;
                    });
                    // 开始下载
                    SteamClient.instance.steamUgc.downloadItem(itemId, true);
                    // 强制刷新UI以显示下载进度
                    _startDownloadProgressUpdate(itemId);
                    // 自动显示下载进度条
                    _showDownloadProgressAfterDelay(item.id);
                  } catch (e) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('$_subscribeFailedMsg: $e')),
                    );
                  }
                },
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.add, size: 16, color: Colors.white),
                    SizedBox(width: 6),
                    Text(
                      _subscribeText,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        }
      },
    );
  }

  // 存储下载完成动画状态的Map
  final Map<String, bool> _downloadCompleteAnimations = {};
  
  // 创建一个存储下载进度流控制器的Map
  final Map<int, StreamController<double>> _progressControllers = {};
  
  // 获取下载进度的Stream
  Stream<double> _getDownloadProgressStream(int itemId) {
    if (!_progressControllers.containsKey(itemId)) {
      // 创建一个广播流控制器，以便多个部件可以监听同一个下载进度
      _progressControllers[itemId] = StreamController<double>.broadcast();
      _startDownloadProgressUpdate(itemId);
    }
    return _progressControllers[itemId]!.stream;
  }

  void _startDownloadProgressUpdate(int itemId) {
    // 初始化动画状态
    _downloadCompleteAnimations[itemId.toString()] = false;
    
    // 添加下载完成监听器
    SteamDownloadListener.add(itemId, () {
      if (mounted) {
        // 触发下载完成动画
        setState(() {
          _downloadCompleteAnimations[itemId.toString()] = true;
        });
        
        // 向流发送最终进度值
        if (_progressControllers.containsKey(itemId)) {
          _progressControllers[itemId]!.add(1.0);
        }
        
        // 延迟后重置动画状态，显示已订阅按钮
        Future.delayed(Duration(milliseconds: 800), () {
          if (mounted) {
            setState(() {
              _downloadCompleteAnimations[itemId.toString()] = false;
              // 下载完成后，取消强制显示状态
              _forceShowDownloadButton[itemId.toString()] = false;
              // 移除下载标记
              _downloadingItems[itemId.toString()] = false;
            });
            
            // 延迟后关闭流控制器
            if (_progressControllers.containsKey(itemId)) {
              _progressControllers[itemId]!.close();
              _progressControllers.remove(itemId);
            }
          }
        });
      }
    }, once: true);
    
    // 创建一个更快速的定时器，更频繁地更新下载进度
    Timer.periodic(Duration(milliseconds: 200), (timer) {
      if (mounted) {
        final steamFile = SteamSimpleFile(id: itemId);
        steamFile.load().then((_) {
          // 获取当前下载状态
          bool isCurrentlyDownloading = steamFile.isDownLoading || SteamDownloadListener.isDownloading(itemId);
          
          if (!isCurrentlyDownloading) {
            // 增加延迟确认机制，防止误判
            Future.delayed(Duration(milliseconds: 500), () {
              // 再次检查下载状态
              SteamSimpleFile(id: itemId).load().then((value) {
                if (!SteamSimpleFile(id: itemId).isDownLoading && !SteamDownloadListener.isDownloading(itemId)) {
                  timer.cancel();
                  if (_progressControllers.containsKey(itemId) && !_progressControllers[itemId]!.isClosed) {
                    // 如果下载完成但监听器尚未触发，则手动发送完成信号
                    _progressControllers[itemId]!.add(1.0);
                  }
                  // 确保下载完成后更新状态
                  if (mounted) {
                    setState(() {
                      _downloadingItems[itemId.toString()] = false;
                    });
                  }
                }
              });
            });
          } else {
            // 更新下载进度，并通过Stream发送
            steamFile.updateDownloadBytes().then((_) {
              if (mounted && _progressControllers.containsKey(itemId) && !_progressControllers[itemId]!.isClosed) {
                final progress = steamFile.totalBytes > 0 
                  ? steamFile.downloadedBytes / steamFile.totalBytes 
                  : 0.0;
                _progressControllers[itemId]!.add(progress);
              }
            });
          }
        });
      } else {
        timer.cancel();
        // 确保流被关闭
        if (_progressControllers.containsKey(itemId) && !_progressControllers[itemId]!.isClosed) {
          _progressControllers[itemId]!.close();
          _progressControllers.remove(itemId);
        }
      }
    });
  }

  // 构建下载完成动画
  Widget _buildDownloadCompleteAnimation(BuildContext context) {
    return Container(
      key: ValueKey('download-complete'),
      width: 120,
      height: 36,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(18),
        gradient: LinearGradient(
          colors: [Colors.green[400]!, Colors.green[300]!],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.green.withOpacity(0.3),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Center(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.check_circle_outline,
              color: Colors.white,
              size: 20,
            ),
            SizedBox(width: 6),
            Text(
              '完成',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 订阅后立即显示下载进度的方法
  void _showDownloadProgressAfterDelay(String itemId) {
    // 强制显示下载按钮，不依赖于鼠标悬停
    setState(() {
      _forceShowDownloadButton[itemId] = true;
      _downloadingItems[itemId] = true;
    });
    
    // 如果这个项目还没有进度控制器，创建一个
    if (!_downloadProgressControllers.containsKey(itemId)) {
      _downloadProgressControllers[itemId] = StreamController<double>.broadcast();
    }
    
    // 开始监控下载进度
    _startDownloadProgressUpdate(int.parse(itemId));
    
    // 额外保障：即使下载状态检测失败，也确保下载按钮至少显示一段较长时间
    Future.delayed(Duration(seconds: 10), () {
      // 检查下载是否真的完成
      if (mounted) {
        final intItemId = int.parse(itemId);
        SteamSimpleFile(id: intItemId).load().then((value) {
          if (SteamSimpleFile(id: intItemId).isDownLoading || SteamDownloadListener.isDownloading(intItemId)) {
            // 下载仍在进行，保持状态
            setState(() {
              _forceShowDownloadButton[itemId] = true;
              _downloadingItems[itemId] = true;
            });
          }
        });
      }
    });
  }

  // 添加一个方法用于在context无效时安全地获取本地化字符串
  String _getLocalizedMessage(String key) {
    switch(key) {
      case 'filePreparingPleaseRetry':
        return _unsubscribeFailedMsg;
      case 'openGameError':
        return '打开游戏失败';
      default:
        return '操作失败';
    }
  }
}
