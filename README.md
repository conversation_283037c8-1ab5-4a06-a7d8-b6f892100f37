# ve

## Debug

settings > Extensions > Dart > DevTools > Dart:Open Dev Tools = never

```
robocopy "G:\SteamLibrary\steamapps\workshop\content\3464020" "$PWD\..\..\workshop\content\3464020\" /MIR /NJH /NJS /NP /R:3 /W:5
```

## Build

```
Stop-Process -Name "dotnet" -Force
taskkill /F /IM cmake.exe

wget https://github.com/alexmercerind/flutter-windows-ANGLE-OpenGL-ES/releases/download/lastest/ANGLE.7z -O build/windows/x64
wget https://github.com/media-kit/libmpv-win32-video-build/releases/download/2023-09-24/mpv-dev-x86_64-20230924-git-652a1dd.7z -O build/windows/x64

cp "D:\Flutter\ANGLE.7z" "build\windows\x64"
cp "D:\Flutter\mpv-dev-x86_64-20230924-git-652a1dd.7z" "build\windows\x64"
```

```
powershell -Command "(Get-Content -Path 'windows\flutter\ephemeral\.plugin_symlinks\media_kit_libs_windows_video\windows\CMakeLists.txt') -replace 'mpv-dev-x86_64-20230408-git-7ae7fc0.7z', 'mpv-dev-x86_64-20230924-git-652a1dd.7z' | Set-Content -Path 'windows\flutter\ephemeral\.plugin_symlinks\media_kit_libs_windows_video\windows\CMakeLists.txt'"
powershell -Command "(Get-Content -Path 'windows\flutter\ephemeral\.plugin_symlinks\media_kit_libs_windows_video\windows\CMakeLists.txt') -replace '2023-04-08', '2023-09-24' | Set-Content -Path 'windows\flutter\ephemeral\.plugin_symlinks\media_kit_libs_windows_video\windows\CMakeLists.txt'"
powershell -Command "(Get-Content -Path 'windows\flutter\ephemeral\.plugin_symlinks\media_kit_libs_windows_video\windows\CMakeLists.txt') -replace '3d6e3686b5d543dfed6a775b8bff6b14', 'a832ef24b3a6ff97cd2560b5b9d04cd8' | Set-Content -Path 'windows\flutter\ephemeral\.plugin_symlinks\media_kit_libs_windows_video\windows\CMakeLists.txt'"
```

```
$Env:http_proxy="http://127.0.0.1:7890";$Env:https_proxy="http://127.0.0.1:7890"
kill 360
```

## Release

```
dart run steamworks_gen -o mypackage/steamwk/lib/src/generated steam_api.json -t mac
dart run steamworks_gen -o mypackage/steamwk/lib/src/generated steam_api.json -t linux
```

```
flutter build windows --release  --obfuscate --split-debug-info=../table
robocopy . "build\windows\x64\runner\Release" steam_ap*
```

```
flutter build macos --release  --obfuscate --split-debug-info=../table
cp steam_appid.txt build/macos/Build/Products/Release/ve.app/Contents/MacOS
cp libsteam_api.dylib build/macos/Build/Products/Release/ve.app/Contents/Frameworks
```

```
{
    '简体中文': 'zh_CN',
    '英语': 'en',
    '法语': 'fr',
    '意大利语': 'it',
    '德语': 'de',
    '西班牙语': 'es',
    '丹麦语': 'da',
    '乌克兰语': 'uk',
    '俄语': 'ru',
    '保加利亚语': 'bg',
    '匈牙利语': 'hu',
    '印度尼西亚语': 'id',
    '土耳其语': 'tr',
    '希腊语': 'el',
    '挪威语': 'nb',
    '捷克语': 'cs',
    '日语': 'ja',
    '波兰语': 'pl',
    '泰语': 'th',
    '瑞典语': 'sv',
    '繁体中文': 'zh',
    '罗马尼亚语': 'ro',
    '芬兰语': 'fi',
    '荷兰语': 'nl',
    '葡萄牙语 - 巴西': 'pt',
    '葡萄牙语 - 葡萄牙': 'ptpt',
    '越南语': 'vi',
    '阿拉伯语': 'ar',
    '韩语': 'ko',
    '乌兹别克语': 'uz',
    '乌尔都语': 'ur',
    '亚美尼亚语': 'hy',
    '伊博语': 'ig',
    '信德语': 'sd',
    '僧伽罗语': 'si',
    '克罗地亚语': 'hr',
    '冰岛语': 'is',
    '加利西亚语': 'gl',
    '加泰罗尼亚语': 'ca',
    '南非语': 'af',
    '卡纳达语': 'kn',
    '印地语': 'hi',
    '古吉拉特语': 'gu',
    '吉尔吉斯语': 'ky',
    '哈萨克语': 'kk',
    '土库曼语': 'tk',
    '基尼亚卢旺达语': 'rw',
    '塞尔维亚语': 'sr',
    '奥迪亚语': 'or',
    '威尔士语': 'cy',
    '孔卡尼语': 'gom',
    '孟加拉语': 'bn',
    '尼泊尔语': 'ne',
    '巴斯克语': 'eu',
    '希伯来语': 'he',
    '拉脱维亚语': 'lv',
    '提格里尼亚语': 'ti',
    '斯洛伐克语': 'sk',
    '斯洛文尼亚语': 'sl',
    '斯瓦希里语': 'sw',
    '旁遮普语': 'pa',
    '格鲁吉亚语': 'ka',
    '毛利语': 'mi',
    '波斯尼亚语': 'bs',
    '波斯语': 'fa',
    '泰卢固语': 'te',
    '泰米尔语': 'ta',
    '爱尔兰语': 'ga',
    '爱沙尼亚语': 'et',
    '祖鲁语': 'zu',
    '科萨语': 'xh',
    '立陶宛语': 'lt',
    '索托语': 'st',
    '约鲁巴语': 'yo',
    '维吾尔语': 'ug',
    '茨瓦纳语': 'tn',
    '菲律宾语': 'fil',
    '蒙古语': 'mn',
    '豪萨语': 'ha',
    '达利语': 'prs',
    '阿塞拜疆语': 'az',
    '阿姆哈拉语': 'am',
    '阿尔巴尼亚语': 'sq',
    '阿萨姆语': 'as',
    '鞑靼语': 'tt',
    '马其顿语': 'mk',
    '马拉地语': 'mr',
    '马拉雅拉姆语': 'ml',
    '马来语': 'ms',
    '马耳他语': 'mt',
    '高棉语': 'km'
}
```