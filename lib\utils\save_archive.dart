import 'dart:convert';
import 'dart:io';

import 'package:flutter_flow_chart/flutter_flow_chart.dart';
import 'package:path/path.dart' as p;
import 'package:steamworks/steamworks.dart';
import 'package:ve/utils/workshop_path.dart';
import 'steam_globals.dart';
import 'coin_manager.dart';

/// 视频节点队列元素
class VideoQueueNode {
  /// 节点ID
  final String nodeId;
  
  /// 是否已完成观看
  final bool completed;
  
  /// 添加到队列的时间
  final DateTime addedTime;
  
  /// 完成观看的时间
  final DateTime? completedTime;
  
  VideoQueueNode({
    required this.nodeId,
    this.completed = false,
    required this.addedTime,
    this.completedTime,
  });
  
  /// 从JSON创建
  factory VideoQueueNode.fromJson(Map<String, dynamic> json) {
    return VideoQueueNode(
      nodeId: json['nodeId'],
      completed: json['completed'] ?? false,
      addedTime: DateTime.parse(json['addedTime']),
      completedTime: json['completedTime'] != null 
          ? DateTime.parse(json['completedTime']) 
          : null,
    );
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'nodeId': nodeId,
      'completed': completed,
      'addedTime': addedTime.toIso8601String(),
      'completedTime': completedTime?.toIso8601String(),
    };
  }
  
  /// 标记为已完成
  VideoQueueNode markAsCompleted() {
    return VideoQueueNode(
      nodeId: nodeId,
      completed: true,
      addedTime: addedTime,
      completedTime: DateTime.now(),
    );
  }
}

/// 项目存档数据结构
class ProjectArchive {
  /// 项目路径
  final String projectPath;
  
  /// 起始元素ID
  final String startElementId;
  
  /// 当前正在观看的节点ID
  final String currentNodeId;
  
  /// 视频队列 - 只增不减的队列结构
  final List<VideoQueueNode> videoQueue;
  
  /// 创建时间
  final DateTime createTime;
  
  /// 最后更新时间
  final DateTime updateTime;
  
  /// 金币数量
  final int? coins;
  
  /// 全局变量状态 - 存储全局变量的当前值
  final Map<String, dynamic>? globalVariables;
  
  ProjectArchive({
    required this.projectPath,
    required this.startElementId,
    required this.currentNodeId,
    required this.videoQueue,
    required this.createTime,
    required this.updateTime,
    this.coins,
    this.globalVariables,
  });
  
  /// 从JSON创建
  factory ProjectArchive.fromJson(Map<String, dynamic> json) {
    try {
      // 确保必要字段存在
      if (json['projectPath'] == null || 
          json['startElementId'] == null || 
          json['currentNodeId'] == null) {
        throw Exception('缺少必要的存档字段');
      }
      
      // 处理videoQueue，确保它是一个有效的列表
      List<VideoQueueNode> queue = [];
      if (json['videoQueue'] != null) {
        queue = (json['videoQueue'] as List<dynamic>)
            .map((node) => VideoQueueNode.fromJson(node))
            .toList();
      } else if (json['watchedNodes'] != null) {
        // 兼容旧版本存档格式
        print('检测到旧版本存档格式，正在转换为视频队列格式');
        final watchedNodes = (json['watchedNodes'] as List<dynamic>);
        for (var node in watchedNodes) {
          queue.add(VideoQueueNode(
            nodeId: node['nodeId'],
            completed: node['watched'] ?? false,
            addedTime: DateTime.parse(node['createTime']),
            completedTime: node['lastWatchedTime'] != null 
                ? DateTime.parse(node['lastWatchedTime']) 
                : null,
          ));
        }
      }
      
      // 读取全局变量
      Map<String, dynamic>? globalVariables;
      if (json['globalVariables'] != null) {
        globalVariables = Map<String, dynamic>.from(json['globalVariables']);
      }
      
      return ProjectArchive(
        projectPath: json['projectPath'],
        startElementId: json['startElementId'],
        currentNodeId: json['currentNodeId'],
        videoQueue: queue,
        createTime: DateTime.parse(json['createTime'] ?? DateTime.now().toIso8601String()),
        updateTime: DateTime.parse(json['updateTime'] ?? DateTime.now().toIso8601String()),
        coins: json['coins'],
        globalVariables: globalVariables,
      );
    } catch (e) {
      print('解析存档JSON数据时出错: $e');
      throw e; // 重新抛出异常以便上层函数捕获
    }
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    final json = {
      'projectPath': projectPath,
      'startElementId': startElementId,
      'currentNodeId': currentNodeId,
      'videoQueue': videoQueue.map((node) => node.toJson()).toList(),
      'createTime': createTime.toIso8601String(),
      'updateTime': updateTime.toIso8601String(),
    };
    
    // 如果有金币数据，添加到JSON中
    if (coins != null) {
      json['coins'] = coins as Object;
    }
    
    // 如果有全局变量数据，添加到JSON中
    if (globalVariables != null) {
      json['globalVariables'] = globalVariables as Object;
    }
    
    return json;
  }
  
  /// 添加视频到队列
  /// 如果视频已在队列中，则不会重复添加
  /// 如果是起始元素，则不会添加到队列中
  /// 返回更新后的存档对象
  ProjectArchive addVideoToQueue(String nodeId) {
    // 如果是起始元素，不添加到队列中
    if (nodeId == startElementId) {
      return ProjectArchive(
        projectPath: projectPath,
        startElementId: startElementId,
        currentNodeId: nodeId, // 更新当前节点ID
        videoQueue: videoQueue, // 保持队列不变
        createTime: createTime,
        updateTime: DateTime.now(),
        coins: coins, // 保持金币数量不变
        globalVariables: globalVariables, // 保持全局变量不变
      );
    }
    
    // 检查节点是否已在队列中
    final existingIndex = videoQueue.indexWhere((node) => node.nodeId == nodeId);
    final newQueue = List<VideoQueueNode>.from(videoQueue);
    final now = DateTime.now();
    
    if (existingIndex != -1) {
      // 如果节点已存在但未完成，则标记为已完成
      if (!newQueue[existingIndex].completed) {
        newQueue[existingIndex] = newQueue[existingIndex].markAsCompleted();
      }
    } else {
      // 如果节点不存在，添加到队列末尾
      newQueue.add(VideoQueueNode(
        nodeId: nodeId,
        completed: false, // 初始状态为未完成
        addedTime: now,
      ));
    }
    
    return ProjectArchive(
      projectPath: projectPath,
      startElementId: startElementId,
      currentNodeId: nodeId, // 更新当前节点ID
      videoQueue: newQueue,
      createTime: createTime,
      updateTime: now,
      coins: coins, // 保持金币数量不变
      globalVariables: globalVariables, // 保持全局变量不变
    );
  }
  
  /// 标记队列中的节点为已完成
  /// 返回更新后的存档对象
  ProjectArchive markNodeAsCompleted(String nodeId) {
    // 检查节点是否已在队列中
    final existingIndex = videoQueue.indexWhere((node) => node.nodeId == nodeId);
    final newQueue = List<VideoQueueNode>.from(videoQueue);
    final now = DateTime.now();
    
    if (existingIndex != -1) {
      // 如果节点已存在但未完成，则标记为已完成
      if (!newQueue[existingIndex].completed) {
        newQueue[existingIndex] = newQueue[existingIndex].markAsCompleted();
      }
    } else {
      // 如果节点不存在，添加到队列末尾并标记为已完成
      newQueue.add(VideoQueueNode(
        nodeId: nodeId,
        completed: true,
        addedTime: now,
        completedTime: now,
      ));
    }
    
    return ProjectArchive(
      projectPath: projectPath,
      startElementId: startElementId,
      currentNodeId: currentNodeId, // 保持当前节点ID不变
      videoQueue: newQueue,
      createTime: createTime,
      updateTime: now,
      coins: coins, // 保持金币数量不变
      globalVariables: globalVariables, // 保持全局变量不变
    );
  }
  
  /// 更新全局变量
  /// 返回更新后的存档对象
  ProjectArchive updateGlobalVariables(Map<String, dynamic> newGlobalVariables) {
    return ProjectArchive(
      projectPath: projectPath,
      startElementId: startElementId,
      currentNodeId: currentNodeId,
      videoQueue: videoQueue,
      createTime: createTime,
      updateTime: DateTime.now(),
      coins: coins,
      globalVariables: newGlobalVariables,
    );
  }
  
  /// 检查节点是否已完成观看
  bool isNodeCompleted(String nodeId) {
    return videoQueue.any((node) => node.nodeId == nodeId && node.completed);
  }
  
  /// 获取最后一个添加到队列的节点（当前正在播放的节点）
  VideoQueueNode? getLastQueuedNode() {
    if (videoQueue.isEmpty) return null;
    return videoQueue.last;
  }
}

/// 项目存档工具类
class SaveArchive {
  /// 获取Steam安装路径
  static String _getSteamPath() {
    if (Platform.isWindows) {
      // 尝试从环境变量获取Steam路径
      return Platform.environment['SteamPath'] ?? 'C:\\Program Files (x86)\\Steam';
    } else if (Platform.isMacOS) {
      return '${Platform.environment['HOME']}/Library/Application Support/Steam';
    } else if (Platform.isLinux) {
      return '${Platform.environment['HOME']}/.local/share/Steam';
    } else {
      return '';
    }
  }
  /// 获取存档文件存储基础路径
  static Future<String> getFilePath() async {
    final currentDir = Directory.current;
    final path = p.join(currentDir.path, 'SavesDir');
    
    // 检查路径是否存在
    final dir = Directory(path);
    if (!dir.existsSync()) {
      print('警告: 项目存放路径不存在: $path');
    }
    
    return '$path/remote/file';
  }
  
  /// 获取特定项目的存档文件夹路径
  static Future<String> getProjectArchivePath(String startElementId) async {
    final basePath = await getFilePath();
    final projectPath = '$basePath/$startElementId';
    return projectPath;
  }
  
  /// 从FLOWCHART.json文件中获取start元素的ID
  static Future<String?> getStartElementId(String flowchartPath) async {
    try {
      final file = File(flowchartPath);
      if (!await file.exists()) {
        print('FLOWCHART.json文件不存在: $flowchartPath');
        return null;
      }
      
      final jsonContent = await file.readAsString();
      final jsonData = jsonDecode(jsonContent);
      
      if (jsonData == null || jsonData['elements'] == null) {
        print('FLOWCHART.json文件格式错误: $flowchartPath');
        return null;
      }
      
      // 查找kind为0的元素（start元素）
      final elements = jsonData['elements'] as List<dynamic>;
      for (var element in elements) {
        if (element['kind'] == 0) {
          return element['id'] as String?;
        }
      }
      
      print('未找到start元素: $flowchartPath');
      return null;
    } catch (e) {
      print('读取FLOWCHART.json文件出错: $e');
      return null;
    }
  }
  
  /// 为本地项目创建存档文件
  static Future<bool> createArchiveForLocalProject(String projectPath) async {
    try {
      final flowchartPath = '$projectPath/FLOWCHART.json';
      final startElementId = await getStartElementId(flowchartPath);
      
      if (startElementId == null) {
        return false;
      }
      
      return await _createArchiveFile(startElementId, projectPath);
    } catch (e) {
      print('为本地项目创建存档文件时出错: $e');
      return false;
    }
  }
  
  /// 为工作坊项目创建存档文件
  static Future<bool> createArchiveForWorkshopProject(String workshopItemId) async {
    try {
      final flowchartPath = WorkshopPath.getFlowchartPath(workshopItemId);
      final startElementId = await getStartElementId(flowchartPath);
      
      if (startElementId == null) {
        return false;
      }
      
      return await _createArchiveFile(startElementId, workshopItemId);
    } catch (e) {
      print('为工作坊项目创建存档文件时出错: $e');
      return false;
    }
  }
  
  /// 创建存档文件
  static Future<bool> _createArchiveFile(String startElementId, String projectPath) async {
    try {
      // 获取项目存档文件夹路径
      final projectArchivePath = await getProjectArchivePath(startElementId);
      final archiveDir = Directory(projectArchivePath);
      
      // 确保项目存档目录存在
      if (!await archiveDir.exists()) {
        await archiveDir.create(recursive: true);
      }
      
      // 使用时间戳创建存档文件名
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final archiveFile = File('$projectArchivePath/$timestamp.json');
      
      // 创建存档内容
      final archiveContent = {
        'projectPath': projectPath,
        'startElementId': startElementId,
        'createTime': DateTime.now().toIso8601String(),
      };
      
      // 写入存档文件，添加缩进和换行以提高可读性
      final jsonString = JsonEncoder.withIndent('  ').convert(archiveContent);
      await archiveFile.writeAsString(jsonString);
      print('成功创建存档文件: ${archiveFile.path}');
      
      return true;
    } catch (e) {
      print('创建存档文件时出错: $e');
      return false;
    }
  }
  
  /// 读取项目存档（兼容旧版本）
  /// [startElementId] 项目的起始元素ID
  /// 返回项目存档对象，如果不存在则返回null
  static Future<ProjectArchive?> loadArchive(String startElementId) async {
    try {
      // 尝试使用新方法读取最新存档
      final latestArchive = await loadLatestArchive(startElementId);
      if (latestArchive != null) {
        return latestArchive;
      }
      
      // 如果新方法失败，尝试读取旧版本存档
      final archivePath = await getFilePath();
      final archiveFile = File('$archivePath/$startElementId.json');
      
      // 检查存档文件是否存在
      if (!await archiveFile.exists()) {
        print('存档文件不存在: ${archiveFile.path}');
        return null;
      }
      
      // 读取存档文件内容
      final jsonContent = await archiveFile.readAsString();
      final jsonData = jsonDecode(jsonContent);
      
      // 检查是否是新版本的存档格式
      if (jsonData.containsKey('currentNodeId')) {
        // 新版本存档格式，直接解析
        return ProjectArchive.fromJson(jsonData);
      } else {
        // 旧版本存档格式，需要转换
        print('检测到旧版本存档格式，进行转换');
        return null;
      }
    } catch (e) {
      print('读取存档文件时出错: $e');
      return null;
    }
  }
  
  /// 读取项目的最新存档
  /// [startElementId] 项目的起始元素ID
  /// 返回最新的项目存档对象，如果不存在则返回null
  static Future<ProjectArchive?> loadLatestArchive(String startElementId) async {
    try {
      if (startElementId == null || startElementId.isEmpty) {
        print('无效的startElementId: $startElementId');
        return null;
      }
      
      final projectArchivePath = await getProjectArchivePath(startElementId);
      print('尝试从路径加载存档: $projectArchivePath');
      final archiveDir = Directory(projectArchivePath);
      
      // 检查项目存档目录是否存在
      if (!await archiveDir.exists()) {
        print('项目存档目录不存在: $projectArchivePath');
        return null;
      }
      
      // 获取目录中的所有json文件
      final entities = await archiveDir.list().toList();
      final files = entities
          .where((entity) => entity is File && entity.path.endsWith('.json'))
          .toList();
      
      if (files.isEmpty) {
        print('项目存档目录中没有存档文件: $projectArchivePath');
        return null;
      }
      
      // 按文件名（时间戳）排序，获取最新的存档文件
      files.sort((a, b) => p.basename(b.path).compareTo(p.basename(a.path)));
      final latestFile = files.first as File;
      
      print('找到最新存档文件: ${latestFile.path}');
      
      // 读取存档文件内容
      final jsonContent = await latestFile.readAsString();
      if (jsonContent.isEmpty) {
        print('存档文件内容为空: ${latestFile.path}');
        return null;
      }
      
      try {
        final jsonData = jsonDecode(jsonContent);
        if (jsonData == null) {
          print('存档文件JSON解析结果为null: ${latestFile.path}');
          return null;
        }
        
        print('成功读取最新存档文件: ${latestFile.path}');
        final archive = ProjectArchive.fromJson(jsonData);
        
        // 不再同步金币数量，因为金币不再与项目关联
        
        return archive;
      } catch (parseError) {
        print('解析存档文件JSON时出错: $parseError');
        return null;
      }
    } catch (e) {
      print('读取最新存档文件时出错: $e');
      return null;
    }
  }
  
  /// 创建初始存档
  /// [startElementId] 项目的起始元素ID
  /// [projectPath] 项目路径
  /// 返回创建的项目存档对象
  static Future<ProjectArchive> createInitialArchive(String startElementId, String projectPath) async {
    final now = DateTime.now();
    // 从CoinManager获取当前金币数量
    final coinManager = CoinManager();
    final coins = await coinManager.getTotalCoins();
    
    // 加载初始全局变量值
    Map<String, dynamic>? initialGlobalVariables = await _loadInitialGlobalVariables(projectPath);
    
    final initialArchive = ProjectArchive(
      projectPath: projectPath,
      startElementId: startElementId,
      currentNodeId: startElementId, // 初始时当前节点为起始节点
      videoQueue: [], // 初始时视频队列为空
      createTime: now,
      updateTime: now,
      coins: coins, // 添加金币数量
      globalVariables: initialGlobalVariables, // 设置初始全局变量
    );
    
    // 保存初始存档
    await saveArchive(initialArchive);
    
    return initialArchive;
  }
  
  /// 从全局变量文件中加载初始全局变量值
  static Future<Map<String, dynamic>?> _loadInitialGlobalVariables(String projectPath) async {
    try {
      // 从流程图FLOWCHART.json中加载全局变量初始值
      final flowchartFile = File('$projectPath/FLOWCHART.json');
      if (await flowchartFile.exists()) {
        final content = await flowchartFile.readAsString();
        final flowchartData = jsonDecode(content) as Map<String, dynamic>;
        
        // 检查FLOWCHART.json是否包含globalVariables字段
        if (flowchartData.containsKey('globalVariables')) {
          final globalVarsJson = flowchartData['globalVariables'] as List;
          
          // 初始化全局变量Map
          Map<String, dynamic> globalVariables = {};
          for (var item in globalVarsJson) {
            globalVariables[item['name']] = item['value'];
          }
          
          print('从流程图FLOWCHART.json加载了 ${globalVariables.length} 个初始全局变量');
          return globalVariables;
        }
      }
      
      return null;
    } catch (e) {
      print('加载初始全局变量出错: $e');
    }
    
    return null;
  }
  
  /// 保存项目存档
  /// [archive] 要保存的项目存档对象
  /// 返回是否保存成功
  static Future<bool> saveArchive(ProjectArchive? archive) async {
    try {
      // 验证存档对象
      if (archive == null) {
        print('存档对象为null，无法保存');
        return false;
      }
      
      // 创建新的存档对象，包含金币信息
      archive = ProjectArchive(
        projectPath: archive.projectPath,
        startElementId: archive.startElementId,
        currentNodeId: archive.currentNodeId,
        videoQueue: archive.videoQueue,
        createTime: archive.createTime,
        updateTime: archive.updateTime,
        coins: archive.coins,
        globalVariables: archive.globalVariables,
      );
      print('准备保存项目存档，包含金币信息');
      
      // 验证startElementId
      final startElementId = archive.startElementId;
      if (startElementId.isEmpty) {
        print('存档对象的startElementId无效: $startElementId');
        return false;
      }
      
      // 获取项目存档文件夹路径
      final projectArchivePath = await getProjectArchivePath(startElementId);
      print('准备保存存档到路径: $projectArchivePath');
      
      // 确保路径有效
      if (projectArchivePath.isEmpty) {
        print('项目存档路径无效: $projectArchivePath');
        return false;
      }
      
      try {
        // 创建存档目录
        final archiveDir = Directory(projectArchivePath);
        
        // 确保项目存档目录存在
        if (!await archiveDir.exists()) {
          print('创建存档目录: $projectArchivePath');
          await archiveDir.create(recursive: true);
        }
        
        // 使用时间戳创建存档文件名
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        final archiveFile = File('$projectArchivePath/$timestamp.json');
        
        try {
          // 转换为JSON
          final jsonData = archive.toJson();
          print('存档对象转换为JSON成功');
          
          // 编码为JSON字符串，添加缩进和换行以提高可读性
          final jsonString = JsonEncoder.withIndent('  ').convert(jsonData);
          print('JSON编码成功（带缩进格式），准备写入文件');
          
          // 写入存档文件
          await archiveFile.writeAsString(jsonString);
          print('成功保存存档文件: ${archiveFile.path}');
          
          return true;
        } catch (jsonError) {
          print('JSON处理过程中出错: $jsonError');
          return false;
        }
      } catch (dirError) {
        print('创建或访问目录时出错: $dirError');
        return false;
      }
    } catch (e) {
      print('保存存档文件时出错: $e');
      print('错误堆栈: ${e is Error ? e.stackTrace : "无堆栈信息"}');
      return false;
    }
  }
  
  /// 检查存档是否存在
  /// [startElementId] 项目的起始元素ID
  /// 返回存档是否存在
  static Future<bool> archiveExists(String startElementId) async {
    try {
      // 检查新版本存档
      final projectArchivePath = await getProjectArchivePath(startElementId);
      final archiveDir = Directory(projectArchivePath);
      
      if (await archiveDir.exists()) {
        // 检查目录中是否有json文件
        final files = await archiveDir.list()
            .where((entity) => entity is File && entity.path.endsWith('.json'))
            .toList();
        
        if (files.isNotEmpty) {
          return true;
        }
      }
      
      // 检查旧版本存档
      final oldArchivePath = await getFilePath();
      final oldArchiveFile = File('$oldArchivePath/$startElementId.json');
      
      return await oldArchiveFile.exists();
    } catch (e) {
      print('检查存档文件是否存在时出错: $e');
      return false;
    }
  }
  
  /// 批量处理目录下的所有项目，为每个项目创建存档文件
  /// [directoryPath] 包含多个项目的目录路径
  /// 返回成功创建存档的项目数量
  static Future<int> createArchivesForDirectory(String directoryPath) async {
    try {
      final directory = Directory(directoryPath);
      if (!await directory.exists()) {
        print('目录不存在: $directoryPath');
        return 0;
      }
      
      int successCount = 0;
      final List<FileSystemEntity> entities = await directory.list().toList();
      
      // 遍历目录中的所有子目录（每个子目录视为一个项目）
      for (var entity in entities) {
        if (entity is Directory) {
          final projectPath = entity.path;
          final flowchartPath = '$projectPath/FLOWCHART.json';
          
          // 检查是否存在FLOWCHART.json文件
          if (await File(flowchartPath).exists()) {
            // 获取start元素ID并创建存档
            final startElementId = await getStartElementId(flowchartPath);
            if (startElementId != null) {
              final success = await _createArchiveFile(startElementId, projectPath);
              if (success) {
                successCount++;
                print('成功为项目创建存档: $projectPath');
              }
            } else {
              print('无法获取项目的start元素ID: $projectPath');
            }
          } else {
            print('项目中不存在FLOWCHART.json文件: $projectPath');
          }
        }
      }
      
      print('批量处理完成，成功创建 $successCount 个项目存档');
      return successCount;
    } catch (e) {
      print('批量处理项目存档时出错: $e');
      return 0;
    }
  }
  
  /// 批量处理工作坊目录下的所有项目，为每个项目创建存档文件
  /// 返回成功创建存档的项目数量
  static Future<int> createArchivesForWorkshopDirectory() async {
    try {
      // 获取当前应用的AppID
      int appId = SteamGlobals.instance.appId;
      print('使用全局变量获取Steam AppID: $appId');
      
      // 构建工作坊内容目录路径
      final currentDir = Directory.current;
      final workshopContentPath = p.join(currentDir.path, '../..', 'workshop', 'content', '$appId');
      
      final workshopDir = Directory(workshopContentPath);
      if (!await workshopDir.exists()) {
        print('工作坊内容目录不存在: $workshopContentPath');
        return 0;
      }
      
      int successCount = 0;
      final List<FileSystemEntity> entities = await workshopDir.list().toList();
      
      // 遍历工作坊内容目录中的所有子目录（每个子目录是一个工作坊项目）
      for (var entity in entities) {
        if (entity is Directory) {
          final workshopItemId = p.basename(entity.path);
          final flowchartPath = WorkshopPath.getFlowchartPath(workshopItemId);
          
          // 检查是否存在FLOWCHART.json文件
          if (await File(flowchartPath).exists()) {
            // 获取start元素ID并创建存档
            final startElementId = await getStartElementId(flowchartPath);
            if (startElementId != null) {
              final success = await _createArchiveFile(startElementId, workshopItemId);
              if (success) {
                successCount++;
                print('成功为工作坊项目创建存档: $workshopItemId');
              }
            } else {
              print('无法获取工作坊项目的start元素ID: $workshopItemId');
            }
          } else {
            print('工作坊项目中不存在FLOWCHART.json文件: $workshopItemId');
          }
        }
      }
      
      print('批量处理完成，成功创建 $successCount 个工作坊项目存档');
      return successCount;
    } catch (e) {
      print('批量处理工作坊项目存档时出错: $e');
      return 0;
    }
  }
}