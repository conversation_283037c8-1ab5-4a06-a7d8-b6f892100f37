import 'dart:io';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:ve/generated/l10n.dart';
import 'package:path/path.dart' as p;
import 'package:intl/intl.dart';
import 'package:ve/utils/save_archive.dart';
import 'package:ve/utils/workshop_path.dart';
import 'package:ve/UI/Views/open/open_game_view.dart';
import '../styles.dart';

class LoadArchiveScreen extends StatefulWidget {
  final String projectPath;
  final String projectName;
  final String workshopItemId;

  const LoadArchiveScreen({
    Key? key,
    required this.projectPath,
    required this.projectName,
    required this.workshopItemId,
  }) : super(key: key);

  @override
  _LoadArchiveScreenState createState() => _LoadArchiveScreenState();
}

class _LoadArchiveScreenState extends State<LoadArchiveScreen> {
  List<Map<String, dynamic>> _archives = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadArchives();
  }

  Future<void> _loadArchives() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // 获取项目的FLOWCHART.json文件路径
      final String jsonFilePath;
      if (widget.workshopItemId != '0') {
        // 工作坊项目
        jsonFilePath = await WorkshopPath.getFlowchartPath(widget.workshopItemId);
      } else {
        // 本地项目
        jsonFilePath = '${widget.projectPath}/FLOWCHART.json';
      }

      // 检查文件是否存在
      final jsonFile = File(jsonFilePath);
      if (!await jsonFile.exists()) {
        setState(() {
          _isLoading = false;
          _errorMessage = S.of(context).flowchartFileNotFound;
        });
        return;
      }

      // 获取起始元素ID
      final startElementId = await SaveArchive.getStartElementId(jsonFilePath);
      if (startElementId == null) {
        setState(() {
          _isLoading = false;
          _errorMessage = S.of(context).startElementIdNotFound;
        });
        return;
      }

      // 获取项目存档文件夹路径
      final projectArchivePath = await SaveArchive.getProjectArchivePath(startElementId);
      final archiveDir = Directory(projectArchivePath);

      // 检查存档目录是否存在
      if (!await archiveDir.exists()) {
        // 如果不存在存档，创建初始存档
        final initialArchive = await SaveArchive.createInitialArchive(
          startElementId,
          widget.projectPath,
        );

        setState(() {
          _archives = [
            {
              'id': 'initial',
              'path': projectArchivePath,
              'createTime': initialArchive.createTime,
              'updateTime': initialArchive.updateTime,
              'startElementId': startElementId,
              'currentNodeId': initialArchive.currentNodeId,
              'watchedCount': 0,
            }
          ];
          _isLoading = false;
        });
        return;
      }

      // 获取目录中的所有json文件
      final entities = await archiveDir.list().toList();
      final files = entities
          .where((entity) => entity is File && entity.path.endsWith('.json'))
          .toList();

      if (files.isEmpty) {
        // 如果没有存档文件，创建初始存档
        final initialArchive = await SaveArchive.createInitialArchive(
          startElementId,
          widget.projectPath,
        );

        setState(() {
          _archives = [
            {
              'id': 'initial',
              'path': projectArchivePath,
              'createTime': initialArchive.createTime,
              'updateTime': initialArchive.updateTime,
              'startElementId': startElementId,
              'currentNodeId': initialArchive.currentNodeId,
              'watchedCount': 0,
            }
          ];
          _isLoading = false;
        });
        return;
      }

      // 按文件名（时间戳）排序，最新的在前面
      files.sort((a, b) => p.basename(b.path).compareTo(p.basename(a.path)));

      // 读取每个存档文件的信息
      List<Map<String, dynamic>> archives = [];
      for (var file in files) {
        try {
          final jsonContent = await (file as File).readAsString();
          final archive = ProjectArchive.fromJson(await json.decode(jsonContent));
          
          archives.add({
            'id': p.basenameWithoutExtension(file.path),
            'path': file.path,
            'createTime': archive.createTime,
            'updateTime': archive.updateTime,
            'startElementId': archive.startElementId,
            'currentNodeId': archive.currentNodeId,
            'watchedCount': archive.videoQueue.where((node) => node.completed).length,
          });
        } catch (e) {
          print('读取存档文件出错: ${file.path}, $e');
          // 继续处理下一个文件
        }
      }

      setState(() {
        _archives = archives;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = '${S.of(context).loadArchiveError}: $e';
      });
    }
  }

  Future<void> _loadArchive(Map<String, dynamic> archive) async {
    try {
      final startElementId = archive['startElementId'] as String;
      final currentNodeId = archive['currentNodeId'] as String;
      
      // 加载存档的更多详情
      final projectArchive = await SaveArchive.loadLatestArchive(startElementId);
      if (projectArchive == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('${S.of(context).loadArchiveError}: 存档读取失败'))
        );
        return;
      }
      
      // 导航到游戏视图，并传递初始节点ID
      if (mounted) {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => OpenGameView(
              projectPath: widget.projectPath,
              workshopItemId: widget.workshopItemId,
              projectName: widget.projectName,
              initialNodeId: currentNodeId, // 使用当前节点作为初始节点ID
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('${S.of(context).loadArchiveError}: ${e.toString()}'))
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: Text(S.of(context).loadArchive, style: TextStyles.h3.copyWith(color: Colors.white)),
        backgroundColor: Colors.black,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? Center(child: Text(_errorMessage!, style: TextStyles.body))
              : _archives.isEmpty
                  ? Center(child: Text(S.of(context).noArchivesFound, style: TextStyles.body))
                  : ListView.builder(
                      padding: const EdgeInsets.all(16.0),
                      itemCount: _archives.length,
                      itemBuilder: (context, index) {
                        final archive = _archives[index];
                        final createTime = archive['createTime'] as DateTime;
                        final updateTime = archive['updateTime'] as DateTime;
                        final watchedCount = archive['watchedCount'] as int;
                        
                        return Card(
                          color: Colors.grey[900],
                          margin: const EdgeInsets.only(bottom: 16.0),
                          child: InkWell(
                            onTap: () => _loadArchive(archive),
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    '${S.of(context).archive} ${index + 1}',
                                    style: TextStyles.h4,
                                  ),
                                  const SizedBox(height: 8.0),
                                  Text(
                                    '${S.of(context).createTime}: ${DateFormat('yyyy-MM-dd HH:mm:ss').format(createTime)}',
                                    style: TextStyles.body,
                                  ),
                                  const SizedBox(height: 4.0),
                                  Text(
                                    '${S.of(context).lastUpdate}: ${DateFormat('yyyy-MM-dd HH:mm:ss').format(updateTime)}',
                                    style: TextStyles.body,
                                  ),
                                  const SizedBox(height: 4.0),
                                  Text(
                                    '${S.of(context).watchedNodesCount}: $watchedCount',
                                    style: TextStyles.body,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    ),
    );
  }
}