import 'dart:ui';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:stacked/stacked.dart';
import 'workshop_viewmodel.dart'; // 导入 WorkshopViewModel
import 'package:ve/generated/l10n.dart';
import 'package:ve/utils/data.dart'; // 导入 Workshop 类

class WorkshopView extends StatefulWidget {
  const WorkshopView({super.key});

  @override
  _WorkshopViewState createState() => _WorkshopViewState();
}

class _WorkshopViewState extends State<WorkshopView> {
  bool _isLoading = false; // 添加加载状态变量
  int _loadingIndex = -1; // 跟踪正在加载的图片索引

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<WorkshopViewModel>.reactive(
      builder: (context, model, child) {
        return Scaffold(
          appBar: AppBar(
            title: Text(S.of(context).workshopItems),
          ),
          body: Container(
            height: MediaQuery.of(context).size.height,
            width: MediaQuery.of(context).size.width,
            decoration: const BoxDecoration(
              image: DecorationImage(
                image: AssetImage('assets/images/landscape.jpg'),
                fit: BoxFit.cover,
              ),
            ),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 20),
                      GridView.builder(
                        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 10, // 每行显示 10 个
                          childAspectRatio: 1, // 宽高比
                          crossAxisSpacing: 6.0, // 列间距
                          mainAxisSpacing: 6.0, // 行间距
                        ),
itemCount: model.filteredWorkshops.length, // 总共的图片数量
                        shrinkWrap: true, // 使 GridView 不占用无限高度
                        physics: const NeverScrollableScrollPhysics(), // 禁用滚动
                        itemBuilder: (context, index) {
                          return _buildWorkshopCard(model.filteredWorkshops[index]);
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
      viewModelBuilder: () => WorkshopViewModel(),
    );
  }

Widget _buildWorkshopCard(WorkshopItem workshop) {
    return Card(
      child: Column(
        children: [
          Expanded(
            child: Image.network(
              workshop.imageUrl,
              fit: BoxFit.cover,
            ),
          ),
          Text(workshop.title),
          Text(workshop.description),
        ],
      ),
    );
  }

  Future<Image> loadImage(String url, BuildContext context) async {
    try {
      // 尝试加载图片
      final image = Image.network(
        url,
        errorBuilder: (context, error, stackTrace) {
          print('网络图片加载失败: $error');
          return Image.asset('assets/images/logo.png', fit: BoxFit.cover);
        },
      );
      // 等待图片加载完成
      await precacheImage(image.image, context);
      return image;
    } catch (e) {
      print('图片加载失败: $e');
      // 如果加载失败，返回默认图片而不是抛出异常
      return Image.asset('assets/images/logo.png', fit: BoxFit.cover);
    }
  }
}
