import 'package:flutter/material.dart';
import '../UI/shared/value_change_indicator.dart';

/// 数值变化辅助类
/// 提供便捷方法在应用中显示数值变化提示框
class ValueChangeHelper {
  /// 静态变量，用于跟踪当前显示的提示框数量
  static Map<Alignment, int> _activeIndicators = {};
  
  /// 最大的垂直偏移量（避免提示框超出屏幕）
  static const int _maxVerticalOffset = 5;
  
  /// 提示框之间的垂直间距
  static const double _verticalSpacing = 80.0;
  
  /// 在指定上下文中显示数值变化提示框
  /// 
  /// [context] 上下文
  /// [type] 变化类型（增加或减少）
  /// [valueName] 变量名称
  /// [position] 提示框显示位置
  /// [duration] 动画持续时间
  static void showIndicator(
    BuildContext context, {
    required ValueChangeType type,
    String? valueName,
    Alignment position = Alignment.topRight,
    Duration duration = const Duration(milliseconds: 1600),
  }) {
    // 获取屏幕大小
    final size = MediaQuery.of(context).size;
    
    // 增加这个位置的提示框计数
    _activeIndicators[position] = (_activeIndicators[position] ?? 0) + 1;
    
    // 获取当前提示框序号（用于计算垂直偏移）
    final int indicatorIndex = _activeIndicators[position]! - 1;
    
    // 限制最大偏移量
    final int offsetIndex = indicatorIndex % _maxVerticalOffset;
    
    // 根据对齐方式计算位置，并应用垂直偏移
    final Offset offset = _calculatePosition(position, size, offsetIndex);
    
    // 创建浮层
    final overlayState = Overlay.of(context);
    final overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        left: offset.dx,
        top: offset.dy,
        child: ValueChangeIndicator(
          changeType: type,
          duration: duration,
          valueName: valueName,
        ),
      ),
    );
    
    // 插入浮层
    overlayState.insert(overlayEntry);
    
    // 动画完成后移除浮层
    Future.delayed(duration * 1.2, () {
      overlayEntry.remove();
      
      // 减少这个位置的提示框计数
      _activeIndicators[position] = (_activeIndicators[position] ?? 1) - 1;
      if (_activeIndicators[position] == 0) {
        _activeIndicators.remove(position);
      }
    });
  }
  
  /// 比较新旧值并显示相应的提示
  /// 
  /// [context] 上下文
  /// [oldValue] 旧值
  /// [newValue] 新值
  /// [valueName] 变量名称
  /// [position] 提示框显示位置
  /// [duration] 动画持续时间
  /// 
  /// 返回是否显示了提示框
  static bool compareAndShow(
    BuildContext context, {
    required num oldValue,
    required num newValue,
    String? valueName,
    Alignment position = Alignment.topRight,
    Duration duration = const Duration(milliseconds: 1600),
  }) {
    // 比较值的大小
    if (newValue > oldValue) {
      // 数值增加
      showIndicator(
        context,
        type: ValueChangeType.increase,
        valueName: valueName,
        position: position,
        duration: duration,
      );
      return true;
    } else if (newValue < oldValue) {
      // 数值减少
      showIndicator(
        context,
        type: ValueChangeType.decrease,
        valueName: valueName,
        position: position,
        duration: duration,
      );
      return true;
    }
    
    // 数值未变化，不显示提示
    return false;
  }
  
  /// 根据对齐方式计算位置
  static Offset _calculatePosition(Alignment alignment, Size screenSize, int offsetIndex) {
    final double x, y;
    
    // 计算水平位置
    if (alignment.x < 0) {
      // 左侧
      x = 16;
    } else if (alignment.x > 0) {
      // 右侧
      x = screenSize.width - 240;
    } else {
      // 居中
      x = (screenSize.width - 200) / 2;
    }
    
    // 计算垂直位置
    if (alignment.y < 0) {
      // 顶部
      y = 80 + (offsetIndex * _verticalSpacing);
    } else if (alignment.y > 0) {
      // 底部
      y = screenSize.height - 140 - (offsetIndex * _verticalSpacing);
    } else {
      // 居中
      y = (screenSize.height - 80) / 2 + ((offsetIndex - (_maxVerticalOffset / 2).floor()) * _verticalSpacing);
    }
    
    return Offset(x, y);
  }
} 