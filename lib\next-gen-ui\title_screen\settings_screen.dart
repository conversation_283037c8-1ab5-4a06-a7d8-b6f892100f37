import 'package:flutter/material.dart';
import 'package:ve/generated/l10n.dart';
import '../styles.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SettingsScreen extends StatefulWidget {
  final String projectId; // 添加项目ID参数
  
  const SettingsScreen({
    Key? key, 
    required this.projectId,
  }) : super(key: key);

  @override
  _SettingsScreenState createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  // 设置选项
  bool _enableAutoSave = true;
  bool _showVideoControls = false;
  String _selectedLanguage = 'zh_CN'; // 默认中文
  double _volume = 0.8; // 默认音量80%
  
  // 为设置生成唯一键
  String get _autoSaveKey => 'autoSave_${widget.projectId}';
  String get _showVideoControlsKey => 'showVideoControls_${widget.projectId}';
  String get _volumeKey => 'volume_${widget.projectId}';
  String get _languageKey => 'language_${widget.projectId}';

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  // 加载设置
  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    
    setState(() {
      _enableAutoSave = prefs.getBool(_autoSaveKey) ?? true;
      _showVideoControls = prefs.getBool(_showVideoControlsKey) ?? false;
      _volume = prefs.getDouble(_volumeKey) ?? 0.8;
      _selectedLanguage = prefs.getString(_languageKey) ?? 'zh_CN';
    });
  }

  // 保存设置
  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      await prefs.setBool(_autoSaveKey, _enableAutoSave);
      await prefs.setBool(_showVideoControlsKey, _showVideoControls);
      await prefs.setDouble(_volumeKey, _volume);
      await prefs.setString(_languageKey, _selectedLanguage);
    } catch (e) {
      // 捕获任何保存过程中的错误
      debugPrint('保存设置时出错: $e');
      
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: Text(S.of(context).settings, style: TextStyles.h3.copyWith(color: Colors.white)),
        backgroundColor: Colors.black,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: Icon(Icons.save),
            onPressed: _saveSettings,
            tooltip: S.of(context).saveSettings,
          ),
        ],
      ),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          // 通用设置组
          _buildSectionHeader(S.of(context).generalSettings),
          _buildSwitchTile(
            title: S.of(context).autoSaveProgress,
            subtitle: S.of(context).autoSaveDescription,
            value: _enableAutoSave,
            onChanged: (value) async {
              setState(() {
                _enableAutoSave = value;
              });
              
              // 直接保存这个单独的设置，不显示Snackbar
              final prefs = await SharedPreferences.getInstance();
              await prefs.setBool(_autoSaveKey, value);
            },
          ),
          _buildSwitchTile(
            title: S.of(context).showVideoControls,
            subtitle: S.of(context).showVideoControlsDescription,
            value: _showVideoControls,
            onChanged: (value) async {
              setState(() {
                _showVideoControls = value;
              });
              
              // 直接保存这个单独的设置，不显示Snackbar
              final prefs = await SharedPreferences.getInstance();
              await prefs.setBool(_showVideoControlsKey, value);
            },
          ),
          
          // 音频设置组
          _buildSectionHeader(S.of(context).audioSettings),
          _buildSliderTile(
            title: S.of(context).volume,
            value: _volume,
            min: 0.0,
            max: 1.0,
            divisions: 10,
            label: '${(_volume * 100).round()}%',
            onChanged: (value) async {
              setState(() {
                _volume = value;
              });
              
              // 直接保存这个单独的设置，不显示Snackbar
              final prefs = await SharedPreferences.getInstance();
              await prefs.setDouble(_volumeKey, value);
            },
          ),
          
          
          // 关于信息
          _buildSectionHeader(S.of(context).about),
          ListTile(
            title: Text(S.of(context).version, style: TextStyles.body),
            subtitle: Text('1.0.0', style: TextStyles.bodySmall),
          ),
          ListTile(
            title: Text(S.of(context).developer, style: TextStyles.body),
            subtitle: Text(S.of(context).gameTitle, style: TextStyles.bodySmall),
          ),
        ],
      ),
    );
  }

  // 构建分组标题
  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(top: 16.0, bottom: 8.0),
      child: Text(
        title,
        style: TextStyles.h4.copyWith(color: Colors.blue),
      ),
    );
  }

  // 构建开关设置项
  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Card(
      color: Colors.grey[900],
      child: SwitchListTile(
        title: Text(title, style: TextStyles.body),
        subtitle: Text(subtitle, style: TextStyles.bodySmall),
        value: value,
        onChanged: onChanged,
        activeColor: Colors.blue,
      ),
    );
  }

  // 构建滑块设置项
  Widget _buildSliderTile({
    required String title,
    required double value,
    required double min,
    required double max,
    required int divisions,
    required String label,
    required ValueChanged<double> onChanged,
  }) {
    return Card(
      color: Colors.grey[900],
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(title, style: TextStyles.body),
            Row(
              children: [
                Expanded(
                  child: Slider(
                    value: value,
                    min: min,
                    max: max,
                    divisions: divisions,
                    label: label,
                    onChanged: onChanged,
                    activeColor: Colors.blue,
                  ),
                ),
                Text(label, style: TextStyles.bodySmall),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // 构建下拉菜单设置项
  Widget _buildDropdownTile<T>({
    required String title,
    required T value,
    required List<DropdownMenuItem<T>> items,
    required ValueChanged<T?> onChanged,
  }) {
    return Card(
      color: Colors.grey[900],
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(title, style: TextStyles.body),
            const SizedBox(height: 8.0),
            DropdownButton<T>(
              value: value,
              items: items,
              onChanged: onChanged,
              isExpanded: true,
              dropdownColor: Colors.grey[800],
              style: TextStyles.body,
            ),
          ],
        ),
      ),
    );
  }
}