import 'dart:ui';
import 'dart:convert';
import 'dart:io';

import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/workshop_path.dart';

const _layersKey = 'layers';
const _foldersKey = 'folders';
const _showILPEditorTipKey = 'show_ilp_editor_tip';
const _steamFilterKey = 'steam_filter';
const _localeKey = 'locale';
const _adultKey = 'adult';
const _themeKey = 'theme';

/// for v1.0.17+
const _gameHelperKey = 'game_helper_v1.0.17';

class RecentProject {
  String projectPath;
  String projectName;
  final String type; // 'local' or 'workshop'
  final DateTime lastPlayed;
  String? imagePath; // 项目封面图片路径

  RecentProject({
    required this.projectPath,
    required this.projectName,
    required this.type,
    required this.lastPlayed,
    this.imagePath,
  });

  Map<String, dynamic> toJson() => {
    'projectPath': projectPath,
    'projectName': projectName,
    'type': type,
    'lastPlayed': lastPlayed.toIso8601String(),
    'imagePath': imagePath,
  };

  factory RecentProject.fromJson(Map<String, dynamic> json) => RecentProject(
    projectPath: json['projectPath'],
    projectName: json['projectName'],
    type: json['type'],
    lastPlayed: DateTime.parse(json['lastPlayed']),
    imagePath: json['imagePath'],
  );
}

class RecentEditedProject {
  String projectPath;
  String projectName;
  final String type; // 'local' or 'workshop'
  final DateTime lastEdited;
  String? imagePath; // 项目封面图片路径

  RecentEditedProject({
    required this.projectPath,
    required this.projectName,
    required this.type,
    required this.lastEdited,
    this.imagePath,
  });

  Map<String, dynamic> toJson() => {
    'projectPath': projectPath,
    'projectName': projectName,
    'type': type,
    'lastEdited': lastEdited.toIso8601String(),
    'imagePath': imagePath,
  };

  factory RecentEditedProject.fromJson(Map<String, dynamic> json) => RecentEditedProject(
    projectPath: json['projectPath'],
    projectName: json['projectName'],
    type: json['type'],
    lastEdited: DateTime.parse(json['lastEdited']),
    imagePath: json['imagePath'],
  );
}

class Workshop {
  final String id;
  final String title;
  final String description;
  final String imageUrl;
  final int subscriptionCount;
  final DateTime publishDate;
  final DateTime updateDate;

  Workshop({
    required this.id,
    required this.title,
    required this.description,
    required this.imageUrl,
    required this.subscriptionCount,
    required this.publishDate,
    required this.updateDate,
  });
}

abstract class Data {
  static late SharedPreferences _core;

  static SharedPreferences get core => _core;

  static Future init() async {
    print('存储路径 ${(await getApplicationSupportDirectory()).path}');
    _core = await SharedPreferences.getInstance();
    if (_core.containsKey(_layersKey)) {
      _layers.addAll(_core.getStringList(_layersKey)!);
    }
    if (_core.containsKey(_foldersKey)) {
      _folders.addAll(_core.getStringList(_foldersKey)!);
    }
    if (_core.containsKey(_steamFilterKey)) {
      _steamFilter.addAll(_core.getStringList(_steamFilterKey)!);
    }
    _showILPEditorTip = _core.getBool(_showILPEditorTipKey) ?? true;
    _gameHelper = _core.getBool(_gameHelperKey) ?? true;
    _isAdult = _core.getBool(_adultKey) ?? false;
    _isDark = _core.getBool(_themeKey) ?? false;
    _layers.listen(_rxListener);
    _folders.listen(_rxListener);
  }

  static final _layers = RxSet<String>();
  static final _folders = RxSet<String>();

  static bool _showILPEditorTip = true;

  static bool get showILPEditorTip => _showILPEditorTip;

  static set showILPEditorTip(bool val) {
    _showILPEditorTip = val;
    _signAndSave();
  }

  static bool _gameHelper = false;

  static bool get showGameHelper => _gameHelper;

  static set showGameHelper(bool value) {
    _gameHelper = value;
    _signAndSave();
  }

  static bool _isAdult = false;

  static bool get isAdult => _isAdult;

  static set isAdult(bool value) {
    _isAdult = value;
    _signAndSave();
  }

  static Set<String> get layersId => _layers;

  static Set<String> get folders => _folders;

  static Future<void> reset() async {
    _layers.clear();
    _folders.clear();
    _steamFilter.clear();
    _showILPEditorTip = true;
    _gameHelper = true;
    _isAdult = false;
    await _core.clear();
  }

  static _rxListener(_) {
    _signAndSave();
  }

  static String _getSign(Map map) {
    final sortedKey = map.keys.toList()..sort();
    return sortedKey.map((e) {
      return switch (map[e]) {
        Iterable => (map[e] as Iterable).join('_'),
        _ => map[e].toString(),
      };
    }).join('_');
  }

  static _signAndSave() {
    _core.setStringList(_layersKey, _layers.toList());
    _core.setStringList(_foldersKey, _folders.toList());
    _core.setStringList(_steamFilterKey, _steamFilter.toList());
    _core.setBool(_showILPEditorTipKey, _showILPEditorTip);
    _core.setBool(_gameHelperKey, _gameHelper);
    _core.setBool(_adultKey, _isAdult);
    _core.setBool(_themeKey, _isDark);
  }

  static Locale get locale {
    var str = Data.core.getString(_localeKey);
    if (str != null) {
      return stringToLocale(str);
    }
    return Get.locale ?? const Locale('en', 'US');
  }

  static set localeString(String locale) =>
      Data.core.setString(_localeKey, locale);

  static Set<String> _steamFilter = {};

  static set steamFilter(Set<String> filter) {
    _steamFilter = filter;
    Data.core.setStringList(_steamFilterKey, filter.toList());
  }

  static Set<String> get steamFilter => _steamFilter;

  static late bool _isDark;

  static bool get isDark {
    print('Data.isDark $_isDark');
    return _isDark;
  }

  static set isDark(bool val) {
    _isDark = val;
    _signAndSave();
  }

  // 最近玩过项目存储
  static List<RecentProject> getRecentProjects() {
    final jsonList = _core.getStringList('recentProjects') ?? [];
    return jsonList.map((json) => RecentProject.fromJson(Map<String, dynamic>.from(jsonDecode(json)))).toList();
  }

  // 最近编辑项目存储
  static List<RecentEditedProject> getRecentEditedProjects() {
    final jsonList = _core.getStringList('recentEditedProjects') ?? [];
    return jsonList.map((json) => RecentEditedProject.fromJson(Map<String, dynamic>.from(jsonDecode(json)))).toList();
  }

  static void saveRecentProject(RecentProject project) async {
    // 根据项目类型处理
    if (project.type == 'local') {
      // 本地项目：查找项目文件夹中的图片作为封面
      try {
        final projectDir = Directory(project.projectPath);
        if (projectDir.existsSync()) {
          // 查找项目文件夹中的图片作为封面
          if (project.imagePath == null || project.imagePath!.isEmpty) {
            final imageFiles = projectDir.listSync()
              .where((entity) => entity is File && 
                    (entity.path.toLowerCase().endsWith('.jpg') || 
                     entity.path.toLowerCase().endsWith('.jpeg') || 
                     entity.path.toLowerCase().endsWith('.png')))
              .toList();
            
            if (imageFiles.isNotEmpty) {
              final imagePath = (imageFiles.first as File).path;
              // 验证文件是否真的存在
              if (File(imagePath).existsSync()) {
                project.imagePath = imagePath;
                print('Found image for recent project: ${project.imagePath}');
              } else {
                project.imagePath = 'assets/images/logo.png';
                print('Image file exists in list but cannot be accessed: $imagePath');
              }
            } else {
              project.imagePath = 'assets/images/logo.png';
              print('No image found for recent project, using default');
            }
          }
          
          // 设置项目完整路径
          if (project.projectPath == null || project.projectPath!.isEmpty) {
            project.projectPath = projectDir.path;
            print('Set project path: ${project.projectPath}');
          }
          
          // 从FLOWCHART.json中读取项目名称（仅用于调试信息）
          final flowchartFile = File('${projectDir.path}/FLOWCHART.json');
          if (flowchartFile.existsSync()) {
            try {
              final jsonContent = flowchartFile.readAsStringSync();
              final jsonData = jsonDecode(jsonContent);
              if (jsonData != null && jsonData['projectPath'] != null) {
                print('Found project name from FLOWCHART.json: ${jsonData['projectPath']}');
              }
            } catch (e) {
              print('Error reading project name from FLOWCHART.json: $e');
            }
          }
        }
      } catch (e) {
        print('Error processing local recent project: $e');
        project.imagePath = 'assets/images/logo.png';
      }
    } else if (project.type == 'workshop') {
      // 工作坊项目：使用WorkshopPath获取图片和项目名称
      try {
        // 获取工作坊项目中的第一张图片作为封面
        if (project.imagePath == null || project.imagePath!.isEmpty) {
          project.imagePath = await WorkshopPath.getFirstImagePath(project.projectPath);
          if (project.imagePath == null || project.imagePath!.isEmpty) {
            project.imagePath = 'assets/images/logo.png';
            print('No workshop image found for recent project, using default');
          } else {
            print('Found workshop image for recent project: ${project.imagePath}');
          }
        }
        
        // 设置工作坊项目完整路径
        if (project.projectPath == null || project.projectPath!.isEmpty) {
          project.projectPath = project.projectPath;
          print('Set workshop project path: ${project.projectPath}');
        }
        
        // 从工作坊项目的FLOWCHART.json中读取项目名称（仅用于调试信息）
        final flowchartPath = WorkshopPath.getFlowchartPath(project.projectPath);
        final flowchartFile = File(flowchartPath);
        if (flowchartFile.existsSync()) {
          try {
            final jsonContent = flowchartFile.readAsStringSync();
            final jsonData = jsonDecode(jsonContent);
            if (jsonData != null && jsonData['projectPath'] != null) {
              print('Found workshop project name from FLOWCHART.json: ${jsonData['projectPath']}');
            }
          } catch (e) {
            print('Error reading workshop project name from FLOWCHART.json: $e');
          }
        }
      } catch (e) {
        print('Error processing workshop recent project: $e');
        project.imagePath = 'assets/images/logo.png';
      }
    }
    
    final recent = getRecentProjects();
    recent.removeWhere((p) => p.projectPath == project.projectPath);
    recent.insert(0, project);
    if (recent.length > 5) recent.removeLast();
    _core.setStringList('recentProjects', recent.map((p) => jsonEncode(p.toJson())).toList());
  }

  static void saveRecentEditedProject(RecentEditedProject project) async {
    // 根据项目类型处理
    if (project.type == 'local') {
      // 本地项目：查找项目文件夹中的图片作为封面
      try {
        final projectDir = Directory(project.projectPath);
        if (projectDir.existsSync()) {
          // 查找项目文件夹中的图片作为封面
          if (project.imagePath == null || project.imagePath!.isEmpty) {
            final imageFiles = projectDir.listSync()
              .where((entity) => entity is File && 
                    (entity.path.toLowerCase().endsWith('.jpg') || 
                     entity.path.toLowerCase().endsWith('.jpeg') || 
                     entity.path.toLowerCase().endsWith('.png')))
              .toList();
            
            if (imageFiles.isNotEmpty) {
              final imagePath = (imageFiles.first as File).path;
              // 验证文件是否真的存在
              if (File(imagePath).existsSync()) {
                project.imagePath = imagePath;
                print('Found image for recent edited project: ${project.imagePath}');
              } else {
                project.imagePath = 'assets/images/logo.png';
                print('Image file exists in list but cannot be accessed: $imagePath');
              }
            } else {
              project.imagePath = 'assets/images/logo.png';
              print('No image found for recent edited project, using default');
            }
          }
          
          // 设置项目完整路径
          if (project.projectPath == null || project.projectPath!.isEmpty) {
            project.projectPath = projectDir.path;
            print('Set project path: ${project.projectPath}');
          }
        }
      } catch (e) {
        print('Error processing local recent edited project: $e');
        project.imagePath = 'assets/images/logo.png';
      }
    } else if (project.type == 'workshop') {
      // 工作坊项目：使用WorkshopPath获取图片和项目名称
      try {
        // 获取工作坊项目中的第一张图片作为封面
        if (project.imagePath == null || project.imagePath!.isEmpty) {
          project.imagePath = await WorkshopPath.getFirstImagePath(project.projectPath);
          if (project.imagePath == null || project.imagePath!.isEmpty) {
            project.imagePath = 'assets/images/logo.png';
            print('No workshop image found for recent edited project, using default');
          } else {
            print('Found workshop image for recent edited project: ${project.imagePath}');
          }
        }
        
        // 设置工作坊项目完整路径
        if (project.projectPath == null || project.projectPath!.isEmpty) {
          project.projectPath = project.projectPath;
          print('Set workshop project path: ${project.projectPath}');
        }
      } catch (e) {
        print('Error processing workshop recent edited project: $e');
        project.imagePath = 'assets/images/logo.png';
      }
    }
    
    final recent = getRecentEditedProjects();
    recent.removeWhere((p) => p.projectPath == project.projectPath);
    recent.insert(0, project);
    if (recent.length > 5) recent.removeLast();
    _core.setStringList('recentEditedProjects', recent.map((p) => jsonEncode(p.toJson())).toList());
  }
}

Locale stringToLocale(String str) {
  final list = str.split('-');
  return Locale(list.first, list.elementAtOrNull(1));
}
