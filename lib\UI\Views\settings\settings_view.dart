import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ve/generated/l10n.dart';  // Import the generated localization class
import 'package:ve/main.dart';  // Add this import
import 'package:ve/UI/Views/settings/credits_view.dart';  // 导入制作人员名单视图

class SettingsView extends StatefulWidget {
  const SettingsView({super.key});

  @override
  State<SettingsView> createState() => _SettingsViewState();
}

class _SettingsViewState extends State<SettingsView> {
  bool _isFullScreen = false;//主窗口全屏
  bool _newWindowFullScreen = false; // 修改变量名，用于设置新窗口是否全屏
  bool _useNewWindowForEditing = true; // 是否使用新窗口打开编辑项目
  bool _useNewWindowForPlaying = true; // 是否使用新窗口打开游戏项目
  bool _isVerticalLayout = false;
  bool _autoSaveEnabled = true; // 是否启用自动保存
  double _autoSaveInterval = 30.0; // 自动保存间隔（分钟）
  String _autoSaveUnit = 's'; // 自动保存单位
  bool _enableFlowchartCheck = true; // 是否启用流程图检测
  bool _isPublicMode = false; // 是否启用公共场所模式/直播模式
  bool _enableVideoClickPause = false; // 是否允许点击视频区域暂停播放
  late Locale _currentLocale;
  late SharedPreferences _prefs;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _currentLocale = Localizations.localeOf(context);
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    _prefs = await SharedPreferences.getInstance();
    setState(() {
      _isFullScreen = _prefs.getBool('isFullScreen') ?? false;
      _newWindowFullScreen = _prefs.getBool('newWindowFullScreen') ?? false; // 加载新窗口全屏设置
      _useNewWindowForEditing = _prefs.getBool('useNewWindowForEditing') ?? true;
      _useNewWindowForPlaying = _prefs.getBool('useNewWindowForPlaying') ?? true;
      _isVerticalLayout = _prefs.getBool('isVerticalLayout') ?? false;
      _autoSaveEnabled = _prefs.getBool('enableAutoSave') ?? true;
      _autoSaveInterval = _prefs.getDouble('autoSaveInterval') ?? 30.0;
      _autoSaveUnit = _prefs.getString('autoSaveUnit') ?? 's';
      // 确保值在有效范围内
      if (_autoSaveUnit == 's') {
        if (_autoSaveInterval < 10) {
          _autoSaveInterval = 10.0;
        } else if (_autoSaveInterval > 69) {
          _autoSaveInterval = 69.0;
        }
      } else {
        if (_autoSaveInterval < 1) {
          _autoSaveInterval = 1.0;
        } else if (_autoSaveInterval > 60) {
          _autoSaveInterval = 60.0;
        }
      }
      _enableFlowchartCheck = _prefs.getBool('enableFlowchartCheck') ?? true;
      _isPublicMode = _prefs.getBool('isPublicMode') ?? false; // 加载公共场所模式设置
      _enableVideoClickPause = _prefs.getBool('enableVideoClickPause') ?? false; // 加载视频点击暂停设置
    });
  }

  Future<void> _saveSettings() async {
    await _prefs.setBool('isFullScreen', _isFullScreen);
    await _prefs.setBool('newWindowFullScreen', _newWindowFullScreen); // 保存新窗口全屏设置
    await _prefs.setBool('useNewWindowForEditing', _useNewWindowForEditing);
    await _prefs.setBool('useNewWindowForPlaying', _useNewWindowForPlaying);
    await _prefs.setBool('isVerticalLayout', _isVerticalLayout);
    await _prefs.setString('language', _currentLocale.languageCode);
    await _prefs.setBool('enableAutoSave', _autoSaveEnabled);
    await _prefs.setDouble('autoSaveInterval', _autoSaveInterval);
    await _prefs.setString('autoSaveUnit', _autoSaveUnit);
    await _prefs.setBool('enableFlowchartCheck', _enableFlowchartCheck);
    await _prefs.setBool('isPublicMode', _isPublicMode); // 保存公共场所模式设置
    await _prefs.setBool('enableVideoClickPause', _enableVideoClickPause); // 保存视频点击暂停设置
  }

  String _getLanguageName(Locale locale) {
    switch('${locale.languageCode}_${locale.countryCode}') {
      case 'zh_CN': return '简体中文';
      case 'en': return 'English';
      case 'fr': return 'Français';
      case 'it': return 'Italiano';
      case 'de': return 'Deutsch';
      case 'es': return 'Español';
      case 'da': return 'Dansk';
      case 'uk': return 'Українська';
      case 'ru': return 'Русский';
      case 'bg': return 'Български';
      case 'hu': return 'Magyar';
      case 'id': return 'Bahasa Indonesia';
      case 'tr': return 'Türkçe';
      case 'el': return 'Ελληνικά';
      case 'nb': return 'Norsk';
      case 'cs': return 'Čeština';
      case 'ja': return '日本語';
      case 'pl': return 'Polski';
      case 'th': return 'ไทย';
      case 'sv': return 'Svenska';
      case 'zh': return '繁體中文';
      case 'ro': return 'Română';
      case 'fi': return 'Suomi';
      case 'nl': return 'Nederlands';
      case 'pt': return 'Português (Brasil)';
      case 'ptpt': return 'Português (Portugal)';
      case 'vi': return 'Tiếng Việt';
      case 'ar': return 'العربية';
      case 'ko': return '한국어';
      case 'uz': return 'O\'zbek';
      case 'ur': return 'اردو';
      case 'hy': return 'Հայերեն';
      case 'ig': return 'Igbo';
      case 'sd': return 'سنڌي';
      case 'si': return 'සිංහල';
      case 'hr': return 'Hrvatski';
      case 'is': return 'Íslenska';
      case 'gl': return 'Galego';
      case 'ca': return 'Català';
      case 'af': return 'Afrikaans';
      case 'kn': return 'ಕನ್ನಡ';
      case 'hi': return 'हिन्दी';
      case 'gu': return 'ગુજરાતી';
      case 'ky': return 'Кыргызча';
      case 'kk': return 'Қазақ';
      case 'tk': return 'Türkmen';
      case 'rw': return 'Kinyarwanda';
      case 'sr': return 'Српски';
      case 'or': return 'ଓଡ଼ିଆ';
      case 'cy': return 'Cymraeg';
      case 'gom': return 'कोंकणी';
      case 'bn': return 'বাংলা';
      case 'ne': return 'नेपाली';
      case 'eu': return 'Euskara';
      case 'he': return 'עברית';
      case 'lv': return 'Latviešu';
      case 'ti': return 'ትግርኛ';
      case 'sk': return 'Slovenčina';
      case 'sl': return 'Slovenščina';
      case 'sw': return 'Kiswahili';
      case 'pa': return 'ਪੰਜਾਬੀ';
      case 'ka': return 'ქართული';
      case 'mi': return 'Te Reo Māori';
      case 'bs': return 'Bosanski';
      case 'fa': return 'فارسی';
      case 'te': return 'తెలుగు';
      case 'ta': return 'தமிழ்';
      case 'ga': return 'Gaeilge';
      case 'et': return 'Eesti';
      case 'zu': return 'isiZulu';
      case 'xh': return 'isiXhosa';
      case 'lt': return 'Lietuvių';
      case 'st': return 'Sesotho';
      case 'yo': return 'Yorùbá';
      case 'ug': return 'ئۇيغۇرچە';
      case 'tn': return 'Setswana';
      case 'fil': return 'Filipino';
      case 'mn': return 'Монгол';
      case 'ha': return 'Hausa';
      case 'prs': return 'دری';
      case 'az': return 'Azərbaycan';
      case 'am': return 'አማርኛ';
      case 'sq': return 'Shqip';
      case 'as': return 'অসমীয়া';
      case 'tt': return 'Татар';
      case 'mk': return 'Македонски';
      case 'mr': return 'मराठी';
      case 'ml': return 'മലയാളം';
      case 'ms': return 'Bahasa Melayu';
      case 'mt': return 'Malti';
      case 'km': return 'ខ្មែរ';
      default: return 'English';
    }
  }

  void _changeLanguage(String? languageName) {
    if (languageName == null) return;
    Locale newLocale;
    switch(languageName) {
      case '简体中文': newLocale = const Locale('zh', 'CN'); break;
      case 'English': newLocale = const Locale('en'); break;
      case 'Français': newLocale = const Locale('fr'); break;
      case 'Italiano': newLocale = const Locale('it'); break;
      case 'Deutsch': newLocale = const Locale('de'); break;
      case 'Español': newLocale = const Locale('es'); break;
      case 'Dansk': newLocale = const Locale('da'); break;
      case 'Українська': newLocale = const Locale('uk'); break;
      case 'Русский': newLocale = const Locale('ru'); break;
      case 'Български': newLocale = const Locale('bg'); break;
      case 'Magyar': newLocale = const Locale('hu'); break;
      case 'Bahasa Indonesia': newLocale = const Locale('id'); break;
      case 'Türkçe': newLocale = const Locale('tr'); break;
      case 'Ελληνικά': newLocale = const Locale('el'); break;
      case 'Norsk': newLocale = const Locale('nb'); break;
      case 'Čeština': newLocale = const Locale('cs'); break;
      case '日本語': newLocale = const Locale('ja'); break;
      case 'Polski': newLocale = const Locale('pl'); break;
      case 'ไทย': newLocale = const Locale('th'); break;
      case 'Svenska': newLocale = const Locale('sv'); break;
      case '繁體中文': newLocale = const Locale('zh'); break;
      case 'Română': newLocale = const Locale('ro'); break;
      case 'Suomi': newLocale = const Locale('fi'); break;
      case 'Nederlands': newLocale = const Locale('nl'); break;
      case 'Português (Brasil)': newLocale = const Locale('pt'); break;
      case 'Português (Portugal)': newLocale = const Locale('ptpt'); break;
      case 'Tiếng Việt': newLocale = const Locale('vi'); break;
      case 'العربية': newLocale = const Locale('ar'); break;
      case '한국어': newLocale = const Locale('ko'); break;
      case 'O\'zbek': newLocale = const Locale('uz'); break;
      case 'اردو': newLocale = const Locale('ur'); break;
      case 'Հայերեն': newLocale = const Locale('hy'); break;
      case 'Igbo': newLocale = const Locale('ig'); break;
      case 'سنڌي': newLocale = const Locale('sd'); break;
      case 'සිංහල': newLocale = const Locale('si'); break;
      case 'Hrvatski': newLocale = const Locale('hr'); break;
      case 'Íslenska': newLocale = const Locale('is'); break;
      case 'Galego': newLocale = const Locale('gl'); break;
      case 'Català': newLocale = const Locale('ca'); break;
      case 'Afrikaans': newLocale = const Locale('af'); break;
      case 'ಕನ್ನಡ': newLocale = const Locale('kn'); break;
      case 'हिन्दी': newLocale = const Locale('hi'); break;
      case 'ગુજરાતી': newLocale = const Locale('gu'); break;
      case 'Кыргызча': newLocale = const Locale('ky'); break;
      case 'Қазақ': newLocale = const Locale('kk'); break;
      case 'Türkmen': newLocale = const Locale('tk'); break;
      case 'Kinyarwanda': newLocale = const Locale('rw'); break;
      case 'Српски': newLocale = const Locale('sr'); break;
      case 'ଓଡ଼ିଆ': newLocale = const Locale('or'); break;
      case 'Cymraeg': newLocale = const Locale('cy'); break;
      case 'कोंकणी': newLocale = const Locale('gom'); break;
      case 'বাংলা': newLocale = const Locale('bn'); break;
      case 'नेपाली': newLocale = const Locale('ne'); break;
      case 'Euskara': newLocale = const Locale('eu'); break;
      case 'עברית': newLocale = const Locale('he'); break;
      case 'Latviešu': newLocale = const Locale('lv'); break;
      case 'ትግርኛ': newLocale = const Locale('ti'); break;
      case 'Slovenčina': newLocale = const Locale('sk'); break;
      case 'Slovenščina': newLocale = const Locale('sl'); break;
      case 'Kiswahili': newLocale = const Locale('sw'); break;
      case 'ਪੰਜਾਬੀ': newLocale = const Locale('pa'); break;
      case 'ქართული': newLocale = const Locale('ka'); break;
      case 'Te Reo Māori': newLocale = const Locale('mi'); break;
      case 'Bosanski': newLocale = const Locale('bs'); break;
      case 'فارسی': newLocale = const Locale('fa'); break;
      case 'తెలుగు': newLocale = const Locale('te'); break;
      case 'தமிழ்': newLocale = const Locale('ta'); break;
      case 'Gaeilge': newLocale = const Locale('ga'); break;
      case 'Eesti': newLocale = const Locale('et'); break;
      case 'isiZulu': newLocale = const Locale('zu'); break;
      case 'isiXhosa': newLocale = const Locale('xh'); break;
      case 'Lietuvių': newLocale = const Locale('lt'); break;
      case 'Sesotho': newLocale = const Locale('st'); break;
      case 'Yorùbá': newLocale = const Locale('yo'); break;
      case 'ئۇيغۇرچە': newLocale = const Locale('ug'); break;
      case 'Setswana': newLocale = const Locale('tn'); break;
      case 'Filipino': newLocale = const Locale('fil'); break;
      case 'Монгол': newLocale = const Locale('mn'); break;
      case 'Hausa': newLocale = const Locale('ha'); break;
      case 'دری': newLocale = const Locale('prs'); break;
      case 'Azərbaycan': newLocale = const Locale('az'); break;
      case 'አማርኛ': newLocale = const Locale('am'); break;
      case 'Shqip': newLocale = const Locale('sq'); break;
      case 'অসমীয়া': newLocale = const Locale('as'); break;
      case 'Татар': newLocale = const Locale('tt'); break;
      case 'Македонски': newLocale = const Locale('mk'); break;
      case 'मराठी': newLocale = const Locale('mr'); break;
      case 'മലയാളം': newLocale = const Locale('ml'); break;
      case 'Bahasa Melayu': newLocale = const Locale('ms'); break;
      case 'Malti': newLocale = const Locale('mt'); break;
      case 'ខ្មែរ': newLocale = const Locale('km'); break;
      default: newLocale = const Locale('en', 'US');
    }

    final app = context.findAncestorStateOfType<MyAppState>();
    if (app != null) {
      app.updateLocale(newLocale);
      setState(() {
        _currentLocale = newLocale;
      });
    }
  }

  void _openCreditsView() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const CreditsView(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final localizations = S.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.settings),
        backgroundColor: Colors.blueAccent[400],
      ),
      body: Container(
        padding: EdgeInsets.all(20.w),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Card(
                child: ListTile(
                  leading: const Icon(Icons.language),
                  title: Text(localizations.language),
                  trailing: DropdownButton<String>(
                    value: _getLanguageName(_currentLocale),
                    items: const [
                      DropdownMenuItem(
                        value: '简体中文',
                        child: Text('简体中文'),
                      ),
                      DropdownMenuItem(
                        value: 'English',
                        child: Text('English'),
                      ),
                      DropdownMenuItem(
                        value: 'Français',
                        child: Text('Français'),
                      ),
                      DropdownMenuItem(
                        value: 'Italiano',
                        child: Text('Italiano'),
                      ),
                      DropdownMenuItem(
                        value: 'Deutsch',
                        child: Text('Deutsch'),
                      ),
                      DropdownMenuItem(
                        value: 'Español',
                        child: Text('Español (España)'),
                      ),
                      DropdownMenuItem(
                        value: 'Español',
                        child: Text('Español (Latinoamérica)'),
                      ),
                      DropdownMenuItem(
                        value: 'Dansk',
                        child: Text('Dansk'),
                      ),
                      DropdownMenuItem(
                        value: 'Українська',
                        child: Text('Українська'),
                      ),
                      DropdownMenuItem(
                        value: 'Русский',
                        child: Text('Русский'),
                      ),
                      DropdownMenuItem(
                        value: 'Български',
                        child: Text('Български'),
                      ),
                      DropdownMenuItem(
                        value: 'Magyar',
                        child: Text('Magyar'),
                      ),
                      DropdownMenuItem(
                        value: 'Bahasa Indonesia',
                        child: Text('Bahasa Indonesia'),
                      ),
                      DropdownMenuItem(
                        value: 'Türkçe',
                        child: Text('Türkçe'),
                      ),
                      DropdownMenuItem(
                        value: 'Ελληνικά',
                        child: Text('Ελληνικά'),
                      ),
                      DropdownMenuItem(
                        value: 'Norsk',
                        child: Text('Norsk'),
                      ),
                      DropdownMenuItem(
                        value: 'Čeština',
                        child: Text('Čeština'),
                      ),
                      DropdownMenuItem(
                        value: '日本語',
                        child: Text('日本語'),
                      ),
                      DropdownMenuItem(
                        value: 'Polski',
                        child: Text('Polski'),
                      ),
                      DropdownMenuItem(
                        value: 'ไทย',
                        child: Text('ไทย'),
                      ),
                      DropdownMenuItem(
                        value: 'Svenska',
                        child: Text('Svenska'),
                      ),
                      DropdownMenuItem(
                        value: '繁體中文',
                        child: Text('繁體中文'),
                      ),
                      DropdownMenuItem(
                        value: 'Română',
                        child: Text('Română'),
                      ),
                      DropdownMenuItem(
                        value: 'Suomi',
                        child: Text('Suomi'),
                      ),
                      DropdownMenuItem(
                        value: 'Nederlands',
                        child: Text('Nederlands'),
                      ),
                      DropdownMenuItem(
                        value: 'Português (Brasil)',
                        child: Text('Português (Brasil)'),
                      ),
                      DropdownMenuItem(
                        value: 'Português (Portugal)',
                        child: Text('Português (Portugal)'),
                      ),
                      DropdownMenuItem(
                        value: 'Tiếng Việt',
                        child: Text('Tiếng Việt'),
                      ),
                      DropdownMenuItem(
                        value: 'العربية',
                        child: Text('العربية'),
                      ),
                      DropdownMenuItem(
                        value: '한국어',
                        child: Text('한국어'),
                      ),
                      DropdownMenuItem(
                        value: 'O\'zbek',
                        child: Text('O\'zbek'),
                      ),
                      DropdownMenuItem(
                        value: 'اردو',
                        child: Text('اردو'),
                      ),
                      DropdownMenuItem(
                        value: 'Հայերեն',
                        child: Text('Հայերեն'),
                      ),
                      DropdownMenuItem(
                        value: 'Igbo',
                        child: Text('Igbo'),
                      ),
                      DropdownMenuItem(
                        value: 'سنڌي',
                        child: Text('سنڌي'),
                      ),
                      DropdownMenuItem(
                        value: 'සිංහල',
                        child: Text('සිංහල'),
                      ),
                      DropdownMenuItem(
                        value: 'Hrvatski',
                        child: Text('Hrvatski'),
                      ),
                      DropdownMenuItem(
                        value: 'Íslenska',
                        child: Text('Íslenska'),
                      ),
                      DropdownMenuItem(
                        value: 'Galego',
                        child: Text('Galego'),
                      ),
                      DropdownMenuItem(
                        value: 'Català',
                        child: Text('Català'),
                      ),
                      DropdownMenuItem(
                        value: 'Afrikaans',
                        child: Text('Afrikaans'),
                      ),
                      DropdownMenuItem(
                        value: 'ಕನ್ನಡ',
                        child: Text('ಕನ್ನಡ'),
                      ),
                      DropdownMenuItem(
                        value: 'हिन्दी',
                        child: Text('हिन्दी'),
                      ),
                      DropdownMenuItem(
                        value: 'ગુજરાતી',
                        child: Text('ગુજરાતી'),
                      ),
                      DropdownMenuItem(
                        value: 'Кыргызча',
                        child: Text('Кыргызча'),
                      ),
                      DropdownMenuItem(
                        value: 'Қазақ',
                        child: Text('Қазақ'),
                      ),
                      DropdownMenuItem(
                        value: 'Türkmen',
                        child: Text('Türkmen'),
                      ),
                      DropdownMenuItem(
                        value: 'Kinyarwanda',
                        child: Text('Kinyarwanda'),
                      ),
                      DropdownMenuItem(
                        value: 'Српски',
                        child: Text('Српски'),
                      ),
                      DropdownMenuItem(
                        value: 'ଓଡ଼ିଆ',
                        child: Text('ଓଡ଼ିଆ'),
                      ),
                      DropdownMenuItem(
                        value: 'Cymraeg',
                        child: Text('Cymraeg'),
                      ),
                      DropdownMenuItem(
                        value: 'कोंकणी',
                        child: Text('कोंकणी'),
                      ),
                      DropdownMenuItem(
                        value: 'বাংলা',
                        child: Text('বাংলা'),
                      ),
                      DropdownMenuItem(
                        value: 'नेपाली',
                        child: Text('नेपाली'),
                      ),
                      DropdownMenuItem(
                        value: 'Euskara',
                        child: Text('Euskara'),
                      ),
                      DropdownMenuItem(
                        value: 'עברית',
                        child: Text('עברית'),
                      ),
                      DropdownMenuItem(
                        value: 'Latviešu',
                        child: Text('Latviešu'),
                      ),
                      DropdownMenuItem(
                        value: 'ትግርኛ',
                        child: Text('ትግርኛ'),
                      ),
                      DropdownMenuItem(
                        value: 'Slovenčina',
                        child: Text('Slovenčina'),
                      ),
                      DropdownMenuItem(
                        value: 'Slovenščina',
                        child: Text('Slovenščina'),
                      ),
                      DropdownMenuItem(
                        value: 'Kiswahili',
                        child: Text('Kiswahili'),
                      ),
                      DropdownMenuItem(
                        value: 'ਪੰਜਾਬੀ',
                        child: Text('ਪੰਜਾਬੀ'),
                      ),
                      DropdownMenuItem(
                        value: 'ქართული',
                        child: Text('ქართული'),
                      ),
                      DropdownMenuItem(
                        value: 'Te Reo Māori',
                        child: Text('Te Reo Māori'),
                      ),
                      DropdownMenuItem(
                        value: 'Bosanski',
                        child: Text('Bosanski'),
                      ),
                      DropdownMenuItem(
                        value: 'فارسی',
                        child: Text('فارسی'),
                      ),
                      DropdownMenuItem(
                        value: 'తెలుగు',
                        child: Text('తెలుగు'),
                      ),
                      DropdownMenuItem(
                        value: 'தமிழ்',
                        child: Text('தமிழ்'),
                      ),
                      DropdownMenuItem(
                        value: 'Gaeilge',
                        child: Text('Gaeilge'),
                      ),
                      DropdownMenuItem(
                        value: 'Eesti',
                        child: Text('Eesti'),
                      ),
                      DropdownMenuItem(
                        value: 'isiZulu',
                        child: Text('isiZulu'),
                      ),
                      DropdownMenuItem(
                        value: 'isiXhosa',
                        child: Text('isiXhosa'),
                      ),
                      DropdownMenuItem(
                        value: 'Lietuvių',
                        child: Text('Lietuvių'),
                      ),
                      DropdownMenuItem(
                        value: 'Setswana',
                        child: Text('Setswana'),
                      ),
                      DropdownMenuItem(
                        value: 'Yorùbá',
                        child: Text('Yorùbá'),
                      ),
                      DropdownMenuItem(
                        value: 'ئۇيغۇرچە',
                        child: Text('ئۇيغۇرچە'),
                      ),
                      DropdownMenuItem(
                        value: 'Filipino',
                        child: Text('Filipino'),
                      ),
                      DropdownMenuItem(
                        value: 'Монгол',
                        child: Text('Монгол'),
                      ),
                      DropdownMenuItem(
                        value: 'Hausa',
                        child: Text('Hausa'),
                      ),
                      DropdownMenuItem(
                        value: 'دری',
                        child: Text('دری'),
                      ),
                      DropdownMenuItem(
                        value: 'Azərbaycan',
                        child: Text('Azərbaycan'),
                      ),
                      DropdownMenuItem(
                        value: 'አማርኛ',
                        child: Text('አማርኛ'),
                      ),
                      DropdownMenuItem(
                        value: 'Shqip',
                        child: Text('Shqip'),
                      ),
                      DropdownMenuItem(
                        value: 'অসমীয়া',
                        child: Text('অসমীয়া'),
                      ),
                      DropdownMenuItem(
                        value: 'Татар',
                        child: Text('Татар'),
                      ),
                      DropdownMenuItem(
                        value: 'Македонски',
                        child: Text('Македонски'),
                      ),
                      DropdownMenuItem(
                        value: 'मराठी',
                        child: Text('मराठी'),
                      ),
                      DropdownMenuItem(
                        value: 'മലയാളം',
                        child: Text('മലയാളം'),
                      ),
                      DropdownMenuItem(
                        value: 'Malti',
                        child: Text('Malti'),
                      ),
                      DropdownMenuItem(
                        value: 'Bahasa Melayu',
                        child: Text('Bahasa Melayu'),
                      ),
                      DropdownMenuItem(
                        value: 'Malti',
                        child: Text('Malti'),
                      ),
                      DropdownMenuItem(
                        value: 'ខ្មែរ',
                        child: Text('ខ្មែរ'),
                      ),
                    ],
                    onChanged: _changeLanguage,
                  ),
                ),
              ),
              
              SizedBox(height: 20.h),
              
              Card(
                child: ListTile(
                  leading: const Icon(Icons.fullscreen),
                  title: Text(localizations.mainWindowFullscreen),
                  trailing: Switch(
                    value: _isFullScreen,
                    onChanged: (bool value) async {
                      setState(() {
                        _isFullScreen = value;
                      });
                      await _saveSettings();
                      
                      // 调用全局的toggleFullScreen方法应用全屏设置
                      final app = context.findAncestorStateOfType<MyAppState>();
                      if (app != null) {
                        app.toggleFullScreen();
                      }
                    },
                  ),
                ),
              ),
              SizedBox(height: 20.h),
              Card(
                child: ListTile(
                  leading: const Icon(Icons.open_in_new),
                  title: Text(localizations.newWindowFullScreen), // 需要在本地化文件中添加对应的翻译
                  // subtitle: Text(localizations.newWindowFullScreenDescription), // 需要在本地化文件中添加对应的翻译
                  trailing: Switch(
                    value: _newWindowFullScreen,
                    onChanged: (bool value) async {
                      setState(() {
                        _newWindowFullScreen = value;
                      });
                      await _saveSettings();
                      
                      // 添加mounted检查
                      if (mounted) {
                        // 调用全局的toggleNewWindowFullScreen方法
                        final app = context.findAncestorStateOfType<MyAppState>();
                        if (app != null) {
                          app.toggleNewWindowFullScreen();
                        }
                      }
                    },
                  ),
                ),
              ),
              SizedBox(height: 20.h),
              Card(
                child: ListTile(
                  leading: const Icon(Icons.open_in_new),
                  title: Text(localizations.useNewWindowForEditing),
                  subtitle: Text(localizations.useNewWindowForEditingDescription),
                  trailing: Switch(
                    value: _useNewWindowForEditing,
                    onChanged: (bool value) async {
                      setState(() {
                        _useNewWindowForEditing = value;
                      });
                      await _saveSettings();
                      
                      // 添加mounted检查
                      if (mounted) {
                        // 调用全局的toggleUseNewWindowForEditing方法
                        final app = context.findAncestorStateOfType<MyAppState>();
                        if (app != null) {
                          app.toggleUseNewWindowForEditing();
                        }
                      }
                    },
                  ),
                ),
              ),
              SizedBox(height: 20.h),
              Card(
                child: ListTile(
                  leading: const Icon(Icons.open_in_new),
                  title: Text(S.of(context).openGameInNewWindow),
                  subtitle: Text(S.of(context).openGameInNewWindowDesc),
                  trailing: Switch(
                    value: _useNewWindowForPlaying,
                    onChanged: (bool value) async {
                      setState(() {
                        _useNewWindowForPlaying = value;
                      });
                      await _saveSettings();
                      
                      // 添加mounted检查
                      if (mounted) {
                        // 调用全局的toggleUseNewWindowForPlaying方法
                        final app = context.findAncestorStateOfType<MyAppState>();
                        if (app != null) {
                          app.toggleUseNewWindowForPlaying();
                        }
                      }
                    },
                  ),
                ),
              ),
              SizedBox(height: 20.h),
              Card(
                child: ListTile(
                  leading: Icon(_isVerticalLayout ? Icons.view_stream : Icons.view_column),
                  title: Text(S.of(context).verticalLayoutDescription),
                  trailing: Switch(
                    value: _isVerticalLayout,
                    onChanged: (bool value) async {
                      setState(() {
                        _isVerticalLayout = value;
                      });
                      await _saveSettings();
                      
                      // 添加mounted检查
                      if (mounted) {
                        // 调用全局的toggleVerticalLayout方法
                        final app = context.findAncestorStateOfType<MyAppState>();
                        if (app != null) {
                          app.toggleVerticalLayout();
                        }
                      }
                    },
                  ),
                ),
              ),
              
              // 添加自动保存设置
              SizedBox(height: 20.h),
              Card(
                child: Column(
                  children: [
                    ListTile(
                      leading: const Icon(Icons.save),
                      title: Text(S.of(context).autoSaveGame),
                      trailing: Switch(
                        value: _autoSaveEnabled,
                        onChanged: (bool value) async {
                          setState(() {
                            _autoSaveEnabled = value;
                          });
                          await _saveSettings();
                        },
                      ),
                    ),
                    if (_autoSaveEnabled) // 只有在启用自动保存时才显示间隔设置
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                        child: Row(
                          children: [
                            Text('${S.of(context).autoSaveInterval}：'),
                            Expanded(
                              child: Slider(
                                value: _autoSaveInterval,
                                min: _autoSaveUnit == 's' ? 10 : 1, // 秒为单位时最小值为10，分钟为单位时最小值为1
                                max: _autoSaveUnit == 's' ? 69 : 60, // 秒为单位时最大值为69，分钟为单位时最大值为60
                                divisions: _autoSaveUnit == 's' ? 59 : 59, // 秒：(69-10)=59个分割，分钟：(60-1)=59个分割
                                label: '${_autoSaveInterval.round()}',
                                onChanged: (value) async {
                                  setState(() {
                                    _autoSaveInterval = value;
                                    // 当切换单位时，确保值在有效范围内
                                    if (_autoSaveUnit == 's') {
                                      // 切换到秒：最小值10，最大值69
                                      if (_autoSaveInterval < 10) {
                                        _autoSaveInterval = 10;
                                      } else if (_autoSaveInterval > 69) {
                                        _autoSaveInterval = 69;
                                      }
                                    } else {
                                      // 切换到分钟：最小值1，最大值60
                                      if (_autoSaveInterval < 1) {
                                        _autoSaveInterval = 1;
                                      } else if (_autoSaveInterval > 60) {
                                        _autoSaveInterval = 60;
                                      }
                                    }
                                  });
                                  await _saveSettings();
                                },
                              ),
                            ),
                            Text('${_autoSaveInterval.round()}'),
                            DropdownButton<String>(
                              value: _autoSaveUnit,
                              items: const [
                                DropdownMenuItem(value: 'min', child: Text('min')),
                                DropdownMenuItem(value: 's', child: Text('s')),
                              ],
                              onChanged: (value) async {
                                setState(() {
                                  _autoSaveUnit = value!;
                                  // 当切换单位时，确保值在有效范围内
                                  if (_autoSaveUnit == 's') {
                                    // 切换到秒：最小值10，最大值69
                                    if (_autoSaveInterval < 10) {
                                      _autoSaveInterval = 10;
                                    } else if (_autoSaveInterval > 69) {
                                      _autoSaveInterval = 69;
                                    }
                                  } else {
                                    // 切换到分钟：最小值1，最大值60
                                    if (_autoSaveInterval < 1) {
                                      _autoSaveInterval = 1;
                                    } else if (_autoSaveInterval > 60) {
                                      _autoSaveInterval = 60;
                                    }
                                  }
                                });
                                await _saveSettings();
                              },
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
              SizedBox(height: 20.h),
              Card(
                child: ListTile(
                  leading: const Icon(Icons.check_circle),
                  title: Text(S.of(context).enableFlowchartCheck),
                  subtitle: Text(S.of(context).flowchartCheckDescription),
                  trailing: Switch(
                    value: _enableFlowchartCheck,
                    onChanged: (bool value) async {
                      setState(() {
                        _enableFlowchartCheck = value;
                      });
                      await _saveSettings();
                    },
                  ),
                ),
              ),
              SizedBox(height: 20.h),
              Card(
                child: ListTile(
                  leading: const Icon(Icons.public),
                  title: Text(S.of(context).publicMode),
                  subtitle: Text(S.of(context).publicModeDesc),
                  trailing: Switch(
                    value: _isPublicMode,
                    onChanged: (bool value) async {
                      setState(() {
                        _isPublicMode = value;
                      });
                      await _saveSettings();
                      
                      // 添加mounted检查
                      if (mounted) {
                        // 调用全局的togglePublicMode方法（需要在MyAppState中添加）
                        final app = context.findAncestorStateOfType<MyAppState>();
                        if (app != null) {
                          app.togglePublicMode(value);
                        }
                      }
                    },
                  ),
                ),
              ),
              
              // 添加视频点击暂停播放设置
              SizedBox(height: 20.h),
              Card(
                child: ListTile(
                  leading: const Icon(Icons.touch_app),
                  title: Text(S.of(context).enableVideoClickPause),
                  subtitle: Text(S.of(context).enableVideoClickPauseDesc),
                  trailing: Switch(
                    value: _enableVideoClickPause,
                    onChanged: (bool value) async {
                      setState(() {
                        _enableVideoClickPause = value;
                      });
                      await _saveSettings();
                    },
                  ),
                ),
              ),
              
              // 添加制作人员名单入口
              SizedBox(height: 20.h),
              Card(
                child: ListTile(
                  leading: const Icon(Icons.people_alt),
                  title: Text(S.of(context).credits),
                  subtitle: Text(S.of(context).creditsSubtitle),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: _openCreditsView,
                ),
              ),
              
              SizedBox(height: 40.h),
            ],
          ),
        ),
      ),
    );
  }
}
