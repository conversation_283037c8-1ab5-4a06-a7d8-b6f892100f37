#!/bin/bash

# 定义要处理的文件列表
files=("pubspec.yaml" "mypackage/flutter_flow_chart/pubspec.yaml")

process_swk() {
  local file=$1
  awk '
    BEGIN { n=0 }
    /^ *[[:space:]]*#swk/ { 
      in_block=1; 
      print; 
      next 
    }
    in_block && n<3 {
      # 保存行首的所有空白字符（包括制表符）
      match($0, /^ */);
      indent = substr($0, 1, RLENGTH);

      # 提取去除缩进后的部分
      rest = substr($0, RLENGTH + 1);

      if (rest ~ /^#[[:space:]]?/) {
        # 如果行是以 # 开头，则删除 # 和紧随其后的第一个非空白字符（如果有）
        sub(/^#[[:space:]]?/, "", rest);
      } else {
        # 如果不是以 # 开头，则在行首添加 # 和一个空格
        rest = "# " rest;
      }

      # 如果是 path: 行且原始缩进少于4个空格，则增加到4个空格
      if (rest ~ /^path:/ && length(indent) < 4) {
        indent = indent "    ";  # 确保至少有4个空格的缩进
        indent = substr(indent, 1, 4);  # 只保留前4个字符作为缩进
      }

      $0 = indent rest;
      n++;
      print;
      if (n==3) in_block=0;
      next;
    }
    { 
      print; 
      in_block=0; 
      n=0;
    }
  ' "$file" > "${file}.tmp" && mv "${file}.tmp" "$file"
}

for file in "${files[@]}"; do
  # 检查文件是否存在
  if [ ! -f "$file" ]; then
    echo "File $file not found, skipping..."
    continue
  fi

  process_swk "$file"
done