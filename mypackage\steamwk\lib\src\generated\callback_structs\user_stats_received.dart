// ignore_for_file: public_member_api_docs, always_specify_types, avoid_positional_boolean_parameters, avoid_classes_with_only_static_members
import "dart:ffi";

import "../enums/eresult.dart";
import "../typedefs.dart";

@Packed(4)
final class UserStatsReceived extends Struct {
  static int get callbackId => 1101;

  @UnsignedLongLong()
  external int gameId;

  @Int32()
  external EResultAliasDart result;

  @UnsignedLongLong()
  external CSteamId steamIdUser;
}

extension UserStatsReceivedExtensions on Pointer<UserStatsReceived> {
  int get gameId => ref.gameId;

  EResult get result => EResult.fromValue(ref.result);

  CSteamId get steamIdUser => ref.steamIdUser;
}
