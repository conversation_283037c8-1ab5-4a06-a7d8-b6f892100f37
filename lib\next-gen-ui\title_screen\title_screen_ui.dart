// Copyright 2023 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

import 'package:extra_alignments/extra_alignments.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:focusable_control_builder/focusable_control_builder.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:ve/generated/l10n.dart';
import 'package:ve/utils/coin_manager.dart';
import 'package:fullscreen_window/fullscreen_window.dart';
import 'package:ve/utils/fullscreen_manager.dart';
import 'package:ve/main.dart';
import 'dart:io';
import 'package:window_manager/window_manager.dart';

import '../assets.dart';
import '../common/shader_effect.dart';
import '../common/ticking_builder.dart';
import '../common/ui_scaler.dart';
import '../styles.dart';

class TitleScreenUi extends StatefulWidget {
  const TitleScreenUi({
    super.key,
    required this.difficulty,
    required this.onDifficultyPressed,
    required this.onDifficultyFocused,
    required this.onStartPressed,
    this.projectName = '',
  });

  final int difficulty;
  final void Function(int difficulty) onDifficultyPressed;
  final void Function(int? difficulty) onDifficultyFocused;
  final VoidCallback onStartPressed;
  final String projectName;

  @override
  State<TitleScreenUi> createState() => _TitleScreenUiState();
}

class _TitleScreenUiState extends State<TitleScreenUi> {
  late FullscreenManager _fullscreenManager;

  @override
  void initState() {
    super.initState();
    _fullscreenManager = FullscreenManager();
    
    // 检查是否在新实例窗口中，并同步全屏状态
    try {
      var parentState = WidgetsBinding.instance.rootElement?.findAncestorStateOfType<GameInstanceAppState>();
      if (parentState != null) {
        // 确保FullscreenManager的状态与GameInstanceApp一致
        _fullscreenManager.setFullscreen(parentState.isFullscreen);
        print('已同步新实例窗口的全屏状态: ${parentState.isFullscreen}');
      }
    } catch (e) {
      print('同步全屏状态时出错: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    // 获取屏幕尺寸
    final screenSize = MediaQuery.of(context).size;
    
    return SizedBox.expand( // 确保填充所有可用空间
      child: Padding(
        padding: EdgeInsets.symmetric(
          vertical: screenSize.height * 0.05, // 根据屏幕高度动态调整
          horizontal: screenSize.width * 0.05, // 根据屏幕宽度动态调整
        ),
        child: Stack(
          fit: StackFit.expand, // 确保Stack填充所有可用空间
          children: [
            /// Title Text
            TopLeft(
              child: UiScaler(
                alignment: Alignment.topLeft,
                child: _TitleText(projectName: widget.projectName),
              ),
            ),

            /// Fullscreen Button (右上角)
            TopRight(
              child: UiScaler(
                alignment: Alignment.topRight,
                child: Padding(
                  padding: EdgeInsets.only(
                    top: screenSize.height * 0.01,
                    right: screenSize.width * 0.01,
                  ),
                  child: _FullscreenBtn(
                    isFullscreen: _fullscreenManager.isFullscreen,
                    onPressed: () {
                      _fullscreenManager.toggleFullscreen();
                    },
                  ),
                ),
              ),
            ),

            /// Difficulty Btns
            BottomLeft(
              child: UiScaler(
                alignment: Alignment.bottomLeft,
                child: _DifficultyBtns(
                  difficulty: widget.difficulty,
                  onDifficultyPressed: widget.onDifficultyPressed,
                  onDifficultyFocused: widget.onDifficultyFocused,
                ),
              ),
            ),

            /// StartBtn
            BottomRight(
              child: UiScaler(
                alignment: Alignment.bottomRight,
                child: Padding(
                  padding: EdgeInsets.only(
                    bottom: screenSize.height * 0.02, // 动态调整
                    right: screenSize.width * 0.04, // 动态调整
                  ),
                  child: _StartBtn(onPressed: widget.onStartPressed),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _FullscreenBtn extends StatelessWidget {
  const _FullscreenBtn({
    required this.isFullscreen,
    required this.onPressed,
  });

  final bool isFullscreen;
  final VoidCallback onPressed;

  @override
  Widget build(BuildContext context) {
    return FocusableControlBuilder(
      onPressed: onPressed,
      builder: (_, state) {
        return Padding(
          padding: const EdgeInsets.all(8.0),
          child: SizedBox(
            width: 200,
            height: 50,
            child: Stack(
              children: [
                /// Bg with fill and outline
                AnimatedOpacity(
                  opacity: (state.isHovered || state.isFocused) ? 1 : 0,
                  duration: .3.seconds,
                  child: Container(
                    decoration: BoxDecoration(
                      color: const Color(0xFF00D1FF).withAlpha(25),
                      border: Border.all(color: Colors.white, width: 3),
                    ),
                  ),
                ),

                if (state.isHovered || state.isFocused) ...[
                  Container(
                    decoration: BoxDecoration(
                      color: const Color(0xFF00D1FF).withAlpha(25),
                    ),
                  ),
                ],

                /// Label
                Center(
                  child: Text(
                    isFullscreen ? S.of(context).exitFullscreen : S.of(context).fullscreen,
                    style: TextStyles.btn.copyWith(fontSize: 16),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    ).animate().fadeIn(delay: 1.1.seconds, duration: .35.seconds);
  }
}

class _TitleText extends StatelessWidget {
  const _TitleText({this.projectName = ''});

  final String projectName;

  @override
  Widget build(BuildContext context) {
    Widget content = Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Gap(20),
        LayoutBuilder(
          builder: (context, constraints) {
            final isCompact = constraints.maxWidth < 400;
            final displayText = projectName.isNotEmpty ? projectName.toUpperCase() : 'OUTPOST';
            
            // 根据文本长度动态计算字符间隙
            // 原始字符间隙
            final originalLetterSpacing = TextStyles.h1.letterSpacing ?? 0;
            // 文本长度越长，字符间隙越小，基于最佳可读性进行调整
            final dynamicLetterSpacing = displayText.length > 10 
                ? originalLetterSpacing * (1 - (displayText.length - 10) * 0.05).clamp(0.2, 1.0)
                : originalLetterSpacing;
            
            // 创建一个带有动态字符间隙的文本样式
            final dynamicTextStyle = TextStyles.h1.copyWith(
              letterSpacing: dynamicLetterSpacing,
            );
            
            if (isCompact) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Transform.translate(
                    offset: Offset(-(dynamicLetterSpacing * .5), 0),
                    child: Text(
                      displayText,
                      style: dynamicTextStyle,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ),
                ],
              ).animate().fadeIn(delay: .8.seconds, duration: .7.seconds);
            } else {
              return Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Flexible(
                    child: Transform.translate(
                      offset: Offset(-(dynamicLetterSpacing * .5), 0),
                      child: Text(
                        displayText, 
                        style: dynamicTextStyle,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ),
                  ),
                ],
              ).animate().fadeIn(delay: .8.seconds, duration: .7.seconds);
            }
          },
        ),
        Text('INTO THE UNKNOWN', style: TextStyles.h3)
            .animate()
            .fadeIn(delay: 1.seconds, duration: .7.seconds),
      ],
    );
    return Consumer<FragmentPrograms?>(
      builder: (context, fragmentPrograms, _) {
        if (fragmentPrograms == null) return content;
        return TickingBuilder(
          builder: (context, time) {
            return AnimatedSampler(
              (image, size, canvas) {
                const double overdrawPx = 30;
                final shader = fragmentPrograms.ui.fragmentShader();
                shader
                  ..setFloat(0, size.width)
                  ..setFloat(1, size.height)
                  ..setFloat(2, time)
                  ..setImageSampler(0, image);
                Rect rect = Rect.fromLTWH(-overdrawPx, -overdrawPx,
                    size.width + overdrawPx, size.height + overdrawPx);
                canvas.drawRect(rect, Paint()..shader = shader);
              },
              child: content,
            );
          },
        );
      },
    );
  }
}

class _DifficultyBtns extends StatelessWidget {
  const _DifficultyBtns({
    required this.difficulty,
    required this.onDifficultyPressed,
    required this.onDifficultyFocused,
  });

  final int difficulty;
  final void Function(int difficulty) onDifficultyPressed;
  final void Function(int? difficulty) onDifficultyFocused;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        const Gap(20),
        _DifficultyBtn(
          label: S.of(context).archive,
          selected: difficulty == 0,
          onPressed: () => onDifficultyPressed(0),
          onHover: (over) => onDifficultyFocused(over ? 0 : null),
        )
            .animate()
            .fadeIn(delay: 1.3.seconds, duration: .35.seconds)
            .slide(begin: const Offset(0, .2)),
        _DifficultyBtn(
          label: S.of(context).flowchart,
          selected: difficulty == 1,
          onPressed: () => onDifficultyPressed(1),
          onHover: (over) => onDifficultyFocused(over ? 1 : null),
        )
            .animate()
            .fadeIn(delay: 1.5.seconds, duration: .35.seconds)
            .slide(begin: const Offset(0, .2)),
        _DifficultyBtn(
          label: S.of(context).settings,
          selected: difficulty == 2,
          onPressed: () => onDifficultyPressed(2),
          onHover: (over) => onDifficultyFocused(over ? 2 : null),
        )
            .animate()
            .fadeIn(delay: 1.7.seconds, duration: .35.seconds)
            .slide(begin: const Offset(0, .2)),
        const Gap(20),
      ],
    );
  }
}

class _DifficultyBtn extends StatelessWidget {
  const _DifficultyBtn({
    required this.selected,
    required this.onPressed,
    required this.onHover,
    required this.label,
  });
  final String label;
  final bool selected;
  final VoidCallback onPressed;
  final void Function(bool hasFocus) onHover;

  @override
  Widget build(BuildContext context) {
    return FocusableControlBuilder(
      onPressed: onPressed,
      onHoverChanged: (_, state) => onHover.call(state.isHovered),
      builder: (_, state) {
        return Padding(
          padding: const EdgeInsets.all(8.0),
          child: SizedBox(
            width: 250,
            height: 60,
            child: Stack(
              children: [
                /// Bg with fill and outline
                AnimatedOpacity(
                  opacity: (!selected && (state.isHovered || state.isFocused))
                      ? 1
                      : 0,
                  duration: .3.seconds,
                  child: Container(
                    decoration: BoxDecoration(
                      color: const Color(0xFF00D1FF).withAlpha(25),
                      border: Border.all(color: Colors.white, width: 5),
                    ),
                  ),
                ),

                if (state.isHovered || state.isFocused) ...[
                  Container(
                    decoration: BoxDecoration(
                      color: const Color(0xFF00D1FF).withAlpha(25),
                    ),
                  ),
                ],

                /// cross-hairs (selected state)
                if (selected) ...[
                  CenterLeft(
                    child: Image.asset(AssetPaths.titleSelectedLeft),
                  ),
                  CenterRight(
                    child: Image.asset(AssetPaths.titleSelectedRight),
                  ),
                ],

                /// Label
                Center(
                  child: Text(label.toUpperCase(), style: TextStyles.btn),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

class _StartBtn extends StatefulWidget {
  const _StartBtn({required this.onPressed});
  final VoidCallback onPressed;

  @override
  State<_StartBtn> createState() => _StartBtnState();
}

class _StartBtnState extends State<_StartBtn> {
  AnimationController? _btnAnim;
  bool _wasHovered = false;

  @override
  Widget build(BuildContext context) {
    return FocusableControlBuilder(
      cursor: SystemMouseCursors.click,
      onPressed: widget.onPressed,
      builder: (_, state) {
        if ((state.isHovered || state.isFocused) &&
            !_wasHovered &&
            _btnAnim?.status != AnimationStatus.forward) {
          _btnAnim?.forward(from: 0);
        }
        _wasHovered = (state.isHovered || state.isFocused);
        return SizedBox(
          width: 520,
          height: 100,
          child: Stack(
            children: [
              Positioned.fill(child: Image.asset(AssetPaths.titleStartBtn)),
              if (state.isHovered || state.isFocused) ...[
                Positioned.fill(
                    child: Image.asset(AssetPaths.titleStartBtnHover)),
              ],
              Center(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(S.of(context).startGame,
                        textAlign: TextAlign.center,
                        style: TextStyles.btn
                            .copyWith(fontSize: 24, letterSpacing: 18)),
                  ],
                ),
              ),
            ],
          )
              .animate(autoPlay: false, onInit: (c) => _btnAnim = c)
              .shimmer(duration: .7.seconds, color: Colors.black),
        )
            .animate()
            .fadeIn(delay: 2.3.seconds)
            .slide(begin: const Offset(0, .2));
      },
    );
  }
}
