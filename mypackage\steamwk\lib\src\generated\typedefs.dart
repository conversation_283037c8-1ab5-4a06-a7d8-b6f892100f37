// ignore_for_file: public_member_api_docs, always_specify_types, avoid_positional_boolean_parameters, avoid_classes_with_only_static_members
import "dart:ffi";

typedef CSteamId = int;
typedef CGameId = int;
typedef AppId = int;
typedef DepotId = int;
typedef RTime32 = int;
typedef SteamApiCall = int;
typedef AccountId = int;
typedef PartyBeaconId = int;
typedef HAuthTicket = int;
typedef HSteamPipe = int;
typedef HSteamUser = int;
typedef SteamErrMsg = Array<Char>;
typedef FriendsGroupId = int;
typedef HServerListRequest = Pointer<Void>;
typedef HServerQuery = int;
typedef UgcHandle = int;
typedef PublishedFileUpdateHandle = int;
typedef PublishedFileId = int;
typedef UgcFileWriteStreamHandle = int;
typedef SteamLeaderboard = int;
typedef SteamLeaderboardEntries = int;
typedef SnetSocket = int;
typedef SnetListenSocket = int;
typedef ScreenshotHandle = int;
typedef HttpRequestHandle = int;
typedef HttpCookieContainerHandle = int;
typedef InputHandle = int;
typedef InputActionSetHandle = int;
typedef InputDigitalActionHandle = int;
typedef InputAnalogActionHandle = int;
typedef ControllerHandle = int;
typedef ControllerActionSetHandle = int;
typedef ControllerDigitalActionHandle = int;
typedef ControllerAnalogActionHandle = int;
typedef UgcQueryHandle = int;
typedef UgcUpdateHandle = int;
typedef HHtmlBrowser = int;
typedef SteamItemInstanceId = int;
typedef SteamItemDef = int;
typedef SteamInventoryResult = int;
typedef SteamInventoryUpdateHandle = int;
typedef RemotePlaySessionId = int;
typedef HSteamNetConnection = int;
typedef HSteamListenSocket = int;
typedef HSteamNetPollGroup = int;
typedef SteamNetworkingErrMsg = Array<Char>;
typedef SteamNetworkingPopId = int;
typedef SteamNetworkingMicroseconds = int;
