import 'dart:async';
import 'dart:convert';
import 'dart:ffi';
import 'dart:io';

import 'package:collection/collection.dart';
import 'package:ffi/ffi.dart';
import 'package:flutter/cupertino.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:steamworks/steamworks.dart';

import 'steam_file.dart';
import 'compress_image.dart';
import 'steam_ex.dart';
import 'steam_tags.dart';
import 'steam_globals.dart';

extension SteamFileEX on SteamClient {
  Future<SteamFiles> getAllItems({
    required int page,
    required SteamUGCSort sort,
    // required TagType type,
    bool subscribed = false,
    int? userId,
    String? search,
    Set<String>? tags,
  }) async {
    // debugPrint('get page $page, sort $voteType');
    final completer = Completer<SteamFiles>();
    final appId = SteamGlobals.instance.appId;
    int query;

    if (subscribed || userId != null) {
      EUserUgcListSortOrder _sort;
      switch (sort) {
        case SteamUGCSort.publishTime:
          _sort = EUserUgcListSortOrder.creationOrderDesc;
          break;
        case SteamUGCSort.vote:
          _sort = EUserUgcListSortOrder.voteScoreDesc;
          break;
        case SteamUGCSort.updateTime:
          _sort = EUserUgcListSortOrder.lastUpdatedDesc;
          break;
      }
      query = steamUgc.createQueryUserUgcRequest(
        subscribed ? this.userId : userId!,
        subscribed ? EUserUgcList.subscribed : EUserUgcList.published,
        EUgcMatchingUgcType.usableInGame,
        _sort,
        appId,
        appId,
        page,
      );
    } else {
      EUgcQuery _sort;
      switch (sort) {
        case SteamUGCSort.publishTime:
          _sort = EUgcQuery.rankedByPublicationDate;
          break;
        case SteamUGCSort.vote:
          _sort = EUgcQuery.rankedByVote;
          break;
        case SteamUGCSort.updateTime:
          _sort = EUgcQuery.rankedByLastUpdatedDate;
          break;
      }
      query = steamUgc.createQueryAllUgcRequestPage(
        _sort,
        EUgcMatchingUgcType.usableInGame,
        appId,
        appId,
        page,
      );
    }
    if (search != null) {
      steamUgc.setSearchText(query, search.toNativeUtf8());
    }
    steamUgc.setReturnKeyValueTags(query, true);
    steamUgc.setReturnMetadata(query, true);
    steamUgc.setAllowCachedResponse(query, 0);
    steamUgc.setReturnLongDescription(query, true);
    steamUgc.setReturnChildren(query, true);

    tags ??= {};
    // tags.add(type.value);
    for (var tag in tags) {
      steamUgc.addRequiredTag(query, tag.toNativeUtf8());
    }
    debugPrint('steam query tags $tags');

    registerCallResult<SteamUgcQueryCompleted>(
        asyncCallId: steamUgc.sendQueryUgcRequest(query),
        cb: (result, failed) {
          debugPrint('Query ugc items result ${{
            'query_handle': query,
            'result_handle': result.handle,
            'result': result.result,
            'total': result.totalMatchingResults,
            'current': result.numResultsReturned,
          }}');
          final List<SteamFile> details = [];
          if (result.result == EResult.eResultOK) {
            for (var i = 0; i < result.numResultsReturned; i++) {
              // debugPrint('处理 $i');
              using((arena) {
                /// https://partner.steamgames.com/doc/api/ISteamUGC#GetQueryUGCStatistic
                final commentsNumber = arena<UnsignedLongLong>();
                final subscribersNumber = arena<UnsignedLongLong>();
                final favoritesNumber = arena<UnsignedLongLong>();
                
                // 获取评论数
                steamUgc.getQueryUgcStatistic(result.handle, i,
                    EItemStatistic.numComments, commentsNumber);
                // 获取订阅数
                steamUgc.getQueryUgcStatistic(result.handle, i,
                    EItemStatistic.numSubscriptions, subscribersNumber);
                // 获取收藏数
                steamUgc.getQueryUgcStatistic(result.handle, i,
                    EItemStatistic.numFavorites, favoritesNumber);
                
                // debugPrint('评论数: ${commentsNumber.value}, 订阅数: ${subscribersNumber.value}, 收藏数: ${favoritesNumber.value}');

                /// https://partner.steamgames.com/doc/api/ISteamUGC#GetQueryUGCResult
                final detail = arena<SteamUgcDetails>();
                steamUgc.getQueryUgcResult(result.handle, i, detail);
                final previewUrl = arena<Uint8>(255).cast<Utf8>();
                steamUgc.getQueryUgcPreviewUrl(
                  result.handle,
                  i,
                  previewUrl,
                  256,
                );
                debugPrint('$i previewUrl ${previewUrl.toDartString()}');

                int? version, levelCount;
                dynamic infos;

                /// get key value
                {
                  final kvNumbers =
                      steamUgc.getQueryUgcNumKeyValueTags(result.handle, i);
                  int? metaDataLength;
                  // debugPrint('kvNumbers $kvNumbers');
                  for (var tagIndex = 0; tagIndex < kvNumbers; tagIndex++) {
                    final key = arena<Uint8>(255).cast<Utf8>(),
                        value = arena<Uint8>(255).cast<Utf8>();
                    steamUgc.getQueryUgcKeyValueTag(
                      result.handle,
                      i,
                      tagIndex,
                      key,
                      50,
                      value,
                      100,
                    );
                    // debugPrint(
                    //   'key:${key.toDartString()},'
                    //   'value:${value.toDartString()}',
                    // );
                    if (key.toDartString() == 'metaDataLength') {
                      metaDataLength = int.parse(value.toDartString()) + 10;
                    }
                    if (key.toDartString() == 'levelCount') {
                      levelCount = int.parse(value.toDartString());
                    }
                  }
                  if (metaDataLength != null) {
                    final metaData =
                        arena<Uint8>(metaDataLength + 10).cast<Utf8>();
                    steamUgc.getQueryUgcMetadata(
                      result.handle,
                      i,
                      metaData,
                      metaDataLength,
                    );
                    final rawData = metaData.toDartString();
                    // debugPrint('metaData $rawData');
                    final base64DecodeData = base64Decode(rawData);
                    // debugPrint('base64Decode $base64DecodeData');
                    final gzipDecodeData = gzip.decode(base64DecodeData);
                    // debugPrint('gzip.decode $gzipDecodeData');
                    final utf8DecodeData =
                        utf8.decode(gzipDecodeData, allowMalformed: true);
                    // debugPrint('utf8.decode $utf8DecodeData');
                    try {
                      final data = jsonDecode(utf8DecodeData);
                      // debugPrint('data decode $data');
                      version = data['version'];
                      infos = data['infos'];
                    } catch (e) {}
                  }
                }

                final children = arena<Uint64>(detail.numChildren);
                steamUgc.getQueryUgcChildren(
                  query,
                  i,
                  children.cast<UnsignedLongLong>(),
                  detail.numChildren,
                );

                /// get tags
                var styles = <TagStyle>{};
                var shapes = <TagShape>{};
                TagAgeRating? ageRating;
                {
                  final tags = getSteamItemTags(arena, result.handle, i);
                  for (var tag in tags) {
                    if (TagStyles.containsKey(tag)) {
                      styles.add(TagStyles[tag]!);
                    } else if (TagShapes.containsKey(tag)) {
                      shapes.add(TagShapes[tag]!);
                    } else if (TagAgeRatings.containsKey(tag)) {
                      ageRating = TagAgeRatings[tag]!;
                    }
                  }
                }
                // debugPrint('data $data');
                details.add(SteamFile(
                  // type: type,
                  childrenId: List.generate(
                    detail.numChildren,
                    (i) => children[i],
                  ),
                  comments: commentsNumber.value,
                  publishTime: DateTime.fromMillisecondsSinceEpoch(
                      detail.timeCreated * 1000,
                      isUtc: true),
                  updateTime: DateTime.fromMillisecondsSinceEpoch(
                      detail.timeUpdated * 1000,
                      isUtc: true),
                  fileSize: detail.fileSize,
                  ageRating: ageRating,
                  styles: styles,
                  shapes: shapes,
                  id: detail.publishedFileId,
                  name: detail.title.toDartString(),
                  voteUp: detail.votesUp,
                  voteDown: detail.votesDown,
                  steamIdOwner: detail.steamIdOwner,
                  cover: previewUrl.toDartString(),
                  version: version ?? 1,
                  description: detail.description.toDartString(),
                  levelCount: levelCount,
                  subscribers: subscribersNumber.value, // 使用正确的订阅数
                  favorites: favoritesNumber.value, // 使用正确的收藏数
                ));
              });
            }
          }
          steamUgc.releaseQueryUgcRequest(result.handle);

          completer.complete(SteamFiles(
            result: result.result,
            current: result.numResultsReturned,
            total: result.totalMatchingResults,
            files: details,
          ));
          // debugPrint('details length ${details.length}');
        });
    return completer.future;
  }

  Future<SubmitResult> createItem({
    int? itemId,
    required ApiLanguage language,
    visibility = ERemoteStoragePublishedFileVisibility.public,
    String? title,
    String? description,
    String? contentFolder,
    String? previewImagePath,
    Set<(String, String)>? keyValue,
    String? metaData,
    String? updateNote,
    void Function(int handle)? onUpdate,
    Set<int>? childrenId,
    int? levelCount,
  }) async {
    final tempDir = await getTemporaryDirectory();
    itemId ??= await createItemReturnId();
    final completer = Completer<SubmitResult>();
    final handle = steamUgc.startItemUpdate(SteamGlobals.instance.appId, itemId);

    steamUgc.setItemVisibility(handle, visibility);
    steamUgc.setItemUpdateLanguage(handle, language.name.toNativeUtf8());


    final tag = calloc<SteamParamStringArray>();

    if (title != null) {
      steamUgc.setItemTitle(handle, title.toNativeUtf8());
    }
    if (description != null) {
      steamUgc.setItemDescription(handle, description.toNativeUtf8());
    }
    if (previewImagePath != null) {
      final file = File(path.join(tempDir.path, 'preview.png'));
      await file.writeAsBytes(await compressImage(File(previewImagePath)));
      // print('预览图片 ${file.path}');
      steamUgc.setItemPreview(handle, file.path.toNativeUtf8());
    }
    if (childrenId != null) {
      for (var id in childrenId) {
        steamUgc.addDependency(itemId, id);
      }
    }
    if (contentFolder != null) {
      steamUgc.setItemContent(handle, contentFolder.toNativeUtf8());
    }
    keyValue ??= {};
    if (levelCount != null) {
      keyValue.add(('levelCount', levelCount.toString()));
    }
    if (metaData != null) {
      metaData = base64Encode(gzip.encode(utf8.encode(metaData)));
      keyValue.add(('metaDataLength', metaData.length.toString()));
      // debugPrint('metadata $metaData');
      steamUgc.setItemMetadata(handle, metaData.toNativeUtf8());
    }

    /// 必须删除旧key，
    keyValue.map((e) => e.$1).toSet().forEach((key) {
      final res = steamUgc.removeItemKeyValueTags(handle, key.toNativeUtf8());
      debugPrint('remove kv $key $res');
    });
    for (var kv in keyValue) {
      final (key, value) = kv;
      steamUgc.addItemKeyValueTag(
        handle,
        key.toNativeUtf8(),
        value.toNativeUtf8(),
      );
    }

    onUpdate?.call(handle);
    registerCallResult<SubmitItemUpdateResult>(
      asyncCallId: steamUgc.submitItemUpdate(
        handle,
        (updateNote ?? '').toNativeUtf8(),
      ),
      cb: (result, hasFailed) {
        completer.complete(SubmitResult(
          result: result.result,
          publishedFileId: result.publishedFileId,
          userNeedsToAcceptWorkshopLegalAgreement:
              result.userNeedsToAcceptWorkshopLegalAgreement,
        ));
      },
    );
    return completer.future;
  }

  Future<List<String>> fetchPreviewUrls(SteamClient steamClient) async {
    List<String> previewUrls = [];

    // 获取所有项目
    final steamFiles = await steamClient.getAllItems(page: 1, sort: SteamUGCSort.publishTime);

    // 提取每个项目的 previewUrl
    for (var file in steamFiles.files) {
      previewUrls.add(file.cover); // 假设 cover 是预览 URL
    }

    return previewUrls;
  }

  Future<List<int>> getSubscribedItems() async {
    final numItems = steamUgc.getNumSubscribedItems();
    if (numItems == 0) return [];

    final itemsArray = calloc<UnsignedLongLong>(numItems);
    try {
      final actualCount = steamUgc.getSubscribedItems(itemsArray, numItems);
      return List.generate(actualCount, (i) => itemsArray[i]);
    } finally {
      calloc.free(itemsArray);
    }
  }

  /// 获取当前用户上传到创意工坊中点赞数最多的物品
  /// 返回点赞数最多的物品信息，如果没有上传物品则返回null
  Future<SteamFile?> getMostLikedPublishedItem() async {
    final completer = Completer<SteamFile?>();
    final appId = SteamGlobals.instance.appId;
    
    // 创建查询句柄，按点赞数降序排序
    final query = steamUgc.createQueryUserUgcRequest(
      userId, // 当前用户ID
      EUserUgcList.published, // 查询已发布的物品
      EUgcMatchingUgcType.usableInGame, // 类型为可在游戏中使用的物品
      EUserUgcListSortOrder.voteScoreDesc, // 按点赞数降序排序
      appId,
      appId,
      1, // 第一页
    );
    
    // 设置查询参数
    steamUgc.setReturnKeyValueTags(query, true);
    steamUgc.setReturnMetadata(query, true);
    steamUgc.setAllowCachedResponse(query, 0);
    steamUgc.setReturnLongDescription(query, true);
    steamUgc.setReturnChildren(query, true);
    
    // 发送查询请求
    registerCallResult<SteamUgcQueryCompleted>(
      asyncCallId: steamUgc.sendQueryUgcRequest(query),
      cb: (result, failed) {
        debugPrint('Query most liked item result ${{"result": result.result, "total": result.totalMatchingResults}}');
        
        SteamFile? mostLikedItem;
        
        if (result.result == EResult.eResultOK && result.numResultsReturned > 0) {
          // 只需处理第一个结果，因为已经按点赞数降序排序
          using((arena) {
            final commentsNumber = arena<UnsignedLongLong>();
            steamUgc.getQueryUgcStatistic(result.handle, 0, EItemStatistic.numComments, commentsNumber);
            
            final detail = arena<SteamUgcDetails>();
            steamUgc.getQueryUgcResult(result.handle, 0, detail);
            
            final previewUrl = arena<Uint8>(255).cast<Utf8>();
            steamUgc.getQueryUgcPreviewUrl(result.handle, 0, previewUrl, 256);
            
            int? version, levelCount;
            dynamic infos;
            
            // 获取键值对
            final kvNumbers = steamUgc.getQueryUgcNumKeyValueTags(result.handle, 0);
            int? metaDataLength;
            
            for (var tagIndex = 0; tagIndex < kvNumbers; tagIndex++) {
              final key = arena<Uint8>(255).cast<Utf8>(),
                  value = arena<Uint8>(255).cast<Utf8>();
              
              steamUgc.getQueryUgcKeyValueTag(
                result.handle,
                0,
                tagIndex,
                key,
                50,
                value,
                100,
              );
              
              if (key.toDartString() == 'metaDataLength') {
                metaDataLength = int.parse(value.toDartString()) + 10;
              }
              if (key.toDartString() == 'levelCount') {
                levelCount = int.parse(value.toDartString());
              }
            }
            
            if (metaDataLength != null) {
              final metaData = arena<Uint8>(metaDataLength + 10).cast<Utf8>();
              steamUgc.getQueryUgcMetadata(result.handle, 0, metaData, metaDataLength);
              
              final rawData = metaData.toDartString();
              try {
                final base64DecodeData = base64Decode(rawData);
                final gzipDecodeData = gzip.decode(base64DecodeData);
                final utf8DecodeData = utf8.decode(gzipDecodeData, allowMalformed: true);
                final data = jsonDecode(utf8DecodeData);
                version = data['version'];
                infos = data['infos'];
              } catch (e) {}
            }
            
            final children = arena<Uint64>(detail.numChildren);
            steamUgc.getQueryUgcChildren(
              query,
              0,
              children.cast<UnsignedLongLong>(),
              detail.numChildren,
            );
            
            var styles = <TagStyle>{};
            var shapes = <TagShape>{};
            
            mostLikedItem = SteamFile(
              childrenId: List.generate(detail.numChildren, (i) => children[i]),
              comments: commentsNumber.value,
              publishTime: DateTime.fromMillisecondsSinceEpoch(detail.timeCreated * 1000, isUtc: true),
              updateTime: DateTime.fromMillisecondsSinceEpoch(detail.timeUpdated * 1000, isUtc: true),
              fileSize: detail.fileSize,
              styles: styles,
              shapes: shapes,
              id: detail.publishedFileId,
              name: detail.title.toDartString(),
              voteUp: detail.votesUp,
              voteDown: detail.votesDown,
              steamIdOwner: detail.steamIdOwner,
              cover: previewUrl.toDartString(),
              version: version ?? 1,
              description: detail.description.toDartString(),
              levelCount: levelCount,
            );
          });
        }
        
        steamUgc.releaseQueryUgcRequest(result.handle);
        completer.complete(mostLikedItem);
      },
    );
    
    return completer.future;
  }
}
