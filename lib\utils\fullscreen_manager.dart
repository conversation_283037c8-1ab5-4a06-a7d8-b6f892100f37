import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:io';
import 'package:window_manager/window_manager.dart';
import 'package:fullscreen_window/fullscreen_window.dart';
import 'package:ve/main.dart';

class FullscreenManager extends ChangeNotifier {
  static final FullscreenManager _instance = FullscreenManager._internal();
  
  factory FullscreenManager() {
    return _instance;
  }
  
  FullscreenManager._internal() {
    _loadInitialFullscreenState();
  }
  
  bool _isFullscreen = false;
  
  bool get isFullscreen => _isFullscreen;
  
  Future<void> _loadInitialFullscreenState() async {
    final prefs = await SharedPreferences.getInstance();
    _isFullscreen = prefs.getBool('newWindowFullScreen') ?? false;
    notifyListeners();
  }
  
  void toggleFullscreen() {
    _isFullscreen = !_isFullscreen;
    _applyFullscreenSetting();
    
    // 同步全局状态
    _syncWithGlobalState();
    
    notifyListeners();
  }
  
  void setFullscreen(bool value) {
    if (_isFullscreen != value) {
      _isFullscreen = value;
      _applyFullscreenSetting();
      
      // 同步全局状态
      _syncWithGlobalState();
      
      notifyListeners();
    }
  }
  
  // 应用全屏设置
  Future<void> _applyFullscreenSetting() async {
    try {
      if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
        await windowManager.setFullScreen(_isFullscreen);
        
        // 如果退出全屏模式，确保标题栏可见
        if (!_isFullscreen) {
          await windowManager.setTitleBarStyle(TitleBarStyle.normal);
        }
        
        print('FullscreenManager: 使用window_manager设置全屏: $_isFullscreen');
      } else {
        await FullScreenWindow.setFullScreen(_isFullscreen);
        print('FullscreenManager: 使用fullscreen_window设置全屏: $_isFullscreen');
      }
      
      // 保存设置到SharedPreferences - 异步保存，不阻塞UI
      SharedPreferences.getInstance().then((prefs) {
        prefs.setBool('newWindowFullScreen', _isFullscreen);
      });
    } catch (e) {
      print('FullscreenManager: 设置全屏模式失败: $e');
    }
  }
  
  // 同步全屏状态到全局状态
  void _syncWithGlobalState() {
    try {
      // 尝试查找并同步到GameInstanceAppState
      final rootElement = WidgetsBinding.instance.rootElement;
      if (rootElement != null) {
        // 尝试同步到GameInstanceAppState (游戏实例窗口)
        final gameInstance = rootElement.findAncestorStateOfType<GameInstanceAppState>();
        if (gameInstance != null && gameInstance.mounted && gameInstance.isFullscreen != _isFullscreen) {
          gameInstance.fullscreen = _isFullscreen;
          print('FullscreenManager: 已同步全屏状态到GameInstanceAppState: $_isFullscreen');
        }
        
        // 尝试通过查找任何可见组件的context
        try {
          final navigatorState = rootElement.findAncestorStateOfType<NavigatorState>();
          if (navigatorState != null) {
            final context = navigatorState.context;
            try {
              // 尝试同步到AppStateContainer (主窗口)
              final appContainer = AppStateContainer.of(context);
              if (appContainer.isFullScreen != _isFullscreen) {
                appContainer.toggleFullScreen();
                print('FullscreenManager: 已同步全屏状态到AppStateContainer: $_isFullscreen');
              }
            } catch (e) {
              // 找不到AppStateContainer，这是正常的，可能是在游戏实例窗口中
              print('FullscreenManager: 当前上下文中没有找到AppStateContainer (可能是游戏窗口)');
            }
          }
        } catch (e) {
          print('FullscreenManager: 尝试查找NavigatorState失败: $e');
        }
      }
    } catch (e) {
      print('FullscreenManager: 同步全局状态失败: $e');
    }
  }
}