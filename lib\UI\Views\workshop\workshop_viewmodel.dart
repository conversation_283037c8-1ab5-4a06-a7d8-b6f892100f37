import 'package:stacked/stacked.dart';
import 'package:steamworks/steamworks.dart';
import '../../../utils/steam_file_ex.dart';
import '../../../utils/steam_ex.dart';
import '../../../utils/steam_tags.dart';
import 'package:shared_preferences/shared_preferences.dart';

enum SortBy { subscribers, publishDate, updateDate, likes, favorites }

class WorkshopItem {
  final String id;
  final String imageUrl;
  final String title;
  final String description;
  final int subscribers;
  final DateTime publishDate;
  final DateTime updateDate;
  final String cover;
  int likes;
  int favorites;
  bool isSubscribed;
  TagAgeRating? ageRating;
  Set<TagStyle> styles;
  Set<TagShape> shapes;

  WorkshopItem({
    required this.id,
    required this.imageUrl,
    required this.title,
    required this.description,
    required this.subscribers,
    required this.publishDate,
    required this.updateDate,
    required this.cover,
    required this.likes,
    required this.favorites,
    this.isSubscribed = false,
    this.ageRating,
    this.styles = const {},
    this.shapes = const {},
  });
}

Future<List<WorkshopItem>> fetchPreviewUrls(SteamClient steamClient) async {
  List<WorkshopItem> workshopItems = [];

  // 获取所有项目
  final steamFiles = await steamClient.getAllItems(page: 1, sort: SteamUGCSort.publishTime);
  
  // 获取已订阅的项目ID列表
  final subscribedItems = await steamClient.getSubscribedItems();
  final subscribedItemsSet = Set<int>.from(subscribedItems);

  // 处理每个项目，包括订阅状态
  for (var file in steamFiles.files) {
      final coverUrl = file.cover.toString();
      final title = file.name.toString();
      final description = file.description?.toString() ?? '';
      final isSubscribed = subscribedItemsSet.contains(file.id);
      print('file.cover type: ${coverUrl.runtimeType}, value: $coverUrl');
      
      // 获取标签信息
      TagAgeRating? ageRating = file.ageRating;
      Set<TagStyle> styles = file.styles;
      Set<TagShape> shapes = file.shapes;
      
      workshopItems.add(WorkshopItem(
        id: file.id.toString(),
        imageUrl: coverUrl, // 使用 cover 作为 imageUrl
        title: title, // 使用 name 作为 title
        description: description, // 使用 file.description ?? '' 作为 description
        subscribers: file.subscribers, // 使用正确的订阅数
        publishDate: file.publishTime,
        updateDate: file.updateTime,
        cover: coverUrl, // 使用 cover 作为 cover
        likes: file.voteUp, // 使用voteUp作为点赞数
        favorites: file.favorites, // 使用正确的收藏数
        isSubscribed: isSubscribed,
        ageRating: ageRating,
        styles: styles,
        shapes: shapes,
      ));
  }

  return workshopItems;
}

class WorkshopViewModel extends BaseViewModel {
  List<WorkshopItem> previewUrls = [];
  List<WorkshopItem> filteredWorkshops = [];
  
  TagAgeRating? ageRating;
  TagStyle? style;
  TagShape? shape;
  TagSubscriptionStatus? subscriptionStatus;
  SortBy? sortBy;
  bool sortAscending = false;
  bool isPublicMode = false;

  WorkshopViewModel() {
    _initPublicMode();
    loadPreviewUrls();
  }

  Future<void> _initPublicMode() async {
    final prefs = await SharedPreferences.getInstance();
    isPublicMode = prefs.getBool('isPublicMode') ?? false;
    if (isPublicMode) {
      ageRating = TagAgeRating.everyone;
    }
    notifyListeners();
  }

  void toggleSortDirection() {
    sortAscending = !sortAscending;
    _applyFilters();
  }

  Future<void> loadPreviewUrls() async {
    final steamClient = SteamClient.instance;
    
    previewUrls = await fetchPreviewUrls(steamClient);
    filteredWorkshops = List.from(previewUrls);
    notifyListeners();
  }

  void setAgeRating(TagAgeRating? rating) {
    if (isPublicMode && rating == TagAgeRating.nsfw) {
      return;
    }
    
    ageRating = rating;
    if (isPublicMode && rating == null) {
      ageRating = TagAgeRating.everyone;
    }
    _applyFilters();
  }

  void setStyle(TagStyle? style) {
    this.style = style;
    _applyFilters();
  }

  void setShape(TagShape? shape) {
    this.shape = shape;
    _applyFilters();
  }

  void filterWorkshops(String query) {
    _applyFilters(query: query);
  }

  void setSubscriptionStatus(TagSubscriptionStatus? status) {
    subscriptionStatus = status;
    _applyFilters();
  }

  void setSortBy(SortBy? sortBy) {
    this.sortBy = sortBy;
    _applyFilters();
  }

  void _applyFilters({String? query}) {
    var filtered = previewUrls.where((item) {
      if (isPublicMode && item.ageRating == TagAgeRating.nsfw) {
        return false;
      }
      
      if (subscriptionStatus != null) {
        switch (subscriptionStatus!) {
          case TagSubscriptionStatus.subscribed:
            if (!item.isSubscribed) return false;
            break;
          case TagSubscriptionStatus.unsubscribed:
            if (item.isSubscribed) return false;
            break;
        }
      }
      
      if (ageRating != null && item.ageRating != null) {
        if (item.ageRating != ageRating) {
          return false;
        }
      }
      
      if (style != null) {
        if (!item.styles.contains(style)) {
          return false;
        }
      }
      
      if (shape != null) {
        if (!item.shapes.contains(shape)) {
          return false;
        }
      }
      
      if (query != null && query.isNotEmpty) {
        final lowerQuery = query.toLowerCase();
        return item.title.toLowerCase().contains(lowerQuery) ||
            item.description.toLowerCase().contains(lowerQuery);
      }
      return true;
    }).toList();

    if (sortBy != null) {
      switch (sortBy!) {
        case SortBy.subscribers:
          filtered.sort((a, b) => sortAscending 
            ? a.subscribers.compareTo(b.subscribers)
            : b.subscribers.compareTo(a.subscribers));
          break;
        case SortBy.publishDate:
          filtered.sort((a, b) => sortAscending
            ? a.publishDate.compareTo(b.publishDate)
            : b.publishDate.compareTo(a.publishDate));
          break;
        case SortBy.updateDate:
          filtered.sort((a, b) => sortAscending
            ? a.updateDate.compareTo(b.updateDate)
            : b.updateDate.compareTo(a.updateDate));
          break;
        case SortBy.likes:
          filtered.sort((a, b) => sortAscending
            ? a.likes.compareTo(b.likes)
            : b.likes.compareTo(a.likes));
          break;
        case SortBy.favorites:
          filtered.sort((a, b) => sortAscending
            ? a.favorites.compareTo(b.favorites)
            : b.favorites.compareTo(a.favorites));
          break;
      }
    }

    filteredWorkshops = filtered;
    notifyListeners();
  }
}
